#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
سكريبت لبناء الملفات التنفيذية للبرنامج
"""

import os
import subprocess
import shutil
import sys

def run_command(command):
    """
    تنفيذ أمر وعرض مخرجاته في الوقت الفعلي
    
    المعاملات:
        command (str): الأمر المراد تنفيذه
        
    العائد:
        bool: True إذا تم تنفيذ الأمر بنجاح، False في حالة حدوث خطأ
    """
    print(f"جاري تنفيذ: {command}")
    process = subprocess.Popen(
        command,
        shell=True,
        stdout=subprocess.PIPE,
        stderr=subprocess.STDOUT,
        universal_newlines=True
    )
    
    # عرض المخرجات في الوقت الفعلي
    for line in process.stdout:
        print(line, end='')
    
    # انتظار انتهاء العملية
    process.wait()
    
    # التحقق من نجاح تنفيذ الأمر
    if process.returncode != 0:
        print(f"حدث خطأ أثناء تنفيذ الأمر. رمز الخروج: {process.returncode}")
        return False
    
    return True

def build_main_executable():
    """
    بناء الملف التنفيذي للبرنامج الرئيسي
    
    العائد:
        bool: True إذا تم البناء بنجاح، False في حالة حدوث خطأ
    """
    print("\n=== جاري بناء البرنامج الرئيسي ===\n")
    
    # بناء الملف التنفيذي
    if not run_command("pyinstaller diamond_sales.spec"):
        return False
    
    print("\nتم بناء البرنامج الرئيسي بنجاح.")
    return True

def build_activation_generator():
    """
    بناء الملف التنفيذي لمولد أكواد التفعيل
    
    العائد:
        bool: True إذا تم البناء بنجاح، False في حالة حدوث خطأ
    """
    print("\n=== جاري بناء مولد أكواد التفعيل ===\n")
    
    # بناء الملف التنفيذي
    if not run_command("pyinstaller activation_generator.spec"):
        return False
    
    print("\nتم بناء مولد أكواد التفعيل بنجاح.")
    return True

def create_distribution_folder():
    """
    إنشاء مجلد للتوزيع يحتوي على الملفات التنفيذية المبنية
    
    العائد:
        bool: True إذا تم الإنشاء بنجاح، False في حالة حدوث خطأ
    """
    print("\n=== جاري إنشاء مجلد التوزيع ===\n")
    
    # إنشاء مجلد التوزيع إذا لم يكن موجوداً
    if not os.path.exists("dist_package"):
        os.makedirs("dist_package")
    
    # نسخ الملفات التنفيذية إلى مجلد التوزيع
    try:
        # نسخ البرنامج الرئيسي
        if os.path.exists("dist_package/DiamondSales"):
            shutil.rmtree("dist_package/DiamondSales")
        shutil.copytree("dist/DiamondSales", "dist_package/DiamondSales")
        
        # نسخ مولد أكواد التفعيل
        if os.path.exists("dist_package/ActivationGenerator"):
            shutil.rmtree("dist_package/ActivationGenerator")
        shutil.copytree("dist/ActivationGenerator", "dist_package/ActivationGenerator")
        
        print("\nتم إنشاء مجلد التوزيع بنجاح.")
        return True
    except Exception as e:
        print(f"حدث خطأ أثناء إنشاء مجلد التوزيع: {str(e)}")
        return False

def main():
    """
    الدالة الرئيسية
    """
    print("=== بدء عملية بناء الملفات التنفيذية ===\n")
    
    # بناء البرنامج الرئيسي
    if not build_main_executable():
        print("حدث خطأ أثناء بناء البرنامج الرئيسي.")
        return
    
    # بناء مولد أكواد التفعيل
    if not build_activation_generator():
        print("حدث خطأ أثناء بناء مولد أكواد التفعيل.")
        return
    
    # إنشاء مجلد التوزيع
    if not create_distribution_folder():
        print("حدث خطأ أثناء إنشاء مجلد التوزيع.")
        return
    
    print("\n=== تم الانتهاء من عملية البناء بنجاح ===")
    print("الملفات التنفيذية موجودة في مجلد 'dist_package'.")

if __name__ == "__main__":
    main()
