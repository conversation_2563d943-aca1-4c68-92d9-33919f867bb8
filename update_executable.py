# -*- coding: utf-8 -*-

"""
سكريبت لتحديث النسخة التنفيذية مع آخر التحديثات
"""

import os
import shutil
import subprocess
import sys
from datetime import datetime

def update_installer_package():
    """تحديث حزمة المثبت مع آخر التحديثات"""
    print("🔄 تحديث حزمة المثبت...")
    
    # التحقق من وجود مجلد dist
    if not os.path.exists("dist/DiamondSales"):
        print("  ❌ مجلد dist/DiamondSales غير موجود")
        print("  💡 يجب تشغيل build_exe.py أولاً")
        return False
    
    # إنشاء مجلد installer_package جديد
    package_dir = "installer_package"
    if os.path.exists(package_dir):
        print("  🗑️ حذف المجلد القديم...")
        shutil.rmtree(package_dir)
    
    os.makedirs(package_dir)
    print("  ✅ تم إنشاء مجلد جديد")
    
    # نسخ مجلد DiamondSales من dist
    print("  📁 نسخ ملفات البرنامج...")
    shutil.copytree("dist/DiamondSales", os.path.join(package_dir, "DiamondSales"))
    
    # إعادة تسمية الملف التنفيذي إذا كان يحتوي على مسافة
    old_exe_path = os.path.join(package_dir, "DiamondSales", "Diamond Sales.exe")
    new_exe_path = os.path.join(package_dir, "DiamondSales", "DiamondSales.exe")
    if os.path.exists(old_exe_path):
        os.rename(old_exe_path, new_exe_path)
        print("  ✅ تم إعادة تسمية الملف التنفيذي")
    
    # نسخ مجلد assets
    if os.path.exists("assets"):
        shutil.copytree("assets", os.path.join(package_dir, "assets"))
        print("  ✅ تم نسخ مجلد الأصول")
    
    # إنشاء سكريبت Inno Setup محدث
    installer_script = '''[Setup]
AppName=Diamond Sales
AppVersion=1.30
AppPublisher=Diamond Sales Company
DefaultDirName={autopf}\\Diamond Sales
DefaultGroupName=Diamond Sales
OutputDir=.
OutputBaseFilename=DiamondSalesSetup_v1.30
SetupIconFile=assets\\diamond_icon.ico
Compression=lzma
SolidCompression=yes
WizardStyle=modern
ArchitecturesAllowed=x64
ArchitecturesInstallIn64BitMode=x64

[Languages]
Name: "arabic"; MessagesFile: "compiler:Languages\\Arabic.isl"
Name: "english"; MessagesFile: "compiler:Default.isl"

[Tasks]
Name: "desktopicon"; Description: "{cm:CreateDesktopIcon}"; GroupDescription: "{cm:AdditionalIcons}"; Flags: unchecked

[Files]
Source: "DiamondSales\\*"; DestDir: "{app}"; Flags: ignoreversion recursesubdirs createallsubdirs

[Icons]
Name: "{group}\\Diamond Sales"; Filename: "{app}\\DiamondSales.exe"
Name: "{group}\\{cm:UninstallProgram,Diamond Sales}"; Filename: "{uninstallexe}"
Name: "{autodesktop}\\Diamond Sales"; Filename: "{app}\\DiamondSales.exe"; Tasks: desktopicon

[Run]
Filename: "{app}\\DiamondSales.exe"; Description: "{cm:LaunchProgram,Diamond Sales}"; Flags: nowait postinstall skipifsilent
'''
    
    # حفظ سكريبت المثبت
    script_path = os.path.join(package_dir, "DiamondSales_Installer.iss")
    with open(script_path, "w", encoding="utf-8") as f:
        f.write(installer_script)
    print("  ✅ تم إنشاء سكريبت المثبت")
    
    # إنشاء سكريبت batch لبناء المثبت
    batch_script = '''@echo off
chcp 65001 > nul
echo ===============================================
echo          إنشاء مثبت Diamond Sales v1.30
echo ===============================================
echo.

echo 🔍 البحث عن Inno Setup Compiler...

set "ISCC_PATH="

if exist "C:\\Program Files (x86)\\Inno Setup 6\\ISCC.exe" (
    set "ISCC_PATH=C:\\Program Files (x86)\\Inno Setup 6\\ISCC.exe"
    echo ✅ تم العثور على Inno Setup 6 (32-bit)
) else if exist "C:\\Program Files\\Inno Setup 6\\ISCC.exe" (
    set "ISCC_PATH=C:\\Program Files\\Inno Setup 6\\ISCC.exe"
    echo ✅ تم العثور على Inno Setup 6 (64-bit)
) else if exist "C:\\Program Files (x86)\\Inno Setup 5\\ISCC.exe" (
    set "ISCC_PATH=C:\\Program Files (x86)\\Inno Setup 5\\ISCC.exe"
    echo ✅ تم العثور على Inno Setup 5 (32-bit)
) else if exist "C:\\Program Files\\Inno Setup 5\\ISCC.exe" (
    set "ISCC_PATH=C:\\Program Files\\Inno Setup 5\\ISCC.exe"
    echo ✅ تم العثور على Inno Setup 5 (64-bit)
) else (
    echo ❌ لم يتم العثور على Inno Setup Compiler
    echo.
    echo 💡 يرجى تحميل وتثبيت Inno Setup من:
    echo    https://jrsoftware.org/isinfo.php
    echo.
    pause
    exit /b 1
)

echo.
echo 🔨 بناء المثبت...
echo.

"%ISCC_PATH%" "DiamondSales_Installer.iss"

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✅ تم إنشاء المثبت بنجاح!
    echo 📁 الملف: DiamondSalesSetup_v1.30.exe
    echo 📏 الحجم التقريبي: 50-100 MB
    echo.
    echo 💡 يمكنك الآن توزيع هذا الملف للمستخدمين
) else (
    echo.
    echo ❌ فشل في إنشاء المثبت
    echo 💡 تحقق من رسائل الخطأ أعلاه
)

echo.
pause
'''
    
    batch_path = os.path.join(package_dir, "create_installer.bat")
    with open(batch_path, "w", encoding="utf-8") as f:
        f.write(batch_script)
    print("  ✅ تم إنشاء سكريبت البناء")
    
    # إنشاء ملف README
    readme_content = f'''# مثبت Diamond Sales v1.30

## نظرة عامة
هذا المجلد يحتوي على جميع الملفات اللازمة لإنشاء مثبت برنامج Diamond Sales الإصدار 1.30.

## محتويات المجلد
- `DiamondSales/` - مجلد البرنامج الكامل
- `assets/` - الأيقونات والصور
- `DiamondSales_Installer.iss` - سكريبت Inno Setup
- `create_installer.bat` - سكريبت إنشاء المثبت التلقائي
- `README_INSTALLER.md` - هذا الملف

## خطوات إنشاء المثبت

### الطريقة الأولى: التلقائية (مستحسنة)
1. تأكد من تثبيت Inno Setup على جهازك
2. انقر نقراً مزدوجاً على `create_installer.bat`
3. انتظر حتى انتهاء العملية

### الطريقة الثانية: اليدوية
1. ثبت Inno Setup من: https://jrsoftware.org/isinfo.php
2. افتح Inno Setup Compiler
3. افتح ملف `DiamondSales_Installer.iss`
4. اضغط F9 أو Build > Compile

## الملف النهائي
- الاسم: `DiamondSalesSetup_v1.30.exe`
- الحجم التقريبي: 50-100 MB
- يعمل على Windows 64-bit

## الميزات
- تثبيت تلقائي في Program Files
- إنشاء اختصارات في قائمة البداية
- خيار إنشاء اختصار على سطح المكتب
- إلغاء تثبيت نظيف
- دعم اللغتين العربية والإنجليزية

## الحزم المستقلة المتوفرة
يمكنك أيضاً العثور على الحزم المستقلة في مجلد `standalone_packages/`:
- Windows 64-bit
- Windows 32-bit (إذا متوفر)
- حزمة محمولة

## استكشاف الأخطاء
- تأكد من تثبيت Inno Setup
- تأكد من وجود جميع الملفات في المجلد
- شغل الأوامر كمدير إذا لزم الأمر

## الدعم
للدعم الفني، يرجى التواصل مع فريق Diamond Sales.

---
📅 تاريخ التحديث: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
🔢 الإصدار: 1.30
'''
    
    readme_path = os.path.join(package_dir, "README_INSTALLER.md")
    with open(readme_path, "w", encoding="utf-8") as f:
        f.write(readme_content)
    print("  ✅ تم إنشاء ملف README")
    
    print(f"\n🎉 تم تحديث حزمة المثبت بنجاح!")
    print(f"📁 المجلد: {package_dir}/")
    print("📋 لإنشاء المثبت: شغل create_installer.bat")
    
    return True

def main():
    """الدالة الرئيسية"""
    print("=" * 60)
    print("🔄 تحديث النسخة التنفيذية - Diamond Sales v1.30")
    print("=" * 60)
    
    if update_installer_package():
        print("\n✅ تم التحديث بنجاح!")
        print("📁 تحقق من مجلد: installer_package/")
        print("🚀 شغل create_installer.bat لإنشاء المثبت")
    else:
        print("\n❌ فشل في التحديث")
    
    print("\n" + "=" * 60)

if __name__ == "__main__":
    main()