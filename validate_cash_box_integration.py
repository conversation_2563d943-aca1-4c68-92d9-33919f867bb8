from database import صندوق_النقدية, حركة_نقدية, Receipt
from db_session import session_scope
from datetime import datetime
import traceback

def check_cash_box():
    try:
        print("Checking Cash Box Status...")
        
        with session_scope() as session:
            # Check if cash box exists
            cash_box = session.query(صندوق_النقدية).first()
            if cash_box:
                print(f"Cash Box Found: ID={cash_box.id}, Balance=${cash_box.balance:.2f}, Last Updated: {cash_box.last_updated}")
            else:
                print("No Cash Box found in database")
                
            # Count transactions
            transaction_count = session.query(حركة_نقدية).count()
            print(f"Total Cash Transactions: {transaction_count}")
            
            # Get recent transactions
            transactions = session.query(حركة_نقدية).order_by(حركة_نقدية.id.desc()).limit(5).all()
            if transactions:
                print("\nRecent Transactions:")
                for t in transactions:
                    print(f"ID: {t.id}, Type: {t.transaction_type}, Amount: ${t.amount:.2f}, Balance After: ${t.balance_after:.2f}")
                    print(f"  Reference: {t.reference}")
                    print(f"  Description: {t.description}")
                    print(f"  Date: {t.transaction_date}")
                    print("-" * 50)
            else:
                print("\nNo transactions found")
                
            # Check receipt vouchers with cash transactions
            receipts = session.query(Receipt).order_by(Receipt.id.desc()).limit(5).all()
            if receipts:
                print("\nRecent Receipt Vouchers:")
                for r in receipts:
                    print(f"ID: {r.id}, Type: {r.receipt_type}, Amount: ${r.amount_usd:.2f}")
                    
                    # Find related cash transactions
                    related_transactions = session.query(حركة_نقدية).filter(
                        حركة_نقدية.reference.like(f"%سند {r.receipt_type} رقم {r.id}%")
                    ).all()
                    
                    if related_transactions:
                        print(f"  Related Cash Transactions: {len(related_transactions)}")
                        for t in related_transactions:
                            print(f"    Transaction ID: {t.id}, Type: {t.transaction_type}, Amount: ${t.amount:.2f}")
                    else:
                        print("  No Related Cash Transactions Found")
                    print("-" * 50)
            else:
                print("\nNo receipt vouchers found")
    except Exception as e:
        print(f"Error occurred: {str(e)}")
        traceback.print_exc()

if __name__ == "__main__":
    check_cash_box()
    print("Script completed")  # Add this to ensure we see some output
