#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
اختبار بسيط لتقارير السندات
"""

import sys
import os

# إضافة المسار
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from db_session import get_session
    from database import *
    print("تم استيراد database بنجاح")
    
    # إنشاء جلسة
    session = get_session()
    print("تم إنشاء جلسة قاعدة البيانات")
    
    # فحص السندات
    receipts_count = session.query(Receipt).count()
    print(f"عدد السندات في قاعدة البيانات: {receipts_count}")
    
    # فحص العملاء
    customers_count = session.query(Customer).count()
    print(f"عدد العملاء في قاعدة البيانات: {customers_count}")
    
    # فحص الموردين
    suppliers_count = session.query(Supplier).count()
    print(f"عدد الموردين في قاعدة البيانات: {suppliers_count}")
    
    # اختبار دالة طباعة جميع العملاء
    print("\n--- اختبار دالة طباعة جميع العملاء ---")
    try:
        from reports_screen import ReportsScreen
        from PyQt6.QtWidgets import QApplication
        import sys
        
        # إنشاء QApplication
        app = QApplication(sys.argv)
        
        # إنشاء مستخدم وهمي للاختبار
        class MockUser:
            def __init__(self):
                self.id = 1
                self.username = "test_user"
        
        mock_user = MockUser()
        reports = ReportsScreen(mock_user)
        
        # اختبار دالة generate_all_customers_summary
        print("اختبار generate_all_customers_summary...")
        result = reports.generate_all_customers_summary()
        print(f"نتيجة الاختبار: {result}")
        
        # اختبار دالة generate_all_suppliers_summary
        print("اختبار generate_all_suppliers_summary...")
        result = reports.generate_all_suppliers_summary()
        print(f"نتيجة الاختبار: {result}")
        
    except Exception as e:
        print(f"خطأ في اختبار دوال التقارير: {str(e)}")
        import traceback
        traceback.print_exc()
    
    session.close()
    print("تم إغلاق الجلسة بنجاح")
    

    
except Exception as e:
    print(f"خطأ في الاختبار: {e}")
    import traceback
    traceback.print_exc()

print("انتهى الاختبار")
