#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
هذا الملف يقوم بتحديث هيكل جدول التفعيل في قاعدة البيانات
"""

import sqlite3
import os
import datetime
from logger import log_error, log_info

def backup_database():
    """إنشاء نسخة احتياطية من قاعدة البيانات قبل التعديل"""
    try:
        # تأكد من وجود مجلد النسخ الاحتياطية
        if not os.path.exists('backups'):
            os.makedirs('backups')
            
        # إنشاء نسخة احتياطية باسم يحتوي على التاريخ والوقت الحالي
        timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_path = f'backups/diamond_sales_backup_{timestamp}.db'
        
        # نسخ ملف قاعدة البيانات
        source = 'diamond_sales.db'
        if os.path.exists(source):
            import shutil
            shutil.copy2(source, backup_path)
            print(f"تم إنشاء نسخة احتياطية من قاعدة البيانات: {backup_path}")
            log_info(f"تم إنشاء نسخة احتياطية من قاعدة البيانات: {backup_path}")
            return True
        else:
            print("لم يتم العثور على ملف قاعدة البيانات للنسخ الاحتياطي.")
            log_error("لم يتم العثور على ملف قاعدة البيانات للنسخ الاحتياطي.")
            return False
    except Exception as e:
        print(f"حدث خطأ أثناء إنشاء النسخة الاحتياطية: {str(e)}")
        log_error(f"حدث خطأ أثناء إنشاء النسخة الاحتياطية: {str(e)}")
        return False

def update_activation_table():
    """تحديث هيكل جدول التفعيل في قاعدة البيانات"""
    try:
        print("بدء عملية تحديث جدول التفعيل...")
        log_info("بدء عملية تحديث جدول التفعيل...")
        
        # الاتصال بقاعدة البيانات
        conn = sqlite3.connect('diamond_sales.db')
        cursor = conn.cursor()
        
        # التحقق من وجود جدول التفعيل
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='activation'")
        if not cursor.fetchone():
            print("جدول التفعيل غير موجود. سيتم إنشاؤه تلقائيًا عند تشغيل البرنامج.")
            log_info("جدول التفعيل غير موجود. سيتم إنشاؤه تلقائيًا عند تشغيل البرنامج.")
            conn.close()
            return True
        
        # التحقق من وجود الأعمدة الجديدة
        cursor.execute("PRAGMA table_info(activation)")
        columns = [column[1] for column in cursor.fetchall()]
        
        # إضافة الأعمدة الجديدة إذا لم تكن موجودة
        columns_to_add = {
            'expiry_date': 'TIMESTAMP',
            'features': 'TEXT',
            'default_expiry_days': 'INTEGER DEFAULT 365',
            'notify_before_days': 'INTEGER DEFAULT 30'
        }
        
        for column_name, column_type in columns_to_add.items():
            if column_name not in columns:
                print(f"إضافة العمود {column_name} إلى جدول التفعيل...")
                log_info(f"إضافة العمود {column_name} إلى جدول التفعيل...")
                cursor.execute(f"ALTER TABLE activation ADD COLUMN {column_name} {column_type}")
        
        # تحديث قيم الأعمدة الجديدة للسجلات الموجودة
        cursor.execute("SELECT id, activation_code, hardware_id FROM activation")
        activations = cursor.fetchall()
        
        if activations:
            from activation import validate_activation_code
            
            for activation_id, activation_code, hardware_id in activations:
                # التحقق من كود التفعيل واستخراج معلوماته
                activation_info = validate_activation_code(activation_code, hardware_id)
                
                if activation_info:
                    # تحويل قائمة الميزات إلى سلسلة نصية
                    features_str = ",".join(activation_info['features']) if activation_info.get('features') else ""
                    
                    # تحديث السجل
                    cursor.execute(
                        "UPDATE activation SET expiry_date = ?, features = ? WHERE id = ?",
                        (activation_info['expiry_date'].isoformat(), features_str, activation_id)
                    )
                    
                    print(f"تم تحديث معلومات التفعيل للسجل رقم {activation_id}")
                    log_info(f"تم تحديث معلومات التفعيل للسجل رقم {activation_id}")
        
        # حفظ التغييرات
        conn.commit()
        conn.close()
        
        print("تم تحديث جدول التفعيل بنجاح.")
        log_info("تم تحديث جدول التفعيل بنجاح.")
        return True
    except Exception as e:
        print(f"حدث خطأ أثناء تحديث جدول التفعيل: {str(e)}")
        log_error(f"حدث خطأ أثناء تحديث جدول التفعيل: {str(e)}")
        return False

if __name__ == "__main__":
    # إنشاء نسخة احتياطية قبل التعديل
    backup_success = backup_database()
    if backup_success:
        print("تم إنشاء نسخة احتياطية بنجاح.")
    
    # تحديث جدول التفعيل
    update_success = update_activation_table()
    if update_success:
        print("تم تحديث جدول التفعيل بنجاح.")
    else:
        print("فشلت عملية تحديث جدول التفعيل.")
