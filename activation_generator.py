"""
برنامج توليد أكواد التفعيل
يستخدم هذا البرنامج لتوليد أكواد تفعيل للبرنامج الرئيسي
"""

import sys
import os
import datetime
from PyQt6.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout,
                            QLabel, QLineEdit, QPushButton, QSpinBox, QComboBox,
                            QCheckBox, QGroupBox, QWidget, QMessageBox, QTextEdit,
                            QFileDialog, QGridLayout)
from PyQt6.QtGui import QFont, QIcon
from PyQt6.QtCore import Qt

# استيراد وحدة التفعيل
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from activation import generate_activation_code
from database import Activation
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

class ActivationGeneratorWindow(QMainWindow):
    def __init__(self):
        super().__init__()

        # Obtener la duración predeterminada de activación de la base de datos
        self.default_expiry_days = 365  # Valor predeterminado
        try:
            # Conectar a la base de datos
            engine = create_engine('sqlite:///diamond_sales.db')
            Session = sessionmaker(bind=engine)
            session = Session()

            # Obtener la configuración de activación
            activation = session.query(Activation).first()
            if activation and activation.default_expiry_days:
                self.default_expiry_days = activation.default_expiry_days

            session.close()
        except Exception as e:
            print(f"Error al obtener la duración predeterminada de activación: {str(e)}")

        self.init_ui()

    def init_ui(self):
        """
        تهيئة واجهة المستخدم
        """
        self.setWindowTitle("مولد أكواد التفعيل - نظام إدارة مبيعات الألماس")
        self.setGeometry(100, 100, 800, 600)
        self.setLayoutDirection(Qt.LayoutDirection.RightToLeft)

        # إنشاء الويدجت المركزي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # إنشاء التخطيط الرئيسي
        main_layout = QVBoxLayout(central_widget)

        # إضافة عنوان الشاشة
        title_label = QLabel("مولد أكواد التفعيل")
        title_font = QFont()
        title_font.setPointSize(16)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        main_layout.addWidget(title_label)

        # إضافة مجموعة معلومات الجهاز
        hardware_group = QGroupBox("معلومات الجهاز")
        hardware_layout = QGridLayout()

        # إضافة معرف الجهاز
        hardware_id_label = QLabel("معرف الجهاز:")
        self.hardware_id = QLineEdit()
        self.hardware_id.setPlaceholderText("أدخل معرف الجهاز هنا...")

        hardware_layout.addWidget(hardware_id_label, 0, 0)
        hardware_layout.addWidget(self.hardware_id, 0, 1)

        hardware_group.setLayout(hardware_layout)
        main_layout.addWidget(hardware_group)

        # إضافة مجموعة إعدادات التفعيل
        settings_group = QGroupBox("إعدادات التفعيل")
        settings_layout = QGridLayout()

        # إضافة مدة الصلاحية
        expiry_label = QLabel("مدة الصلاحية (بالأيام):")
        self.expiry_days = QSpinBox()
        self.expiry_days.setRange(1, 3650)  # من يوم إلى 10 سنوات
        self.expiry_days.setValue(self.default_expiry_days)  # استخدام القيمة من قاعدة البيانات

        settings_layout.addWidget(expiry_label, 0, 0)
        settings_layout.addWidget(self.expiry_days, 0, 1)

        # إضافة الميزات
        features_label = QLabel("الميزات المفعلة:")
        features_layout = QVBoxLayout()

        self.feature_basic = QCheckBox("الميزات الأساسية")
        self.feature_basic.setChecked(True)
        self.feature_basic.setEnabled(False)  # لا يمكن إلغاء تحديد الميزات الأساسية

        self.feature_advanced = QCheckBox("الميزات المتقدمة")
        self.feature_advanced.setChecked(True)

        self.feature_reports = QCheckBox("التقارير المتقدمة")
        self.feature_reports.setChecked(True)

        self.feature_backup = QCheckBox("النسخ الاحتياطي التلقائي")
        self.feature_backup.setChecked(True)

        features_layout.addWidget(self.feature_basic)
        features_layout.addWidget(self.feature_advanced)
        features_layout.addWidget(self.feature_reports)
        features_layout.addWidget(self.feature_backup)

        features_widget = QWidget()
        features_widget.setLayout(features_layout)

        settings_layout.addWidget(features_label, 1, 0)
        settings_layout.addWidget(features_widget, 1, 1)

        settings_group.setLayout(settings_layout)
        main_layout.addWidget(settings_group)

        # إضافة مجموعة توليد الكود
        generate_group = QGroupBox("توليد كود التفعيل")
        generate_layout = QVBoxLayout()

        # إضافة زر توليد الكود
        generate_button = QPushButton("توليد كود التفعيل")
        generate_button.clicked.connect(self.generate_code)
        generate_layout.addWidget(generate_button)

        # إضافة حقل كود التفعيل
        self.activation_code = QTextEdit()
        self.activation_code.setReadOnly(True)
        self.activation_code.setPlaceholderText("سيظهر كود التفعيل هنا...")
        generate_layout.addWidget(self.activation_code)

        # إضافة أزرار النسخ والحفظ
        buttons_layout = QHBoxLayout()

        copy_button = QPushButton("نسخ الكود")
        copy_button.clicked.connect(self.copy_code)

        save_button = QPushButton("حفظ الكود في ملف")
        save_button.clicked.connect(self.save_code)

        buttons_layout.addWidget(copy_button)
        buttons_layout.addWidget(save_button)

        generate_layout.addLayout(buttons_layout)

        generate_group.setLayout(generate_layout)
        main_layout.addWidget(generate_group)

    def generate_code(self):
        """
        توليد كود التفعيل
        """
        hardware_id = self.hardware_id.text().strip()

        if not hardware_id:
            QMessageBox.warning(self, "تحذير", "الرجاء إدخال معرف الجهاز")
            return

        # التحقق من صحة معرف الجهاز
        if len(hardware_id) != 16 or not hardware_id.isupper() or not all(c.isalnum() for c in hardware_id):
            QMessageBox.warning(self, "تحذير", "معرف الجهاز غير صالح. يجب أن يكون 16 حرفًا من الأحرف الكبيرة والأرقام")
            return

        # جمع الميزات المفعلة
        features = ["basic"]

        if self.feature_advanced.isChecked():
            features.append("advanced")

        if self.feature_reports.isChecked():
            features.append("reports")

        if self.feature_backup.isChecked():
            features.append("backup")

        # توليد كود التفعيل
        expiry_days = self.expiry_days.value()
        activation_code = generate_activation_code(hardware_id, expiry_days, features)

        if activation_code:
            self.activation_code.setText(activation_code)

            # عرض معلومات التفعيل
            expiry_date = (datetime.datetime.now() + datetime.timedelta(days=expiry_days)).strftime("%Y-%m-%d")

            info_message = f"تم توليد كود التفعيل بنجاح\n\n"
            info_message += f"معرف الجهاز: {hardware_id}\n"
            info_message += f"تاريخ انتهاء الصلاحية: {expiry_date}\n"
            info_message += f"مدة الصلاحية: {expiry_days} يوم\n"
            info_message += f"الميزات المفعلة: {', '.join(features)}\n"

            QMessageBox.information(self, "تم التوليد", info_message)
        else:
            QMessageBox.critical(self, "خطأ", "حدث خطأ أثناء توليد كود التفعيل")

    def copy_code(self):
        """
        نسخ كود التفعيل إلى الحافظة
        """
        activation_code = self.activation_code.toPlainText()

        if not activation_code:
            QMessageBox.warning(self, "تحذير", "لا يوجد كود تفعيل لنسخه")
            return

        # نسخ الكود إلى الحافظة
        clipboard = QApplication.clipboard()
        clipboard.setText(activation_code)

        QMessageBox.information(self, "تم النسخ", "تم نسخ كود التفعيل إلى الحافظة")

    def save_code(self):
        """
        حفظ كود التفعيل في ملف
        """
        activation_code = self.activation_code.toPlainText()

        if not activation_code:
            QMessageBox.warning(self, "تحذير", "لا يوجد كود تفعيل لحفظه")
            return

        # فتح مربع حوار حفظ الملف
        file_path, _ = QFileDialog.getSaveFileName(
            self,
            "حفظ كود التفعيل",
            f"activation_code_{self.hardware_id.text()}.txt",
            "ملفات نصية (*.txt)"
        )

        if not file_path:
            return

        try:
            # حفظ الكود في الملف
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(activation_code)

            QMessageBox.information(self, "تم الحفظ", f"تم حفظ كود التفعيل في الملف:\n{file_path}")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء حفظ الملف:\n{str(e)}")

if __name__ == "__main__":
    app = QApplication(sys.argv)

    # تطبيق الخط الافتراضي
    default_font = QFont("Arial", 10)
    app.setFont(default_font)

    window = ActivationGeneratorWindow()
    window.show()

    sys.exit(app.exec())
