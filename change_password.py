"""
Script para cambiar la contraseña del usuario admin a "1"
"""

import sqlite3
import bcrypt
from database import hash_password

def change_admin_password(new_password="1"):
    """
    Cambiar la contraseña del usuario admin
    
    Args:
        new_password (str): Nueva contraseña (por defecto: "1")
    """
    try:
        # Conectar a la base de datos
        conn = sqlite3.connect("diamond_sales.db")
        cursor = conn.cursor()
        
        # Hashear la nueva contraseña
        hashed_password = hash_password(new_password)
        
        # Actualizar la contraseña
        cursor.execute("UPDATE users SET password_hash = ? WHERE username = 'admin'", (hashed_password,))
        
        # Guardar cambios
        conn.commit()
        print(f"¡Contraseña cambiada con éxito a '{new_password}'!")
        
    except Exception as e:
        print(f"Error al cambiar la contraseña: {str(e)}")
    finally:
        if conn:
            conn.close()

if __name__ == "__main__":
    change_admin_password()
