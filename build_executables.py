#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Script para compilar los ejecutables del programa
"""

import os
import subprocess
import shutil
import sys

def run_command(command):
    """
    Ejecuta un comando y muestra su salida en tiempo real
    
    Args:
        command (str): Comando a ejecutar
        
    Returns:
        bool: True si el comando se ejecutó correctamente, False en caso contrario
    """
    print(f"Ejecutando: {command}")
    process = subprocess.Popen(
        command,
        shell=True,
        stdout=subprocess.PIPE,
        stderr=subprocess.STDOUT,
        universal_newlines=True
    )
    
    # Mostrar la salida en tiempo real
    for line in process.stdout:
        print(line, end='')
    
    # Esperar a que termine el proceso
    process.wait()
    
    # Verificar si el comando se ejecutó correctamente
    if process.returncode != 0:
        print(f"Error al ejecutar el comando. Código de salida: {process.returncode}")
        return False
    
    return True

def build_main_executable():
    """
    Compila el ejecutable del programa principal
    
    Returns:
        bool: True si la compilación fue exitosa, False en caso contrario
    """
    print("\n=== Compilando el programa principal ===\n")
    
    # Compilar el ejecutable
    if not run_command("pyinstaller diamond_sales.spec"):
        return False
    
    print("\nPrograma principal compilado correctamente.")
    return True

def build_activation_generator():
    """
    Compila el ejecutable del generador de códigos de activación
    
    Returns:
        bool: True si la compilación fue exitosa, False en caso contrario
    """
    print("\n=== Compilando el generador de códigos de activación ===\n")
    
    # Compilar el ejecutable
    if not run_command("pyinstaller activation_generator.spec"):
        return False
    
    print("\nGenerador de códigos de activación compilado correctamente.")
    return True

def create_distribution_folder():
    """
    Crea una carpeta para la distribución con los ejecutables compilados
    
    Returns:
        bool: True si la creación fue exitosa, False en caso contrario
    """
    print("\n=== Creando carpeta de distribución ===\n")
    
    # Crear carpeta de distribución si no existe
    if not os.path.exists("dist_package"):
        os.makedirs("dist_package")
    
    # Copiar los ejecutables a la carpeta de distribución
    try:
        # Copiar el programa principal
        shutil.copytree("dist/DiamondSales", "dist_package/DiamondSales", dirs_exist_ok=True)
        
        # Copiar el generador de códigos de activación
        shutil.copytree("dist/ActivationGenerator", "dist_package/ActivationGenerator", dirs_exist_ok=True)
        
        print("\nCarpeta de distribución creada correctamente.")
        return True
    except Exception as e:
        print(f"Error al crear la carpeta de distribución: {str(e)}")
        return False

def main():
    """
    Función principal
    """
    print("=== Iniciando compilación de ejecutables ===\n")
    
    # Compilar el programa principal
    if not build_main_executable():
        print("Error al compilar el programa principal.")
        return
    
    # Compilar el generador de códigos de activación
    if not build_activation_generator():
        print("Error al compilar el generador de códigos de activación.")
        return
    
    # Crear carpeta de distribución
    if not create_distribution_folder():
        print("Error al crear la carpeta de distribución.")
        return
    
    print("\n=== Compilación completada con éxito ===")
    print("Los ejecutables se encuentran en la carpeta 'dist_package'.")

if __name__ == "__main__":
    main()
