#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
سكريبت لإعادة تعيين كلمة المرور في حالة نسيانها
"""

import sqlite3
import sys
import os
import secrets
import string
import bcrypt
from datetime import datetime, timedelta
from PyQt6.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget
from PyQt6.QtWidgets import QLabel, QLineEdit, QComboBox, QMessageBox, QHBoxLayout, QGridLayout
from PyQt6.QtGui import QIcon, QFont
from PyQt6.QtCore import Qt, QSize
from logger import log_error, log_info
from database import is_strong_password, hash_password

# استيراد وحدة البريد الإلكتروني
try:
    from email_utils import send_verification_email
    EMAIL_MODULE_AVAILABLE = True
except ImportError:
    EMAIL_MODULE_AVAILABLE = False
    log_error("فشل في استيراد وحدة البريد الإلكتروني. سيتم عرض رموز التحقق بدلاً من إرسالها.")

def generate_random_password():
    """توليد كلمة مرور عشوائية قوية"""
    # تعريف مجموعات الأحرف
    lowercase = string.ascii_lowercase
    uppercase = string.ascii_uppercase
    digits = string.digits
    special = "!@#$%^&*"

    # التأكد من وجود حرف واحد على الأقل من كل مجموعة
    password = [
        secrets.choice(lowercase),
        secrets.choice(uppercase),
        secrets.choice(digits),
        secrets.choice(special)
    ]

    # إضافة 6 أحرف عشوائية أخرى
    all_chars = lowercase + uppercase + digits + special
    password.extend(secrets.choice(all_chars) for _ in range(6))

    # خلط الأحرف
    pw_list = list(password)
    secrets.SystemRandom().shuffle(pw_list)

    return ''.join(pw_list)

class ResetPasswordApp(QMainWindow):
    def __init__(self):
        super().__init__()
        self.email_verified = False
        self.verification_code = None
        self.selected_username = None
        self.init_ui()
        self.center_window()
          def center_window(self):
        """توسيط النافذة في الشاشة"""
        try:
            # محاولة استيراد وحدة توسيط النوافذ
            from ui_utils import center_window
            center_window(self)
        except ImportError:
            # إذا لم تكن الوحدة متوفرة، توسيط النافذة يدويًا
            from PyQt6.QtWidgets import QDesktopWidget
            qr = self.frameGeometry()
            cp = QDesktopWidget().availableGeometry().center()
            qr.moveCenter(cp)
            self.move(qr.topLeft())

    def init_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("استعادة كلمة المرور")
        self.setGeometry(300, 300, 450, 350)
        self.setLayoutDirection(Qt.LayoutDirection.RightToLeft)
        
        # محاولة تحميل الأيقونة
        icon_paths = ['assets/login_icon.svg', 'assets/key_icon.ico', 'icons/key_icon.ico']
        icon_loaded = False
        for path in icon_paths:
            try:
                if os.path.exists(path):
                    self.setWindowIcon(QIcon(path))
                    icon_loaded = True
                    break
            except Exception as e:
                log_error(f"فشل في تحميل الأيقونة {path}: {str(e)}")
        
        if not icon_loaded:
            log_info("لم يتم العثور على أي أيقونة، سيتم استخدام الأيقونة الافتراضية")

        # إنشاء الكائن المركزي
        self.central_widget = QWidget()
        self.setCentralWidget(self.central_widget)
        
        # إنشاء التخطيط الرئيسي
        self.main_layout = QVBoxLayout(self.central_widget)
        
        # إضافة عنوان
        title_label = QLabel("استعادة كلمة المرور")
        title_font = title_label.font()
        title_font.setPointSize(16)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.main_layout.addWidget(title_label)
        
        # إضافة وصف
        description_label = QLabel("يرجى إدخال اسم المستخدم والبريد الإلكتروني المسجل به")
        description_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.main_layout.addWidget(description_label)
        
        # إنشاء نموذج الإدخال
        form_layout = QGridLayout()
        
        # اسم المستخدم
        username_label = QLabel("اسم المستخدم:")
        self.username_combo = QComboBox()
        self.username_combo.setEditable(True)
        form_layout.addWidget(username_label, 0, 0)
        form_layout.addWidget(self.username_combo, 0, 1)
        
        # البريد الإلكتروني
        email_label = QLabel("البريد الإلكتروني:")
        self.email_input = QLineEdit()
        form_layout.addWidget(email_label, 1, 0)
        form_layout.addWidget(self.email_input, 1, 1)
        
        self.main_layout.addLayout(form_layout)
        
        # زر التحقق من البريد الإلكتروني
        self.verify_email_button = QPushButton("التحقق من البريد الإلكتروني")
        self.verify_email_button.clicked.connect(self.verify_email)
        self.main_layout.addWidget(self.verify_email_button)
        
        # حقل رمز التحقق (مخفي في البداية)
        self.verification_section = QWidget()
        verification_layout = QHBoxLayout(self.verification_section)
        
        verification_code_label = QLabel("رمز التحقق:")
        self.verification_code_input = QLineEdit()
        verification_layout.addWidget(verification_code_label)
        verification_layout.addWidget(self.verification_code_input)
        
        self.verify_code_button = QPushButton("تأكيد الرمز")
        self.verify_code_button.clicked.connect(self.verify_code)
        verification_layout.addWidget(self.verify_code_button)
        
        self.verification_section.setVisible(False)
        self.main_layout.addWidget(self.verification_section)
        
        # حقل كلمة المرور الجديدة (مخفي في البداية)
        self.new_password_section = QWidget()
        password_layout = QGridLayout(self.new_password_section)
        
        new_password_label = QLabel("كلمة المرور الجديدة:")
        self.new_password_input = QLineEdit()
        self.new_password_input.setEchoMode(QLineEdit.EchoMode.Password)
        password_layout.addWidget(new_password_label, 0, 0)
        password_layout.addWidget(self.new_password_input, 0, 1)
        
        confirm_password_label = QLabel("تأكيد كلمة المرور:")
        self.confirm_password_input = QLineEdit()
        self.confirm_password_input.setEchoMode(QLineEdit.EchoMode.Password)
        password_layout.addWidget(confirm_password_label, 1, 0)
        password_layout.addWidget(self.confirm_password_input, 1, 1)
        
        # إضافة مؤشر لقوة كلمة المرور
        password_strength_label = QLabel("قوة كلمة المرور:")
        self.password_strength_indicator = QLabel("---")
        password_layout.addWidget(password_strength_label, 2, 0)
        password_layout.addWidget(self.password_strength_indicator, 2, 1)
        
        # إضافة حدث لتتبع تغيرات كلمة المرور
        self.new_password_input.textChanged.connect(self.update_password_strength)
        
        self.generate_password_button = QPushButton("توليد كلمة مرور عشوائية")
        self.generate_password_button.clicked.connect(self.generate_password)
        password_layout.addWidget(self.generate_password_button, 3, 0, 1, 2)
        
        self.new_password_section.setVisible(False)
        self.main_layout.addWidget(self.new_password_section)
        
        # زر إعادة تعيين كلمة المرور
        self.reset_password_button = QPushButton("إعادة تعيين كلمة المرور")
        self.reset_password_button.clicked.connect(self.reset_password)
        self.reset_password_button.setVisible(False)
        self.main_layout.addWidget(self.reset_password_button)
        
        # إضافة زر العودة
        self.back_button = QPushButton("العودة")
        self.back_button.clicked.connect(self.close)
        self.main_layout.addWidget(self.back_button)
        
        # تحميل قائمة المستخدمين
        self.load_users()
