"""
تحسين دالة load_transactions في صندوق النقدية
"""

from PyQt6.QtWidgets import QMessageBox
from sqlalchemy import func, desc
from db_session import session_scope
from database import صندوق_النقدية, حركة_نقدية

def enhance_cash_box_transactions():
    """
    تحسين استعلام جلب حركات صندوق النقدية
    """
    try:
        with session_scope() as session:
            # التأكد من وجود صندوق النقدية
            cash_box = session.query(صندوق_النقدية).first()
            if not cash_box:
                QMessageBox.warning(None, "تنبيه", "لم يتم العثور على صندوق النقدية")
                return
            
            # التحقق من حركات السحب (withdraw)
            withdraw_transactions = session.query(حركة_نقدية).filter(
                حركة_نقدية.transaction_type == "withdraw"
            ).all()
            
            withdraw_count = len(withdraw_transactions)
            
            # التحقق من حركات الإيداع (deposit)
            deposit_transactions = session.query(حركة_نقدية).filter(
                حركة_نقدية.transaction_type == "deposit"
            ).all()
            
            deposit_count = len(deposit_transactions)
            
            # التأكد من أن جميع الحركات مرتبطة بصندوق النقدية
            orphaned_transactions = session.query(حركة_نقدية).filter(
                حركة_نقدية.cash_box_id != cash_box.id
            ).all()
            
            for transaction in orphaned_transactions:
                transaction.cash_box_id = cash_box.id
            
            if orphaned_transactions:
                session.commit()
                orphaned_count = len(orphaned_transactions)
            else:
                orphaned_count = 0
            
            return {
                "deposits": deposit_count,
                "withdrawals": withdraw_count,
                "orphaned_fixed": orphaned_count,
                "cash_box_balance": cash_box.balance
            }
            
    except Exception as e:
        return {
            "error": str(e)
        }

if __name__ == "__main__":
    from PyQt6.QtWidgets import QApplication
    import sys
    
    app = QApplication.instance() or QApplication(sys.argv)
    
    result = enhance_cash_box_transactions()
    
    if "error" in result:
        QMessageBox.critical(None, "خطأ", f"حدث خطأ: {result['error']}")
    else:
        QMessageBox.information(None, "نتائج التحسين", 
            f"الإيداعات: {result['deposits']}\n"
            f"السحوبات: {result['withdrawals']}\n"
            f"الحركات المصححة: {result['orphaned_fixed']}\n"
            f"رصيد الصندوق: {result['cash_box_balance']:.2f} $")
