"""
Script para arreglar las transacciones de efectivo y actualizar el saldo de la caja
"""

import sqlite3
from datetime import datetime

def fix_cash_transactions():
    """
    Crear transacciones de efectivo basadas en los recibos existentes
    y actualizar el saldo de la caja
    """
    try:
        # Conectar a la base de datos
        conn = sqlite3.connect("diamond_sales.db")
        cursor = conn.cursor()
        
        # Obtener el ID de la caja
        cursor.execute("SELECT id FROM cash_box LIMIT 1")
        cash_box_id = cursor.fetchone()[0]
        
        # Obtener el ID del usuario admin
        cursor.execute("SELECT id FROM users WHERE username = 'admin' LIMIT 1")
        admin_id = cursor.fetchone()[0]
        
        # Eliminar transacciones existentes
        cursor.execute("DELETE FROM cash_transactions")
        
        # Obtener todos los recibos
        cursor.execute("""
            SELECT id, receipt_type, amount_usd, issue_date, customer_id, supplier_id, sale_id, purchase_id
            FROM receipts
            ORDER BY issue_date
        """)
        receipts = cursor.fetchall()
        
        # Inicializar el saldo
        balance = 0.0
        
        # Crear transacciones de efectivo para cada recibo
        for receipt in receipts:
            receipt_id, receipt_type, amount, issue_date, customer_id, supplier_id, sale_id, purchase_id = receipt
            
            # Determinar el tipo de transacción y la descripción
            if receipt_type in ['CashIn', 'قبض']:
                transaction_type = 'deposit'
                balance += amount
                
                # Crear descripción
                if customer_id:
                    cursor.execute("SELECT name FROM customers WHERE id = ?", (customer_id,))
                    customer_name = cursor.fetchone()[0]
                    description = f"سند قبض رقم {receipt_id} من العميل {customer_name}"
                else:
                    description = f"سند قبض رقم {receipt_id}"
                
                if sale_id:
                    description += f" - مبيعات رقم {sale_id}"
            
            elif receipt_type in ['CashOut', 'صرف']:
                transaction_type = 'withdraw'
                balance -= amount
                
                # Crear descripción
                if supplier_id:
                    cursor.execute("SELECT name FROM suppliers WHERE id = ?", (supplier_id,))
                    supplier_name = cursor.fetchone()[0]
                    description = f"سند صرف رقم {receipt_id} للمورد {supplier_name}"
                else:
                    description = f"سند صرف رقم {receipt_id}"
                
                if purchase_id:
                    description += f" - مشتريات رقم {purchase_id}"
            
            # Insertar la transacción
            cursor.execute("""
                INSERT INTO cash_transactions 
                (cash_box_id, transaction_type, amount, balance_after, transaction_date, 
                reference, description, created_by, created_at, receipt_id)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                cash_box_id, 
                transaction_type, 
                amount, 
                balance, 
                issue_date, 
                f"Receipt-{receipt_id}", 
                description, 
                admin_id, 
                datetime.now(), 
                receipt_id
            ))
        
        # Actualizar el saldo de la caja
        cursor.execute("UPDATE cash_box SET balance = ?, last_updated = ? WHERE id = ?", 
                      (balance, datetime.now(), cash_box_id))
        
        # Guardar cambios
        conn.commit()
        print(f"Se han creado {len(receipts)} transacciones de efectivo")
        print(f"Saldo actual de la caja: {balance:.2f} USD")
        
    except Exception as e:
        print(f"Error al arreglar las transacciones de efectivo: {str(e)}")
        if conn:
            conn.rollback()
    finally:
        if conn:
            conn.close()

if __name__ == "__main__":
    fix_cash_transactions()
