"""
وحدة الأمان للتطبيق
"""

import re
import random
import string
import time
import hashlib
from functools import wraps
from logger import log_error, log_warning, log_info

# قائمة بكلمات المرور الشائعة التي يجب تجنبها
COMMON_PASSWORDS = [
    "123456", "password", "12345678", "qwerty", "123456789", "12345", "1234",
    "111111", "1234567", "dragon", "123123", "baseball", "abc123", "football",
    "monkey", "letmein", "696969", "shadow", "master", "666666", "qwertyuiop",
    "123321", "mustang", "1234567890", "michael", "654321", "superman", "1qaz2wsx",
    "7777777", "121212", "000000", "qazwsx", "123qwe", "killer", "trustno1",
    "jordan", "jennifer", "zxcvbnm", "asdfgh", "hunter", "buster", "soccer",
    "harley", "batman", "andrew", "tigger", "sunshine", "iloveyou", "2000",
    "charlie", "robert", "thomas", "hockey", "ranger", "daniel", "starwars",
    "klaster", "112233", "george", "computer", "michelle", "jessica", "pepper",
    "1111", "zxcvbn", "555555", "11111111", "131313", "freedom", "777777",
    "pass", "maggie", "159753", "aaaaaa", "ginger", "princess", "joshua",
    "cheese", "amanda", "summer", "love", "ashley", "nicole", "chelsea",
    "biteme", "matthew", "access", "yankees", "987654321", "dallas", "austin",
    "thunder", "taylor", "matrix", "mobilemail", "mom", "monitor", "monitoring",
    "montana", "moon", "moscow"
]

def validate_password(password):
    """
    التحقق من قوة كلمة المرور

    يجب أن تحتوي كلمة المرور على:
    - حرف واحد على الأقل (يمكن أن يكون حرفًا أو رقمًا)

    Args:
        password (str): كلمة المرور للتحقق منها

    Returns:
        tuple: (صالحة، رسالة الخطأ)
    """
    # التحقق من أن كلمة المرور غير فارغة
    if not password:
        return False, "كلمة المرور فارغة. يجب إدخال كلمة مرور."

    # التحقق من وجود حرف أو رقم واحد على الأقل
    if not re.search(r'[A-Za-z0-9]', password):
        return False, "يجب أن تحتوي كلمة المرور على حرف أو رقم واحد على الأقل."

    return True, "كلمة المرور قوية."

def generate_password(length=12):
    """
    توليد كلمة مرور قوية عشوائية

    Args:
        length (int): طول كلمة المرور (الافتراضي: 12)

    Returns:
        str: كلمة المرور المولدة
    """
    # التأكد من أن الطول كافٍ
    if length < 8:
        length = 8

    # تعريف مجموعات الأحرف
    lowercase = string.ascii_lowercase
    uppercase = string.ascii_uppercase
    digits = string.digits
    special = "!@#$%^&*(),.?\":{}|<>"

    # التأكد من وجود حرف واحد على الأقل من كل مجموعة
    password = [
        random.choice(lowercase),
        random.choice(uppercase),
        random.choice(digits),
        random.choice(special)
    ]

    # إضافة باقي الأحرف
    all_chars = lowercase + uppercase + digits + special
    password.extend(random.choice(all_chars) for _ in range(length - 4))

    # خلط الأحرف
    random.shuffle(password)

    return ''.join(password)

def rate_limit(max_calls, period):
    """
    مزخرف (decorator) للحد من معدل الاستدعاء

    Args:
        max_calls (int): الحد الأقصى لعدد الاستدعاءات
        period (int): الفترة الزمنية بالثواني

    Returns:
        function: الدالة المزخرفة
    """
    calls = {}

    def decorator(func):
        @wraps(func)
        def wrapper(self, *args, **kwargs):
            # استخدام اسم الدالة كمفتاح
            key = func.__name__

            current_time = time.time()
            call_history = calls.get(key, [])

            # إزالة الاستدعاءات القديمة
            call_history = [t for t in call_history if current_time - t < period]

            # التحقق من عدد الاستدعاءات
            if len(call_history) >= max_calls:
                log_warning(f"تم تجاوز معدل الاستدعاء للدالة {key}")
                raise Exception(f"تم تجاوز معدل الاستدعاء. يرجى المحاولة مرة أخرى بعد {period} ثانية.")

            # إضافة الاستدعاء الحالي
            call_history.append(current_time)
            calls[key] = call_history

            return func(self, *args, **kwargs)
        return wrapper
    return decorator
