#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تست وظائف شاشة التقارير
Test script for reports screen functionality
"""

import sys
import os
from datetime import datetime, date
from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import QDate

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_reports_import():
    """Test if reports_screen can be imported without errors"""
    try:
        from reports_screen import ReportsScreen
        print("✓ تم استيراد شاشة التقارير بنجاح - Reports screen imported successfully")
        return True
    except Exception as e:
        print(f"✗ خطأ في استيراد شاشة التقارير - Error importing reports screen: {e}")
        return False

def test_reports_screen_creation():
    """Test if ReportsScreen can be instantiated"""
    try:
        from reports_screen import ReportsScreen
        from database import get_session
        
        # Create a test session
        session = get_session()
        
        # Create reports screen instance
        reports_screen = ReportsScreen(session)
        print("✓ تم إنشاء شاشة التقارير بنجاح - Reports screen created successfully")
        
        session.close()
        return True
    except Exception as e:
        print(f"✗ خطأ في إنشاء شاشة التقارير - Error creating reports screen: {e}")
        return False

def test_date_handling():
    """Test date handling in reports"""
    try:
        # Test QDate conversion
        test_date = QDate.currentDate()
        python_date = test_date.toPython()
        print(f"✓ تحويل التاريخ يعمل بشكل صحيح - Date conversion works: {python_date}")
        return True
    except Exception as e:
        print(f"✗ خطأ في تحويل التاريخ - Error in date conversion: {e}")
        return False

def main():
    """Run all tests"""
    print("بدء اختبار وظائف شاشة التقارير...")
    print("Starting reports screen functionality tests...")
    print("-" * 50)
    
    # Initialize QApplication (needed for PyQt6)
    app = QApplication(sys.argv)
    
    tests = [
        ("استيراد الشاشة - Import Test", test_reports_import),
        ("إنشاء الشاشة - Creation Test", test_reports_screen_creation),
        ("معالجة التاريخ - Date Handling Test", test_date_handling),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\nتشغيل اختبار: {test_name}")
        print(f"Running test: {test_name}")
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"✗ فشل الاختبار - Test failed: {e}")
    
    print("-" * 50)
    print(f"النتائج - Results: {passed}/{total} اختبارات نجحت - tests passed")
    
    if passed == total:
        print("✓ جميع الاختبارات نجحت! - All tests passed!")
        return True
    else:
        print("✗ بعض الاختبارات فشلت - Some tests failed")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
