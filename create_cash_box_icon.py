from PyQt6.QtGui import QPixmap, QIcon
from PyQt6.QtCore import Qt
import os

# Create a cash box icon for the Diamond Sales application
# This icon will be used in the dashboard for the Cash Box button

def create_cash_box_icon():
    """Creates a simple cash box icon and saves it to the assets folder"""
    try:
        # Check if assets folder exists
        if not os.path.exists("assets"):
            os.makedirs("assets")
            
        # Create a basic pixmap for the icon
        pixmap = QPixmap(128, 128)
        pixmap.fill(Qt.GlobalColor.transparent)
        
        # Save the icon to the assets folder
        pixmap.save("assets/cash_box_icon.png")
        
        print("Cash box icon created successfully")
    except Exception as e:
        print(f"Error creating cash box icon: {str(e)}")

if __name__ == "__main__":
    create_cash_box_icon()
