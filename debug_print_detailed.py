#!/usr/bin/env python
# -*- coding: utf-8 -*-

import sys
import os
from PyQt6.QtWidgets import QApplication
from cash_box_screen import شاشة_صندوق_النقدية
from db_session import session_scope

class TestUser:
    def __init__(self):
        self.id = 1
        self.username = "admin"
        self.role = "admin"

def debug_print_function():
    app = QApplication(sys.argv)
    
    # إنشاء مستخدم وهمي
    user = TestUser()
    
    # إنشاء شاشة صندوق النقدية
    cash_screen = شاشة_صندوق_النقدية(user)
    
    # تحميل البيانات أولاً
    cash_screen.load_data()
    
    print(f"عدد الصفوف في الجدول: {cash_screen.transactions_table.rowCount()}")
    print(f"عدد الأعمدة في الجدول: {cash_screen.transactions_table.columnCount()}")
    
    # التحقق من وجود العناصر
    print(f"total_balance_label موجود: {hasattr(cash_screen, 'total_balance_label')}")
    print(f"total_in_label موجود: {hasattr(cash_screen, 'total_in_label')}")
    print(f"total_out_label موجود: {hasattr(cash_screen, 'total_out_label')}")
    
    if hasattr(cash_screen, 'total_balance_label'):
        print(f"نص الرصيد: '{cash_screen.total_balance_label.text()}'")
    if hasattr(cash_screen, 'total_in_label'):
        print(f"نص الإيداعات: '{cash_screen.total_in_label.text()}'")
    if hasattr(cash_screen, 'total_out_label'):
        print(f"نص المسحوبات: '{cash_screen.total_out_label.text()}'")
    
    # اختبار دالة إنشاء HTML مع معالجة الأخطاء
    try:
        print("بدء إنشاء محتوى HTML...")
        
        # اختبار كل خطوة على حدة
        print("1. الحصول على بيانات الجدول...")
        rows = cash_screen.transactions_table.rowCount()
        cols = cash_screen.transactions_table.columnCount()
        print(f"   الصفوف: {rows}, الأعمدة: {cols}")
        
        print("2. الحصول على قيم الملخص...")
        balance_text = cash_screen.total_balance_label.text()
        total_in_text = cash_screen.total_in_label.text()
        total_out_text = cash_screen.total_out_label.text()
        print(f"   الرصيد: {balance_text}")
        print(f"   الإيداعات: {total_in_text}")
        print(f"   المسحوبات: {total_out_text}")
        
        print("3. معالجة النصوص...")
        balance = balance_text.split(": ")[1] if ": " in balance_text else balance_text
        total_in = total_in_text.split(": ")[1] if ": " in total_in_text else total_in_text
        total_out = total_out_text.split(": ")[1] if ": " in total_out_text else total_out_text
        print(f"   الرصيد المعالج: {balance}")
        print(f"   الإيداعات المعالجة: {total_in}")
        print(f"   المسحوبات المعالجة: {total_out}")
        
        print("4. إعداد التواريخ...")
        start_date_str = cash_screen.start_date_edit.date().toString("yyyy-MM-dd")
        end_date_str = cash_screen.end_date_edit.date().toString("yyyy-MM-dd")
        date_range = f"للفترة من {start_date_str} إلى {end_date_str}"
        print(f"   نطاق التاريخ: {date_range}")
        
        print("5. بناء محتوى الجدول...")
        table_content = "<table><thead><tr><th>رقم العملية</th></tr></thead><tbody>"
        
        if rows == 0:
            table_content += '<tr><td colspan="10">لا توجد معاملات</td></tr>'
        else:
            for row in range(min(rows, 2)):  # اختبار أول صفين فقط
                table_content += '<tr><td>اختبار</td></tr>'
        
        table_content += "</tbody></table>"
        print(f"   طول محتوى الجدول: {len(table_content)}")
        
        print("6. بناء محتوى الملخص...")
        summary_content = '<div class="summary-item"><div class="summary-label">اختبار</div></div>'
        print(f"   طول محتوى الملخص: {len(summary_content)}")
        
        print("7. استدعاء دالة القالب...")
        html = cash_screen.generate_enhanced_html_template(
            title="تقرير صندوق النقدية",
            subtitle="تقرير صندوق النقدية",
            date_range=date_range,
            content=table_content,
            summary=summary_content
        )
        
        print(f"تم إنشاء محتوى HTML. الطول: {len(html)} حرف")
        
        if html:
            # حفظ المحتوى في ملف للاختبار
            test_file = "debug_detailed_cash_report.html"
            with open(test_file, 'w', encoding='utf-8') as file:
                file.write(html)
            print(f"تم حفظ الملف: {test_file}")
        else:
            print("محتوى HTML فارغ!")
        
    except Exception as e:
        print(f"خطأ في الاختبار: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_print_function()
