from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from database import User, hash_password

# الاتصال بقاعدة البيانات
engine = create_engine('sqlite:///diamond_sales.db')
Session = sessionmaker(bind=engine)
session = Session()

try:
    # البحث عن المستخدم admin
    admin = session.query(User).filter_by(username='admin').first()
    
    if admin:
        # تغيير كلمة المرور إلى '1'
        admin.password_hash = hash_password('1')
        admin.failed_login_attempts = 0
        admin.is_active = True
        session.commit()
        print("تم تغيير كلمة مرور المستخدم admin إلى: 1")
    else:
        print("لم يتم العثور على المستخدم admin")

except Exception as e:
    session.rollback()
    print(f"حدث خطأ: {str(e)}")
finally:
    session.close()
