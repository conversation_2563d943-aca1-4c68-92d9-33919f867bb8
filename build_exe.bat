@echo off
chcp 65001 >nul
echo Building Diamond Sales executable...
echo.

REM Clean previous builds
if exist "dist" rmdir /s /q "dist"
if exist "build" rmdir /s /q "build"

REM Run PyInstaller
".venv\Scripts\pyinstaller.exe" diamond_sales.spec

if %errorlevel% neq 0 (
    echo Error: Build failed!
    pause
    exit /b 1
)

echo.
echo Build completed successfully!
echo Executable location: dist\DiamondSales\
echo.
pause