#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Diamond Sales V1.30 - Final Verification Script
اختبار نهائي لجميع مكونات برنامج مبيعات الألماس الإصدار 1.30
"""

import os
import sys
import subprocess
import json
from pathlib import Path

def print_status(message, status="INFO"):
    """طباعة رسالة حالة بتنسيق منظم"""
    symbols = {
        "INFO": "ℹ️",
        "SUCCESS": "✅", 
        "ERROR": "❌",
        "WARNING": "⚠️"
    }
    print(f"{symbols.get(status, 'ℹ️')} {message}")

def check_file_exists(file_path, description):
    """فحص وجود ملف"""
    if os.path.exists(file_path):
        size = os.path.getsize(file_path)
        print_status(f"{description}: موجود ({size:,} بايت)", "SUCCESS")
        return True
    else:
        print_status(f"{description}: مفقود", "ERROR")
        return False

def verify_installer_package():
    """فحص مجلد حزمة التثبيت"""
    print("\n" + "="*60)
    print("🔍 فحص مجلد حزمة التثبيت")
    print("="*60)
    
    base_path = Path("installer_package")
    
    # فحص الملفات الأساسية
    files_to_check = [
        (base_path / "README.txt", "ملف التعليمات"),
        (base_path / "reset_password.exe", "أداة إعادة تعيين كلمة المرور"),
        (base_path / "DiamondSales" / "DiamondSales.exe", "البرنامج الرئيسي"),
        (base_path / "DiamondSales" / "diamond_sales.db", "قاعدة البيانات"),
        (base_path / "ActivationGenerator" / "ActivationGenerator.exe", "مولد أكواد التفعيل"),
    ]
    
    all_passed = True
    for file_path, description in files_to_check:
        if not check_file_exists(file_path, description):
            all_passed = False
    
    # فحص المجلدات
    dirs_to_check = [
        (base_path / "DiamondSales" / "assets", "مجلد الأصول"),
        (base_path / "DiamondSales" / "translations", "مجلد الترجمات"),
        (base_path / "DiamondSales" / "_internal", "مجلد مكتبات البرنامج الرئيسي"),
        (base_path / "ActivationGenerator" / "_internal", "مجلد مكتبات مولد التفعيل"),
    ]
    
    for dir_path, description in dirs_to_check:
        if os.path.exists(dir_path) and os.path.isdir(dir_path):
            files_count = len(list(Path(dir_path).glob("*")))
            print_status(f"{description}: موجود ({files_count} ملف/مجلد)", "SUCCESS")
        else:
            print_status(f"{description}: مفقود", "ERROR")
            all_passed = False
    
    return all_passed

def verify_installer_files():
    """فحص ملفات برنامج التثبيت"""
    print("\n" + "="*60)
    print("📦 فحص ملفات برنامج التثبيت")
    print("="*60)
    
    installer_files = [
        ("installer_output/DiamondSalesSetup_v1.30.exe", "برنامج التثبيت الرئيسي"),
        ("diamond_sales_installer_v130.iss", "ملف إعداد Inno Setup"),
    ]
    
    all_passed = True
    for file_path, description in installer_files:
        if not check_file_exists(file_path, description):
            all_passed = False
    
    return all_passed

def verify_build_files():
    """فحص ملفات البناء والمواصفات"""
    print("\n" + "="*60)
    print("🔧 فحص ملفات البناء والمواصفات")
    print("="*60)
    
    build_files = [
        ("build_all.py", "سكريبت البناء الشامل"),
        ("diamond_sales.spec", "مواصفات PyInstaller للبرنامج الرئيسي"),
        ("activation_generator.spec", "مواصفات مولد التفعيل"),
        ("reset_password.spec", "مواصفات أداة إعادة تعيين كلمة المرور"),
        ("main.py", "ملف البرنامج الرئيسي"),
    ]
    
    all_passed = True
    for file_path, description in build_files:
        if not check_file_exists(file_path, description):
            all_passed = False
    
    return all_passed

def verify_documentation():
    """فحص ملفات التوثيق"""
    print("\n" + "="*60)
    print("📚 فحص ملفات التوثيق")
    print("="*60)
    
    doc_files = [
        ("INSTALLATION_GUIDE.md", "دليل التثبيت الشامل"),
        ("installer_package/README.txt", "ملف التعليمات السريعة"),
    ]
    
    all_passed = True
    for file_path, description in doc_files:
        if not check_file_exists(file_path, description):
            all_passed = False
    
    return all_passed

def generate_verification_report():
    """إنشاء تقرير التحقق النهائي"""
    print("\n" + "="*60)
    print("📊 تقرير التحقق النهائي")
    print("="*60)
    
    results = {
        "installer_package": verify_installer_package(),
        "installer_files": verify_installer_files(), 
        "build_files": verify_build_files(),
        "documentation": verify_documentation()
    }
    
    # حساب النتيجة الإجمالية
    total_tests = len(results)
    passed_tests = sum(results.values())
    
    print(f"\n📈 النتائج:")
    print(f"   - اجتاز: {passed_tests}/{total_tests}")
    print(f"   - نسبة النجاح: {(passed_tests/total_tests)*100:.1f}%")
    
    if all(results.values()):
        print_status("تم التحقق من جميع المكونات بنجاح! 🎉", "SUCCESS")
        print_status("برنامج التثبيت جاهز للتوزيع", "SUCCESS")
    else:
        print_status("يوجد مكونات مفقودة أو تحتاج إصلاح", "WARNING")
        for test_name, result in results.items():
            if not result:
                print_status(f"فشل في: {test_name}", "ERROR")
    
    # حفظ التقرير
    report = {
        "timestamp": "2025-05-24",
        "version": "1.30",
        "results": results,
        "summary": {
            "total_tests": total_tests,
            "passed_tests": passed_tests,
            "success_rate": f"{(passed_tests/total_tests)*100:.1f}%"
        }
    }
    
    with open("verification_report.json", "w", encoding="utf-8") as f:
        json.dump(report, f, indent=2, ensure_ascii=False)
    
    print_status("تم حفظ تقرير التحقق في verification_report.json", "INFO")
    
    return all(results.values())

def main():
    """الدالة الرئيسية"""
    print("🚀 برنامج التحقق النهائي من مبيعات الألماس الإصدار 1.30")
    print("="*70)
    
    # التأكد من وجودنا في المجلد الصحيح
    if not os.path.exists("main.py"):
        print_status("يجب تشغيل هذا السكريبت من مجلد DiamondSalesV1.20", "ERROR")
        return False
    
    # بدء عملية التحقق
    success = generate_verification_report()
    
    if success:
        print("\n🎯 توصيات نهائية:")
        print("   ✅ يمكن توزيع برنامج التثبيت")
        print("   ✅ جميع المكونات جاهزة")
        print("   ✅ التوثيق مكتمل")
        print("\n📁 ملفات التوزيع النهائية:")
        print("   📦 installer_output/DiamondSalesSetup_v1.30.exe")
        print("   📚 INSTALLATION_GUIDE.md") 
        print("   📋 verification_report.json")
    
    return success

if __name__ == "__main__":
    main()
