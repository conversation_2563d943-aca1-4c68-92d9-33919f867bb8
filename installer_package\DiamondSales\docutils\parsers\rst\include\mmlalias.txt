.. This data file has been placed in the public domain.
.. Derived from the Unicode character mappings available from
   <http://www.w3.org/2003/entities/xml/>.
   Processed by unicode2rstsubs.py, part of Docutils:
   <https://docutils.sourceforge.io>.

.. |angle|                           unicode:: U+02220 .. ANGLE
.. |ApplyFunction|                   unicode:: U+02061 .. FUNCTION APPLICATION
.. |approx|                          unicode:: U+02248 .. ALMOST EQUAL TO
.. |approxeq|                        unicode:: U+0224A .. ALMOST EQUAL OR EQUAL TO
.. |Assign|                          unicode:: U+02254 .. COLON EQUALS
.. |backcong|                        unicode:: U+0224C .. ALL EQUAL TO
.. |backepsilon|                     unicode:: U+003F6 .. GREEK REVERSED LUNATE EPSILON SYMBOL
.. |backprime|                       unicode:: U+02035 .. REVERSED PRIME
.. |backsim|                         unicode:: U+0223D .. REVERSED TILDE
.. |backsimeq|                       unicode:: U+022CD .. REVERSED TILDE EQUALS
.. |Backslash|                       unicode:: U+02216 .. SET MINUS
.. |barwedge|                        unicode:: U+02305 .. PROJECTIVE
.. |Because|                         unicode:: U+02235 .. BECAUSE
.. |because|                         unicode:: U+02235 .. BECAUSE
.. |Bernoullis|                      unicode:: U+0212C .. SCRIPT CAPITAL B
.. |between|                         unicode:: U+0226C .. BETWEEN
.. |bigcap|                          unicode:: U+022C2 .. N-ARY INTERSECTION
.. |bigcirc|                         unicode:: U+025EF .. LARGE CIRCLE
.. |bigcup|                          unicode:: U+022C3 .. N-ARY UNION
.. |bigodot|                         unicode:: U+02A00 .. N-ARY CIRCLED DOT OPERATOR
.. |bigoplus|                        unicode:: U+02A01 .. N-ARY CIRCLED PLUS OPERATOR
.. |bigotimes|                       unicode:: U+02A02 .. N-ARY CIRCLED TIMES OPERATOR
.. |bigsqcup|                        unicode:: U+02A06 .. N-ARY SQUARE UNION OPERATOR
.. |bigstar|                         unicode:: U+02605 .. BLACK STAR
.. |bigtriangledown|                 unicode:: U+025BD .. WHITE DOWN-POINTING TRIANGLE
.. |bigtriangleup|                   unicode:: U+025B3 .. WHITE UP-POINTING TRIANGLE
.. |biguplus|                        unicode:: U+02A04 .. N-ARY UNION OPERATOR WITH PLUS
.. |bigvee|                          unicode:: U+022C1 .. N-ARY LOGICAL OR
.. |bigwedge|                        unicode:: U+022C0 .. N-ARY LOGICAL AND
.. |bkarow|                          unicode:: U+0290D .. RIGHTWARDS DOUBLE DASH ARROW
.. |blacklozenge|                    unicode:: U+029EB .. BLACK LOZENGE
.. |blacksquare|                     unicode:: U+025AA .. BLACK SMALL SQUARE
.. |blacktriangle|                   unicode:: U+025B4 .. BLACK UP-POINTING SMALL TRIANGLE
.. |blacktriangledown|               unicode:: U+025BE .. BLACK DOWN-POINTING SMALL TRIANGLE
.. |blacktriangleleft|               unicode:: U+025C2 .. BLACK LEFT-POINTING SMALL TRIANGLE
.. |blacktriangleright|              unicode:: U+025B8 .. BLACK RIGHT-POINTING SMALL TRIANGLE
.. |bot|                             unicode:: U+022A5 .. UP TACK
.. |boxminus|                        unicode:: U+0229F .. SQUARED MINUS
.. |boxplus|                         unicode:: U+0229E .. SQUARED PLUS
.. |boxtimes|                        unicode:: U+022A0 .. SQUARED TIMES
.. |Breve|                           unicode:: U+002D8 .. BREVE
.. |bullet|                          unicode:: U+02022 .. BULLET
.. |Bumpeq|                          unicode:: U+0224E .. GEOMETRICALLY EQUIVALENT TO
.. |bumpeq|                          unicode:: U+0224F .. DIFFERENCE BETWEEN
.. |CapitalDifferentialD|            unicode:: U+02145 .. DOUBLE-STRUCK ITALIC CAPITAL D
.. |Cayleys|                         unicode:: U+0212D .. BLACK-LETTER CAPITAL C
.. |Cedilla|                         unicode:: U+000B8 .. CEDILLA
.. |CenterDot|                       unicode:: U+000B7 .. MIDDLE DOT
.. |centerdot|                       unicode:: U+000B7 .. MIDDLE DOT
.. |checkmark|                       unicode:: U+02713 .. CHECK MARK
.. |circeq|                          unicode:: U+02257 .. RING EQUAL TO
.. |circlearrowleft|                 unicode:: U+021BA .. ANTICLOCKWISE OPEN CIRCLE ARROW
.. |circlearrowright|                unicode:: U+021BB .. CLOCKWISE OPEN CIRCLE ARROW
.. |circledast|                      unicode:: U+0229B .. CIRCLED ASTERISK OPERATOR
.. |circledcirc|                     unicode:: U+0229A .. CIRCLED RING OPERATOR
.. |circleddash|                     unicode:: U+0229D .. CIRCLED DASH
.. |CircleDot|                       unicode:: U+02299 .. CIRCLED DOT OPERATOR
.. |circledR|                        unicode:: U+000AE .. REGISTERED SIGN
.. |circledS|                        unicode:: U+024C8 .. CIRCLED LATIN CAPITAL LETTER S
.. |CircleMinus|                     unicode:: U+02296 .. CIRCLED MINUS
.. |CirclePlus|                      unicode:: U+02295 .. CIRCLED PLUS
.. |CircleTimes|                     unicode:: U+02297 .. CIRCLED TIMES
.. |ClockwiseContourIntegral|        unicode:: U+02232 .. CLOCKWISE CONTOUR INTEGRAL
.. |CloseCurlyDoubleQuote|           unicode:: U+0201D .. RIGHT DOUBLE QUOTATION MARK
.. |CloseCurlyQuote|                 unicode:: U+02019 .. RIGHT SINGLE QUOTATION MARK
.. |clubsuit|                        unicode:: U+02663 .. BLACK CLUB SUIT
.. |coloneq|                         unicode:: U+02254 .. COLON EQUALS
.. |complement|                      unicode:: U+02201 .. COMPLEMENT
.. |complexes|                       unicode:: U+02102 .. DOUBLE-STRUCK CAPITAL C
.. |Congruent|                       unicode:: U+02261 .. IDENTICAL TO
.. |ContourIntegral|                 unicode:: U+0222E .. CONTOUR INTEGRAL
.. |Coproduct|                       unicode:: U+02210 .. N-ARY COPRODUCT
.. |CounterClockwiseContourIntegral| unicode:: U+02233 .. ANTICLOCKWISE CONTOUR INTEGRAL
.. |CupCap|                          unicode:: U+0224D .. EQUIVALENT TO
.. |curlyeqprec|                     unicode:: U+022DE .. EQUAL TO OR PRECEDES
.. |curlyeqsucc|                     unicode:: U+022DF .. EQUAL TO OR SUCCEEDS
.. |curlyvee|                        unicode:: U+022CE .. CURLY LOGICAL OR
.. |curlywedge|                      unicode:: U+022CF .. CURLY LOGICAL AND
.. |curvearrowleft|                  unicode:: U+021B6 .. ANTICLOCKWISE TOP SEMICIRCLE ARROW
.. |curvearrowright|                 unicode:: U+021B7 .. CLOCKWISE TOP SEMICIRCLE ARROW
.. |dbkarow|                         unicode:: U+0290F .. RIGHTWARDS TRIPLE DASH ARROW
.. |ddagger|                         unicode:: U+02021 .. DOUBLE DAGGER
.. |ddotseq|                         unicode:: U+02A77 .. EQUALS SIGN WITH TWO DOTS ABOVE AND TWO DOTS BELOW
.. |Del|                             unicode:: U+02207 .. NABLA
.. |DiacriticalAcute|                unicode:: U+000B4 .. ACUTE ACCENT
.. |DiacriticalDot|                  unicode:: U+002D9 .. DOT ABOVE
.. |DiacriticalDoubleAcute|          unicode:: U+002DD .. DOUBLE ACUTE ACCENT
.. |DiacriticalGrave|                unicode:: U+00060 .. GRAVE ACCENT
.. |DiacriticalTilde|                unicode:: U+002DC .. SMALL TILDE
.. |Diamond|                         unicode:: U+022C4 .. DIAMOND OPERATOR
.. |diamond|                         unicode:: U+022C4 .. DIAMOND OPERATOR
.. |diamondsuit|                     unicode:: U+02666 .. BLACK DIAMOND SUIT
.. |DifferentialD|                   unicode:: U+02146 .. DOUBLE-STRUCK ITALIC SMALL D
.. |digamma|                         unicode:: U+003DD .. GREEK SMALL LETTER DIGAMMA
.. |div|                             unicode:: U+000F7 .. DIVISION SIGN
.. |divideontimes|                   unicode:: U+022C7 .. DIVISION TIMES
.. |doteq|                           unicode:: U+02250 .. APPROACHES THE LIMIT
.. |doteqdot|                        unicode:: U+02251 .. GEOMETRICALLY EQUAL TO
.. |DotEqual|                        unicode:: U+02250 .. APPROACHES THE LIMIT
.. |dotminus|                        unicode:: U+02238 .. DOT MINUS
.. |dotplus|                         unicode:: U+02214 .. DOT PLUS
.. |dotsquare|                       unicode:: U+022A1 .. SQUARED DOT OPERATOR
.. |doublebarwedge|                  unicode:: U+02306 .. PERSPECTIVE
.. |DoubleContourIntegral|           unicode:: U+0222F .. SURFACE INTEGRAL
.. |DoubleDot|                       unicode:: U+000A8 .. DIAERESIS
.. |DoubleDownArrow|                 unicode:: U+021D3 .. DOWNWARDS DOUBLE ARROW
.. |DoubleLeftArrow|                 unicode:: U+021D0 .. LEFTWARDS DOUBLE ARROW
.. |DoubleLeftRightArrow|            unicode:: U+021D4 .. LEFT RIGHT DOUBLE ARROW
.. |DoubleLeftTee|                   unicode:: U+02AE4 .. VERTICAL BAR DOUBLE LEFT TURNSTILE
.. |DoubleLongLeftArrow|             unicode:: U+027F8 .. LONG LEFTWARDS DOUBLE ARROW
.. |DoubleLongLeftRightArrow|        unicode:: U+027FA .. LONG LEFT RIGHT DOUBLE ARROW
.. |DoubleLongRightArrow|            unicode:: U+027F9 .. LONG RIGHTWARDS DOUBLE ARROW
.. |DoubleRightArrow|                unicode:: U+021D2 .. RIGHTWARDS DOUBLE ARROW
.. |DoubleRightTee|                  unicode:: U+022A8 .. TRUE
.. |DoubleUpArrow|                   unicode:: U+021D1 .. UPWARDS DOUBLE ARROW
.. |DoubleUpDownArrow|               unicode:: U+021D5 .. UP DOWN DOUBLE ARROW
.. |DoubleVerticalBar|               unicode:: U+02225 .. PARALLEL TO
.. |DownArrow|                       unicode:: U+02193 .. DOWNWARDS ARROW
.. |Downarrow|                       unicode:: U+021D3 .. DOWNWARDS DOUBLE ARROW
.. |downarrow|                       unicode:: U+02193 .. DOWNWARDS ARROW
.. |DownArrowUpArrow|                unicode:: U+021F5 .. DOWNWARDS ARROW LEFTWARDS OF UPWARDS ARROW
.. |downdownarrows|                  unicode:: U+021CA .. DOWNWARDS PAIRED ARROWS
.. |downharpoonleft|                 unicode:: U+021C3 .. DOWNWARDS HARPOON WITH BARB LEFTWARDS
.. |downharpoonright|                unicode:: U+021C2 .. DOWNWARDS HARPOON WITH BARB RIGHTWARDS
.. |DownLeftVector|                  unicode:: U+021BD .. LEFTWARDS HARPOON WITH BARB DOWNWARDS
.. |DownRightVector|                 unicode:: U+021C1 .. RIGHTWARDS HARPOON WITH BARB DOWNWARDS
.. |DownTee|                         unicode:: U+022A4 .. DOWN TACK
.. |DownTeeArrow|                    unicode:: U+021A7 .. DOWNWARDS ARROW FROM BAR
.. |drbkarow|                        unicode:: U+02910 .. RIGHTWARDS TWO-HEADED TRIPLE DASH ARROW
.. |Element|                         unicode:: U+02208 .. ELEMENT OF
.. |emptyset|                        unicode:: U+02205 .. EMPTY SET
.. |eqcirc|                          unicode:: U+02256 .. RING IN EQUAL TO
.. |eqcolon|                         unicode:: U+02255 .. EQUALS COLON
.. |eqsim|                           unicode:: U+02242 .. MINUS TILDE
.. |eqslantgtr|                      unicode:: U+02A96 .. SLANTED EQUAL TO OR GREATER-THAN
.. |eqslantless|                     unicode:: U+02A95 .. SLANTED EQUAL TO OR LESS-THAN
.. |EqualTilde|                      unicode:: U+02242 .. MINUS TILDE
.. |Equilibrium|                     unicode:: U+021CC .. RIGHTWARDS HARPOON OVER LEFTWARDS HARPOON
.. |Exists|                          unicode:: U+02203 .. THERE EXISTS
.. |expectation|                     unicode:: U+02130 .. SCRIPT CAPITAL E
.. |ExponentialE|                    unicode:: U+02147 .. DOUBLE-STRUCK ITALIC SMALL E
.. |exponentiale|                    unicode:: U+02147 .. DOUBLE-STRUCK ITALIC SMALL E
.. |fallingdotseq|                   unicode:: U+02252 .. APPROXIMATELY EQUAL TO OR THE IMAGE OF
.. |ForAll|                          unicode:: U+02200 .. FOR ALL
.. |Fouriertrf|                      unicode:: U+02131 .. SCRIPT CAPITAL F
.. |geq|                             unicode:: U+02265 .. GREATER-THAN OR EQUAL TO
.. |geqq|                            unicode:: U+02267 .. GREATER-THAN OVER EQUAL TO
.. |geqslant|                        unicode:: U+02A7E .. GREATER-THAN OR SLANTED EQUAL TO
.. |gg|                              unicode:: U+0226B .. MUCH GREATER-THAN
.. |ggg|                             unicode:: U+022D9 .. VERY MUCH GREATER-THAN
.. |gnapprox|                        unicode:: U+02A8A .. GREATER-THAN AND NOT APPROXIMATE
.. |gneq|                            unicode:: U+02A88 .. GREATER-THAN AND SINGLE-LINE NOT EQUAL TO
.. |gneqq|                           unicode:: U+02269 .. GREATER-THAN BUT NOT EQUAL TO
.. |GreaterEqual|                    unicode:: U+02265 .. GREATER-THAN OR EQUAL TO
.. |GreaterEqualLess|                unicode:: U+022DB .. GREATER-THAN EQUAL TO OR LESS-THAN
.. |GreaterFullEqual|                unicode:: U+02267 .. GREATER-THAN OVER EQUAL TO
.. |GreaterLess|                     unicode:: U+02277 .. GREATER-THAN OR LESS-THAN
.. |GreaterSlantEqual|               unicode:: U+02A7E .. GREATER-THAN OR SLANTED EQUAL TO
.. |GreaterTilde|                    unicode:: U+02273 .. GREATER-THAN OR EQUIVALENT TO
.. |gtrapprox|                       unicode:: U+02A86 .. GREATER-THAN OR APPROXIMATE
.. |gtrdot|                          unicode:: U+022D7 .. GREATER-THAN WITH DOT
.. |gtreqless|                       unicode:: U+022DB .. GREATER-THAN EQUAL TO OR LESS-THAN
.. |gtreqqless|                      unicode:: U+02A8C .. GREATER-THAN ABOVE DOUBLE-LINE EQUAL ABOVE LESS-THAN
.. |gtrless|                         unicode:: U+02277 .. GREATER-THAN OR LESS-THAN
.. |gtrsim|                          unicode:: U+02273 .. GREATER-THAN OR EQUIVALENT TO
.. |gvertneqq|                       unicode:: U+02269 U+0FE00 .. GREATER-THAN BUT NOT EQUAL TO - with vertical stroke
.. |Hacek|                           unicode:: U+002C7 .. CARON
.. |hbar|                            unicode:: U+0210F .. PLANCK CONSTANT OVER TWO PI
.. |heartsuit|                       unicode:: U+02665 .. BLACK HEART SUIT
.. |HilbertSpace|                    unicode:: U+0210B .. SCRIPT CAPITAL H
.. |hksearow|                        unicode:: U+02925 .. SOUTH EAST ARROW WITH HOOK
.. |hkswarow|                        unicode:: U+02926 .. SOUTH WEST ARROW WITH HOOK
.. |hookleftarrow|                   unicode:: U+021A9 .. LEFTWARDS ARROW WITH HOOK
.. |hookrightarrow|                  unicode:: U+021AA .. RIGHTWARDS ARROW WITH HOOK
.. |hslash|                          unicode:: U+0210F .. PLANCK CONSTANT OVER TWO PI
.. |HumpDownHump|                    unicode:: U+0224E .. GEOMETRICALLY EQUIVALENT TO
.. |HumpEqual|                       unicode:: U+0224F .. DIFFERENCE BETWEEN
.. |iiiint|                          unicode:: U+02A0C .. QUADRUPLE INTEGRAL OPERATOR
.. |iiint|                           unicode:: U+0222D .. TRIPLE INTEGRAL
.. |Im|                              unicode:: U+02111 .. BLACK-LETTER CAPITAL I
.. |ImaginaryI|                      unicode:: U+02148 .. DOUBLE-STRUCK ITALIC SMALL I
.. |imagline|                        unicode:: U+02110 .. SCRIPT CAPITAL I
.. |imagpart|                        unicode:: U+02111 .. BLACK-LETTER CAPITAL I
.. |Implies|                         unicode:: U+021D2 .. RIGHTWARDS DOUBLE ARROW
.. |in|                              unicode:: U+02208 .. ELEMENT OF
.. |integers|                        unicode:: U+02124 .. DOUBLE-STRUCK CAPITAL Z
.. |Integral|                        unicode:: U+0222B .. INTEGRAL
.. |intercal|                        unicode:: U+022BA .. INTERCALATE
.. |Intersection|                    unicode:: U+022C2 .. N-ARY INTERSECTION
.. |intprod|                         unicode:: U+02A3C .. INTERIOR PRODUCT
.. |InvisibleComma|                  unicode:: U+02063 .. INVISIBLE SEPARATOR
.. |InvisibleTimes|                  unicode:: U+02062 .. INVISIBLE TIMES
.. |langle|                          unicode:: U+02329 .. LEFT-POINTING ANGLE BRACKET
.. |Laplacetrf|                      unicode:: U+02112 .. SCRIPT CAPITAL L
.. |lbrace|                          unicode:: U+0007B .. LEFT CURLY BRACKET
.. |lbrack|                          unicode:: U+0005B .. LEFT SQUARE BRACKET
.. |LeftAngleBracket|                unicode:: U+02329 .. LEFT-POINTING ANGLE BRACKET
.. |LeftArrow|                       unicode:: U+02190 .. LEFTWARDS ARROW
.. |Leftarrow|                       unicode:: U+021D0 .. LEFTWARDS DOUBLE ARROW
.. |leftarrow|                       unicode:: U+02190 .. LEFTWARDS ARROW
.. |LeftArrowBar|                    unicode:: U+021E4 .. LEFTWARDS ARROW TO BAR
.. |LeftArrowRightArrow|             unicode:: U+021C6 .. LEFTWARDS ARROW OVER RIGHTWARDS ARROW
.. |leftarrowtail|                   unicode:: U+021A2 .. LEFTWARDS ARROW WITH TAIL
.. |LeftCeiling|                     unicode:: U+02308 .. LEFT CEILING
.. |LeftDoubleBracket|               unicode:: U+0301A .. LEFT WHITE SQUARE BRACKET
.. |LeftDownVector|                  unicode:: U+021C3 .. DOWNWARDS HARPOON WITH BARB LEFTWARDS
.. |LeftFloor|                       unicode:: U+0230A .. LEFT FLOOR
.. |leftharpoondown|                 unicode:: U+021BD .. LEFTWARDS HARPOON WITH BARB DOWNWARDS
.. |leftharpoonup|                   unicode:: U+021BC .. LEFTWARDS HARPOON WITH BARB UPWARDS
.. |leftleftarrows|                  unicode:: U+021C7 .. LEFTWARDS PAIRED ARROWS
.. |LeftRightArrow|                  unicode:: U+02194 .. LEFT RIGHT ARROW
.. |Leftrightarrow|                  unicode:: U+021D4 .. LEFT RIGHT DOUBLE ARROW
.. |leftrightarrow|                  unicode:: U+02194 .. LEFT RIGHT ARROW
.. |leftrightarrows|                 unicode:: U+021C6 .. LEFTWARDS ARROW OVER RIGHTWARDS ARROW
.. |leftrightharpoons|               unicode:: U+021CB .. LEFTWARDS HARPOON OVER RIGHTWARDS HARPOON
.. |leftrightsquigarrow|             unicode:: U+021AD .. LEFT RIGHT WAVE ARROW
.. |LeftTee|                         unicode:: U+022A3 .. LEFT TACK
.. |LeftTeeArrow|                    unicode:: U+021A4 .. LEFTWARDS ARROW FROM BAR
.. |leftthreetimes|                  unicode:: U+022CB .. LEFT SEMIDIRECT PRODUCT
.. |LeftTriangle|                    unicode:: U+022B2 .. NORMAL SUBGROUP OF
.. |LeftTriangleEqual|               unicode:: U+022B4 .. NORMAL SUBGROUP OF OR EQUAL TO
.. |LeftUpVector|                    unicode:: U+021BF .. UPWARDS HARPOON WITH BARB LEFTWARDS
.. |LeftVector|                      unicode:: U+021BC .. LEFTWARDS HARPOON WITH BARB UPWARDS
.. |leq|                             unicode:: U+02264 .. LESS-THAN OR EQUAL TO
.. |leqq|                            unicode:: U+02266 .. LESS-THAN OVER EQUAL TO
.. |leqslant|                        unicode:: U+02A7D .. LESS-THAN OR SLANTED EQUAL TO
.. |lessapprox|                      unicode:: U+02A85 .. LESS-THAN OR APPROXIMATE
.. |lessdot|                         unicode:: U+022D6 .. LESS-THAN WITH DOT
.. |lesseqgtr|                       unicode:: U+022DA .. LESS-THAN EQUAL TO OR GREATER-THAN
.. |lesseqqgtr|                      unicode:: U+02A8B .. LESS-THAN ABOVE DOUBLE-LINE EQUAL ABOVE GREATER-THAN
.. |LessEqualGreater|                unicode:: U+022DA .. LESS-THAN EQUAL TO OR GREATER-THAN
.. |LessFullEqual|                   unicode:: U+02266 .. LESS-THAN OVER EQUAL TO
.. |LessGreater|                     unicode:: U+02276 .. LESS-THAN OR GREATER-THAN
.. |lessgtr|                         unicode:: U+02276 .. LESS-THAN OR GREATER-THAN
.. |lesssim|                         unicode:: U+02272 .. LESS-THAN OR EQUIVALENT TO
.. |LessSlantEqual|                  unicode:: U+02A7D .. LESS-THAN OR SLANTED EQUAL TO
.. |LessTilde|                       unicode:: U+02272 .. LESS-THAN OR EQUIVALENT TO
.. |ll|                              unicode:: U+0226A .. MUCH LESS-THAN
.. |llcorner|                        unicode:: U+0231E .. BOTTOM LEFT CORNER
.. |Lleftarrow|                      unicode:: U+021DA .. LEFTWARDS TRIPLE ARROW
.. |lmoustache|                      unicode:: U+023B0 .. UPPER LEFT OR LOWER RIGHT CURLY BRACKET SECTION
.. |lnapprox|                        unicode:: U+02A89 .. LESS-THAN AND NOT APPROXIMATE
.. |lneq|                            unicode:: U+02A87 .. LESS-THAN AND SINGLE-LINE NOT EQUAL TO
.. |lneqq|                           unicode:: U+02268 .. LESS-THAN BUT NOT EQUAL TO
.. |LongLeftArrow|                   unicode:: U+027F5 .. LONG LEFTWARDS ARROW
.. |Longleftarrow|                   unicode:: U+027F8 .. LONG LEFTWARDS DOUBLE ARROW
.. |longleftarrow|                   unicode:: U+027F5 .. LONG LEFTWARDS ARROW
.. |LongLeftRightArrow|              unicode:: U+027F7 .. LONG LEFT RIGHT ARROW
.. |Longleftrightarrow|              unicode:: U+027FA .. LONG LEFT RIGHT DOUBLE ARROW
.. |longleftrightarrow|              unicode:: U+027F7 .. LONG LEFT RIGHT ARROW
.. |longmapsto|                      unicode:: U+027FC .. LONG RIGHTWARDS ARROW FROM BAR
.. |LongRightArrow|                  unicode:: U+027F6 .. LONG RIGHTWARDS ARROW
.. |Longrightarrow|                  unicode:: U+027F9 .. LONG RIGHTWARDS DOUBLE ARROW
.. |longrightarrow|                  unicode:: U+027F6 .. LONG RIGHTWARDS ARROW
.. |looparrowleft|                   unicode:: U+021AB .. LEFTWARDS ARROW WITH LOOP
.. |looparrowright|                  unicode:: U+021AC .. RIGHTWARDS ARROW WITH LOOP
.. |LowerLeftArrow|                  unicode:: U+02199 .. SOUTH WEST ARROW
.. |LowerRightArrow|                 unicode:: U+02198 .. SOUTH EAST ARROW
.. |lozenge|                         unicode:: U+025CA .. LOZENGE
.. |lrcorner|                        unicode:: U+0231F .. BOTTOM RIGHT CORNER
.. |Lsh|                             unicode:: U+021B0 .. UPWARDS ARROW WITH TIP LEFTWARDS
.. |lvertneqq|                       unicode:: U+02268 U+0FE00 .. LESS-THAN BUT NOT EQUAL TO - with vertical stroke
.. |maltese|                         unicode:: U+02720 .. MALTESE CROSS
.. |mapsto|                          unicode:: U+021A6 .. RIGHTWARDS ARROW FROM BAR
.. |measuredangle|                   unicode:: U+02221 .. MEASURED ANGLE
.. |Mellintrf|                       unicode:: U+02133 .. SCRIPT CAPITAL M
.. |MinusPlus|                       unicode:: U+02213 .. MINUS-OR-PLUS SIGN
.. |mp|                              unicode:: U+02213 .. MINUS-OR-PLUS SIGN
.. |multimap|                        unicode:: U+022B8 .. MULTIMAP
.. |napprox|                         unicode:: U+02249 .. NOT ALMOST EQUAL TO
.. |natural|                         unicode:: U+0266E .. MUSIC NATURAL SIGN
.. |naturals|                        unicode:: U+02115 .. DOUBLE-STRUCK CAPITAL N
.. |nearrow|                         unicode:: U+02197 .. NORTH EAST ARROW
.. |NegativeMediumSpace|             unicode:: U+0200B .. ZERO WIDTH SPACE
.. |NegativeThickSpace|              unicode:: U+0200B .. ZERO WIDTH SPACE
.. |NegativeThinSpace|               unicode:: U+0200B .. ZERO WIDTH SPACE
.. |NegativeVeryThinSpace|           unicode:: U+0200B .. ZERO WIDTH SPACE
.. |NestedGreaterGreater|            unicode:: U+0226B .. MUCH GREATER-THAN
.. |NestedLessLess|                  unicode:: U+0226A .. MUCH LESS-THAN
.. |nexists|                         unicode:: U+02204 .. THERE DOES NOT EXIST
.. |ngeq|                            unicode:: U+02271 .. NEITHER GREATER-THAN NOR EQUAL TO
.. |ngeqq|                           unicode:: U+02267 U+00338 .. GREATER-THAN OVER EQUAL TO with slash
.. |ngeqslant|                       unicode:: U+02A7E U+00338 .. GREATER-THAN OR SLANTED EQUAL TO with slash
.. |ngtr|                            unicode:: U+0226F .. NOT GREATER-THAN
.. |nLeftarrow|                      unicode:: U+021CD .. LEFTWARDS DOUBLE ARROW WITH STROKE
.. |nleftarrow|                      unicode:: U+0219A .. LEFTWARDS ARROW WITH STROKE
.. |nLeftrightarrow|                 unicode:: U+021CE .. LEFT RIGHT DOUBLE ARROW WITH STROKE
.. |nleftrightarrow|                 unicode:: U+021AE .. LEFT RIGHT ARROW WITH STROKE
.. |nleq|                            unicode:: U+02270 .. NEITHER LESS-THAN NOR EQUAL TO
.. |nleqq|                           unicode:: U+02266 U+00338 .. LESS-THAN OVER EQUAL TO with slash
.. |nleqslant|                       unicode:: U+02A7D U+00338 .. LESS-THAN OR SLANTED EQUAL TO with slash
.. |nless|                           unicode:: U+0226E .. NOT LESS-THAN
.. |NonBreakingSpace|                unicode:: U+000A0 .. NO-BREAK SPACE
.. |NotCongruent|                    unicode:: U+02262 .. NOT IDENTICAL TO
.. |NotDoubleVerticalBar|            unicode:: U+02226 .. NOT PARALLEL TO
.. |NotElement|                      unicode:: U+02209 .. NOT AN ELEMENT OF
.. |NotEqual|                        unicode:: U+02260 .. NOT EQUAL TO
.. |NotEqualTilde|                   unicode:: U+02242 U+00338 .. MINUS TILDE with slash
.. |NotExists|                       unicode:: U+02204 .. THERE DOES NOT EXIST
.. |NotGreater|                      unicode:: U+0226F .. NOT GREATER-THAN
.. |NotGreaterEqual|                 unicode:: U+02271 .. NEITHER GREATER-THAN NOR EQUAL TO
.. |NotGreaterFullEqual|             unicode:: U+02266 U+00338 .. LESS-THAN OVER EQUAL TO with slash
.. |NotGreaterGreater|               unicode:: U+0226B U+00338 .. MUCH GREATER THAN with slash
.. |NotGreaterLess|                  unicode:: U+02279 .. NEITHER GREATER-THAN NOR LESS-THAN
.. |NotGreaterSlantEqual|            unicode:: U+02A7E U+00338 .. GREATER-THAN OR SLANTED EQUAL TO with slash
.. |NotGreaterTilde|                 unicode:: U+02275 .. NEITHER GREATER-THAN NOR EQUIVALENT TO
.. |NotHumpDownHump|                 unicode:: U+0224E U+00338 .. GEOMETRICALLY EQUIVALENT TO with slash
.. |NotLeftTriangle|                 unicode:: U+022EA .. NOT NORMAL SUBGROUP OF
.. |NotLeftTriangleEqual|            unicode:: U+022EC .. NOT NORMAL SUBGROUP OF OR EQUAL TO
.. |NotLess|                         unicode:: U+0226E .. NOT LESS-THAN
.. |NotLessEqual|                    unicode:: U+02270 .. NEITHER LESS-THAN NOR EQUAL TO
.. |NotLessGreater|                  unicode:: U+02278 .. NEITHER LESS-THAN NOR GREATER-THAN
.. |NotLessLess|                     unicode:: U+0226A U+00338 .. MUCH LESS THAN with slash
.. |NotLessSlantEqual|               unicode:: U+02A7D U+00338 .. LESS-THAN OR SLANTED EQUAL TO with slash
.. |NotLessTilde|                    unicode:: U+02274 .. NEITHER LESS-THAN NOR EQUIVALENT TO
.. |NotPrecedes|                     unicode:: U+02280 .. DOES NOT PRECEDE
.. |NotPrecedesEqual|                unicode:: U+02AAF U+00338 .. PRECEDES ABOVE SINGLE-LINE EQUALS SIGN with slash
.. |NotPrecedesSlantEqual|           unicode:: U+022E0 .. DOES NOT PRECEDE OR EQUAL
.. |NotReverseElement|               unicode:: U+0220C .. DOES NOT CONTAIN AS MEMBER
.. |NotRightTriangle|                unicode:: U+022EB .. DOES NOT CONTAIN AS NORMAL SUBGROUP
.. |NotRightTriangleEqual|           unicode:: U+022ED .. DOES NOT CONTAIN AS NORMAL SUBGROUP OR EQUAL
.. |NotSquareSubsetEqual|            unicode:: U+022E2 .. NOT SQUARE IMAGE OF OR EQUAL TO
.. |NotSquareSupersetEqual|          unicode:: U+022E3 .. NOT SQUARE ORIGINAL OF OR EQUAL TO
.. |NotSubset|                       unicode:: U+02282 U+020D2 .. SUBSET OF with vertical line
.. |NotSubsetEqual|                  unicode:: U+02288 .. NEITHER A SUBSET OF NOR EQUAL TO
.. |NotSucceeds|                     unicode:: U+02281 .. DOES NOT SUCCEED
.. |NotSucceedsEqual|                unicode:: U+02AB0 U+00338 .. SUCCEEDS ABOVE SINGLE-LINE EQUALS SIGN with slash
.. |NotSucceedsSlantEqual|           unicode:: U+022E1 .. DOES NOT SUCCEED OR EQUAL
.. |NotSuperset|                     unicode:: U+02283 U+020D2 .. SUPERSET OF with vertical line
.. |NotSupersetEqual|                unicode:: U+02289 .. NEITHER A SUPERSET OF NOR EQUAL TO
.. |NotTilde|                        unicode:: U+02241 .. NOT TILDE
.. |NotTildeEqual|                   unicode:: U+02244 .. NOT ASYMPTOTICALLY EQUAL TO
.. |NotTildeFullEqual|               unicode:: U+02247 .. NEITHER APPROXIMATELY NOR ACTUALLY EQUAL TO
.. |NotTildeTilde|                   unicode:: U+02249 .. NOT ALMOST EQUAL TO
.. |NotVerticalBar|                  unicode:: U+02224 .. DOES NOT DIVIDE
.. |nparallel|                       unicode:: U+02226 .. NOT PARALLEL TO
.. |nprec|                           unicode:: U+02280 .. DOES NOT PRECEDE
.. |npreceq|                         unicode:: U+02AAF U+00338 .. PRECEDES ABOVE SINGLE-LINE EQUALS SIGN with slash
.. |nRightarrow|                     unicode:: U+021CF .. RIGHTWARDS DOUBLE ARROW WITH STROKE
.. |nrightarrow|                     unicode:: U+0219B .. RIGHTWARDS ARROW WITH STROKE
.. |nshortmid|                       unicode:: U+02224 .. DOES NOT DIVIDE
.. |nshortparallel|                  unicode:: U+02226 .. NOT PARALLEL TO
.. |nsimeq|                          unicode:: U+02244 .. NOT ASYMPTOTICALLY EQUAL TO
.. |nsubset|                         unicode:: U+02282 U+020D2 .. SUBSET OF with vertical line
.. |nsubseteq|                       unicode:: U+02288 .. NEITHER A SUBSET OF NOR EQUAL TO
.. |nsubseteqq|                      unicode:: U+02AC5 U+00338 .. SUBSET OF ABOVE EQUALS SIGN with slash
.. |nsucc|                           unicode:: U+02281 .. DOES NOT SUCCEED
.. |nsucceq|                         unicode:: U+02AB0 U+00338 .. SUCCEEDS ABOVE SINGLE-LINE EQUALS SIGN with slash
.. |nsupset|                         unicode:: U+02283 U+020D2 .. SUPERSET OF with vertical line
.. |nsupseteq|                       unicode:: U+02289 .. NEITHER A SUPERSET OF NOR EQUAL TO
.. |nsupseteqq|                      unicode:: U+02AC6 U+00338 .. SUPERSET OF ABOVE EQUALS SIGN with slash
.. |ntriangleleft|                   unicode:: U+022EA .. NOT NORMAL SUBGROUP OF
.. |ntrianglelefteq|                 unicode:: U+022EC .. NOT NORMAL SUBGROUP OF OR EQUAL TO
.. |ntriangleright|                  unicode:: U+022EB .. DOES NOT CONTAIN AS NORMAL SUBGROUP
.. |ntrianglerighteq|                unicode:: U+022ED .. DOES NOT CONTAIN AS NORMAL SUBGROUP OR EQUAL
.. |nwarrow|                         unicode:: U+02196 .. NORTH WEST ARROW
.. |oint|                            unicode:: U+0222E .. CONTOUR INTEGRAL
.. |OpenCurlyDoubleQuote|            unicode:: U+0201C .. LEFT DOUBLE QUOTATION MARK
.. |OpenCurlyQuote|                  unicode:: U+02018 .. LEFT SINGLE QUOTATION MARK
.. |orderof|                         unicode:: U+02134 .. SCRIPT SMALL O
.. |parallel|                        unicode:: U+02225 .. PARALLEL TO
.. |PartialD|                        unicode:: U+02202 .. PARTIAL DIFFERENTIAL
.. |pitchfork|                       unicode:: U+022D4 .. PITCHFORK
.. |PlusMinus|                       unicode:: U+000B1 .. PLUS-MINUS SIGN
.. |pm|                              unicode:: U+000B1 .. PLUS-MINUS SIGN
.. |Poincareplane|                   unicode:: U+0210C .. BLACK-LETTER CAPITAL H
.. |prec|                            unicode:: U+0227A .. PRECEDES
.. |precapprox|                      unicode:: U+02AB7 .. PRECEDES ABOVE ALMOST EQUAL TO
.. |preccurlyeq|                     unicode:: U+0227C .. PRECEDES OR EQUAL TO
.. |Precedes|                        unicode:: U+0227A .. PRECEDES
.. |PrecedesEqual|                   unicode:: U+02AAF .. PRECEDES ABOVE SINGLE-LINE EQUALS SIGN
.. |PrecedesSlantEqual|              unicode:: U+0227C .. PRECEDES OR EQUAL TO
.. |PrecedesTilde|                   unicode:: U+0227E .. PRECEDES OR EQUIVALENT TO
.. |preceq|                          unicode:: U+02AAF .. PRECEDES ABOVE SINGLE-LINE EQUALS SIGN
.. |precnapprox|                     unicode:: U+02AB9 .. PRECEDES ABOVE NOT ALMOST EQUAL TO
.. |precneqq|                        unicode:: U+02AB5 .. PRECEDES ABOVE NOT EQUAL TO
.. |precnsim|                        unicode:: U+022E8 .. PRECEDES BUT NOT EQUIVALENT TO
.. |precsim|                         unicode:: U+0227E .. PRECEDES OR EQUIVALENT TO
.. |primes|                          unicode:: U+02119 .. DOUBLE-STRUCK CAPITAL P
.. |Proportion|                      unicode:: U+02237 .. PROPORTION
.. |Proportional|                    unicode:: U+0221D .. PROPORTIONAL TO
.. |propto|                          unicode:: U+0221D .. PROPORTIONAL TO
.. |quaternions|                     unicode:: U+0210D .. DOUBLE-STRUCK CAPITAL H
.. |questeq|                         unicode:: U+0225F .. QUESTIONED EQUAL TO
.. |rangle|                          unicode:: U+0232A .. RIGHT-POINTING ANGLE BRACKET
.. |rationals|                       unicode:: U+0211A .. DOUBLE-STRUCK CAPITAL Q
.. |rbrace|                          unicode:: U+0007D .. RIGHT CURLY BRACKET
.. |rbrack|                          unicode:: U+0005D .. RIGHT SQUARE BRACKET
.. |Re|                              unicode:: U+0211C .. BLACK-LETTER CAPITAL R
.. |realine|                         unicode:: U+0211B .. SCRIPT CAPITAL R
.. |realpart|                        unicode:: U+0211C .. BLACK-LETTER CAPITAL R
.. |reals|                           unicode:: U+0211D .. DOUBLE-STRUCK CAPITAL R
.. |ReverseElement|                  unicode:: U+0220B .. CONTAINS AS MEMBER
.. |ReverseEquilibrium|              unicode:: U+021CB .. LEFTWARDS HARPOON OVER RIGHTWARDS HARPOON
.. |ReverseUpEquilibrium|            unicode:: U+0296F .. DOWNWARDS HARPOON WITH BARB LEFT BESIDE UPWARDS HARPOON WITH BARB RIGHT
.. |RightAngleBracket|               unicode:: U+0232A .. RIGHT-POINTING ANGLE BRACKET
.. |RightArrow|                      unicode:: U+02192 .. RIGHTWARDS ARROW
.. |Rightarrow|                      unicode:: U+021D2 .. RIGHTWARDS DOUBLE ARROW
.. |rightarrow|                      unicode:: U+02192 .. RIGHTWARDS ARROW
.. |RightArrowBar|                   unicode:: U+021E5 .. RIGHTWARDS ARROW TO BAR
.. |RightArrowLeftArrow|             unicode:: U+021C4 .. RIGHTWARDS ARROW OVER LEFTWARDS ARROW
.. |rightarrowtail|                  unicode:: U+021A3 .. RIGHTWARDS ARROW WITH TAIL
.. |RightCeiling|                    unicode:: U+02309 .. RIGHT CEILING
.. |RightDoubleBracket|              unicode:: U+0301B .. RIGHT WHITE SQUARE BRACKET
.. |RightDownVector|                 unicode:: U+021C2 .. DOWNWARDS HARPOON WITH BARB RIGHTWARDS
.. |RightFloor|                      unicode:: U+0230B .. RIGHT FLOOR
.. |rightharpoondown|                unicode:: U+021C1 .. RIGHTWARDS HARPOON WITH BARB DOWNWARDS
.. |rightharpoonup|                  unicode:: U+021C0 .. RIGHTWARDS HARPOON WITH BARB UPWARDS
.. |rightleftarrows|                 unicode:: U+021C4 .. RIGHTWARDS ARROW OVER LEFTWARDS ARROW
.. |rightleftharpoons|               unicode:: U+021CC .. RIGHTWARDS HARPOON OVER LEFTWARDS HARPOON
.. |rightrightarrows|                unicode:: U+021C9 .. RIGHTWARDS PAIRED ARROWS
.. |rightsquigarrow|                 unicode:: U+0219D .. RIGHTWARDS WAVE ARROW
.. |RightTee|                        unicode:: U+022A2 .. RIGHT TACK
.. |RightTeeArrow|                   unicode:: U+021A6 .. RIGHTWARDS ARROW FROM BAR
.. |rightthreetimes|                 unicode:: U+022CC .. RIGHT SEMIDIRECT PRODUCT
.. |RightTriangle|                   unicode:: U+022B3 .. CONTAINS AS NORMAL SUBGROUP
.. |RightTriangleEqual|              unicode:: U+022B5 .. CONTAINS AS NORMAL SUBGROUP OR EQUAL TO
.. |RightUpVector|                   unicode:: U+021BE .. UPWARDS HARPOON WITH BARB RIGHTWARDS
.. |RightVector|                     unicode:: U+021C0 .. RIGHTWARDS HARPOON WITH BARB UPWARDS
.. |risingdotseq|                    unicode:: U+02253 .. IMAGE OF OR APPROXIMATELY EQUAL TO
.. |rmoustache|                      unicode:: U+023B1 .. UPPER RIGHT OR LOWER LEFT CURLY BRACKET SECTION
.. |Rrightarrow|                     unicode:: U+021DB .. RIGHTWARDS TRIPLE ARROW
.. |Rsh|                             unicode:: U+021B1 .. UPWARDS ARROW WITH TIP RIGHTWARDS
.. |searrow|                         unicode:: U+02198 .. SOUTH EAST ARROW
.. |setminus|                        unicode:: U+02216 .. SET MINUS
.. |ShortDownArrow|                  unicode:: U+02193 .. DOWNWARDS ARROW
.. |ShortLeftArrow|                  unicode:: U+02190 .. LEFTWARDS ARROW
.. |shortmid|                        unicode:: U+02223 .. DIVIDES
.. |shortparallel|                   unicode:: U+02225 .. PARALLEL TO
.. |ShortRightArrow|                 unicode:: U+02192 .. RIGHTWARDS ARROW
.. |ShortUpArrow|                    unicode:: U+02191 .. UPWARDS ARROW
.. |simeq|                           unicode:: U+02243 .. ASYMPTOTICALLY EQUAL TO
.. |SmallCircle|                     unicode:: U+02218 .. RING OPERATOR
.. |smallsetminus|                   unicode:: U+02216 .. SET MINUS
.. |spadesuit|                       unicode:: U+02660 .. BLACK SPADE SUIT
.. |Sqrt|                            unicode:: U+0221A .. SQUARE ROOT
.. |sqsubset|                        unicode:: U+0228F .. SQUARE IMAGE OF
.. |sqsubseteq|                      unicode:: U+02291 .. SQUARE IMAGE OF OR EQUAL TO
.. |sqsupset|                        unicode:: U+02290 .. SQUARE ORIGINAL OF
.. |sqsupseteq|                      unicode:: U+02292 .. SQUARE ORIGINAL OF OR EQUAL TO
.. |Square|                          unicode:: U+025A1 .. WHITE SQUARE
.. |SquareIntersection|              unicode:: U+02293 .. SQUARE CAP
.. |SquareSubset|                    unicode:: U+0228F .. SQUARE IMAGE OF
.. |SquareSubsetEqual|               unicode:: U+02291 .. SQUARE IMAGE OF OR EQUAL TO
.. |SquareSuperset|                  unicode:: U+02290 .. SQUARE ORIGINAL OF
.. |SquareSupersetEqual|             unicode:: U+02292 .. SQUARE ORIGINAL OF OR EQUAL TO
.. |SquareUnion|                     unicode:: U+02294 .. SQUARE CUP
.. |Star|                            unicode:: U+022C6 .. STAR OPERATOR
.. |straightepsilon|                 unicode:: U+003F5 .. GREEK LUNATE EPSILON SYMBOL
.. |straightphi|                     unicode:: U+003D5 .. GREEK PHI SYMBOL
.. |Subset|                          unicode:: U+022D0 .. DOUBLE SUBSET
.. |subset|                          unicode:: U+02282 .. SUBSET OF
.. |subseteq|                        unicode:: U+02286 .. SUBSET OF OR EQUAL TO
.. |subseteqq|                       unicode:: U+02AC5 .. SUBSET OF ABOVE EQUALS SIGN
.. |SubsetEqual|                     unicode:: U+02286 .. SUBSET OF OR EQUAL TO
.. |subsetneq|                       unicode:: U+0228A .. SUBSET OF WITH NOT EQUAL TO
.. |subsetneqq|                      unicode:: U+02ACB .. SUBSET OF ABOVE NOT EQUAL TO
.. |succ|                            unicode:: U+0227B .. SUCCEEDS
.. |succapprox|                      unicode:: U+02AB8 .. SUCCEEDS ABOVE ALMOST EQUAL TO
.. |succcurlyeq|                     unicode:: U+0227D .. SUCCEEDS OR EQUAL TO
.. |Succeeds|                        unicode:: U+0227B .. SUCCEEDS
.. |SucceedsEqual|                   unicode:: U+02AB0 .. SUCCEEDS ABOVE SINGLE-LINE EQUALS SIGN
.. |SucceedsSlantEqual|              unicode:: U+0227D .. SUCCEEDS OR EQUAL TO
.. |SucceedsTilde|                   unicode:: U+0227F .. SUCCEEDS OR EQUIVALENT TO
.. |succeq|                          unicode:: U+02AB0 .. SUCCEEDS ABOVE SINGLE-LINE EQUALS SIGN
.. |succnapprox|                     unicode:: U+02ABA .. SUCCEEDS ABOVE NOT ALMOST EQUAL TO
.. |succneqq|                        unicode:: U+02AB6 .. SUCCEEDS ABOVE NOT EQUAL TO
.. |succnsim|                        unicode:: U+022E9 .. SUCCEEDS BUT NOT EQUIVALENT TO
.. |succsim|                         unicode:: U+0227F .. SUCCEEDS OR EQUIVALENT TO
.. |SuchThat|                        unicode:: U+0220B .. CONTAINS AS MEMBER
.. |Sum|                             unicode:: U+02211 .. N-ARY SUMMATION
.. |Superset|                        unicode:: U+02283 .. SUPERSET OF
.. |SupersetEqual|                   unicode:: U+02287 .. SUPERSET OF OR EQUAL TO
.. |Supset|                          unicode:: U+022D1 .. DOUBLE SUPERSET
.. |supset|                          unicode:: U+02283 .. SUPERSET OF
.. |supseteq|                        unicode:: U+02287 .. SUPERSET OF OR EQUAL TO
.. |supseteqq|                       unicode:: U+02AC6 .. SUPERSET OF ABOVE EQUALS SIGN
.. |supsetneq|                       unicode:: U+0228B .. SUPERSET OF WITH NOT EQUAL TO
.. |supsetneqq|                      unicode:: U+02ACC .. SUPERSET OF ABOVE NOT EQUAL TO
.. |swarrow|                         unicode:: U+02199 .. SOUTH WEST ARROW
.. |Therefore|                       unicode:: U+02234 .. THEREFORE
.. |therefore|                       unicode:: U+02234 .. THEREFORE
.. |thickapprox|                     unicode:: U+02248 .. ALMOST EQUAL TO
.. |thicksim|                        unicode:: U+0223C .. TILDE OPERATOR
.. |ThinSpace|                       unicode:: U+02009 .. THIN SPACE
.. |Tilde|                           unicode:: U+0223C .. TILDE OPERATOR
.. |TildeEqual|                      unicode:: U+02243 .. ASYMPTOTICALLY EQUAL TO
.. |TildeFullEqual|                  unicode:: U+02245 .. APPROXIMATELY EQUAL TO
.. |TildeTilde|                      unicode:: U+02248 .. ALMOST EQUAL TO
.. |toea|                            unicode:: U+02928 .. NORTH EAST ARROW AND SOUTH EAST ARROW
.. |tosa|                            unicode:: U+02929 .. SOUTH EAST ARROW AND SOUTH WEST ARROW
.. |triangle|                        unicode:: U+025B5 .. WHITE UP-POINTING SMALL TRIANGLE
.. |triangledown|                    unicode:: U+025BF .. WHITE DOWN-POINTING SMALL TRIANGLE
.. |triangleleft|                    unicode:: U+025C3 .. WHITE LEFT-POINTING SMALL TRIANGLE
.. |trianglelefteq|                  unicode:: U+022B4 .. NORMAL SUBGROUP OF OR EQUAL TO
.. |triangleq|                       unicode:: U+0225C .. DELTA EQUAL TO
.. |triangleright|                   unicode:: U+025B9 .. WHITE RIGHT-POINTING SMALL TRIANGLE
.. |trianglerighteq|                 unicode:: U+022B5 .. CONTAINS AS NORMAL SUBGROUP OR EQUAL TO
.. |TripleDot|                       unicode:: U+020DB .. COMBINING THREE DOTS ABOVE
.. |twoheadleftarrow|                unicode:: U+0219E .. LEFTWARDS TWO HEADED ARROW
.. |twoheadrightarrow|               unicode:: U+021A0 .. RIGHTWARDS TWO HEADED ARROW
.. |ulcorner|                        unicode:: U+0231C .. TOP LEFT CORNER
.. |Union|                           unicode:: U+022C3 .. N-ARY UNION
.. |UnionPlus|                       unicode:: U+0228E .. MULTISET UNION
.. |UpArrow|                         unicode:: U+02191 .. UPWARDS ARROW
.. |Uparrow|                         unicode:: U+021D1 .. UPWARDS DOUBLE ARROW
.. |uparrow|                         unicode:: U+02191 .. UPWARDS ARROW
.. |UpArrowDownArrow|                unicode:: U+021C5 .. UPWARDS ARROW LEFTWARDS OF DOWNWARDS ARROW
.. |UpDownArrow|                     unicode:: U+02195 .. UP DOWN ARROW
.. |Updownarrow|                     unicode:: U+021D5 .. UP DOWN DOUBLE ARROW
.. |updownarrow|                     unicode:: U+02195 .. UP DOWN ARROW
.. |UpEquilibrium|                   unicode:: U+0296E .. UPWARDS HARPOON WITH BARB LEFT BESIDE DOWNWARDS HARPOON WITH BARB RIGHT
.. |upharpoonleft|                   unicode:: U+021BF .. UPWARDS HARPOON WITH BARB LEFTWARDS
.. |upharpoonright|                  unicode:: U+021BE .. UPWARDS HARPOON WITH BARB RIGHTWARDS
.. |UpperLeftArrow|                  unicode:: U+02196 .. NORTH WEST ARROW
.. |UpperRightArrow|                 unicode:: U+02197 .. NORTH EAST ARROW
.. |upsilon|                         unicode:: U+003C5 .. GREEK SMALL LETTER UPSILON
.. |UpTee|                           unicode:: U+022A5 .. UP TACK
.. |UpTeeArrow|                      unicode:: U+021A5 .. UPWARDS ARROW FROM BAR
.. |upuparrows|                      unicode:: U+021C8 .. UPWARDS PAIRED ARROWS
.. |urcorner|                        unicode:: U+0231D .. TOP RIGHT CORNER
.. |varepsilon|                      unicode:: U+003B5 .. GREEK SMALL LETTER EPSILON
.. |varkappa|                        unicode:: U+003F0 .. GREEK KAPPA SYMBOL
.. |varnothing|                      unicode:: U+02205 .. EMPTY SET
.. |varphi|                          unicode:: U+003C6 .. GREEK SMALL LETTER PHI
.. |varpi|                           unicode:: U+003D6 .. GREEK PI SYMBOL
.. |varpropto|                       unicode:: U+0221D .. PROPORTIONAL TO
.. |varrho|                          unicode:: U+003F1 .. GREEK RHO SYMBOL
.. |varsigma|                        unicode:: U+003C2 .. GREEK SMALL LETTER FINAL SIGMA
.. |varsubsetneq|                    unicode:: U+0228A U+0FE00 .. SUBSET OF WITH NOT EQUAL TO - variant with stroke through bottom members
.. |varsubsetneqq|                   unicode:: U+02ACB U+0FE00 .. SUBSET OF ABOVE NOT EQUAL TO - variant with stroke through bottom members
.. |varsupsetneq|                    unicode:: U+0228B U+0FE00 .. SUPERSET OF WITH NOT EQUAL TO - variant with stroke through bottom members
.. |varsupsetneqq|                   unicode:: U+02ACC U+0FE00 .. SUPERSET OF ABOVE NOT EQUAL TO - variant with stroke through bottom members
.. |vartheta|                        unicode:: U+003D1 .. GREEK THETA SYMBOL
.. |vartriangleleft|                 unicode:: U+022B2 .. NORMAL SUBGROUP OF
.. |vartriangleright|                unicode:: U+022B3 .. CONTAINS AS NORMAL SUBGROUP
.. |Vee|                             unicode:: U+022C1 .. N-ARY LOGICAL OR
.. |vee|                             unicode:: U+02228 .. LOGICAL OR
.. |Vert|                            unicode:: U+02016 .. DOUBLE VERTICAL LINE
.. |vert|                            unicode:: U+0007C .. VERTICAL LINE
.. |VerticalBar|                     unicode:: U+02223 .. DIVIDES
.. |VerticalTilde|                   unicode:: U+02240 .. WREATH PRODUCT
.. |VeryThinSpace|                   unicode:: U+0200A .. HAIR SPACE
.. |Wedge|                           unicode:: U+022C0 .. N-ARY LOGICAL AND
.. |wedge|                           unicode:: U+02227 .. LOGICAL AND
.. |wp|                              unicode:: U+02118 .. SCRIPT CAPITAL P
.. |wr|                              unicode:: U+02240 .. WREATH PRODUCT
.. |zeetrf|                          unicode:: U+02128 .. BLACK-LETTER CAPITAL Z
