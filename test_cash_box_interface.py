"""
اختبار شاشة صندوق النقدية بعد إصلاح سندات الصرف
This script tests the cash box screen to verify that withdrawal vouchers appear correctly
"""

import sys
from PyQt6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QMessageBox
from PyQt6.QtCore import Qt

# Import the fixed cash box screen
from cash_box_screen_fixed import شاشة_صندوق_النقدية

# Create a dummy user for testing
class DummyUser:
    def __init__(self):
        self.id = 1
        self.username = "admin"
        self.role = "admin"
        self.name = "Administrator"

class TestCashBoxWindow(QMainWindow):
    """نافذة اختبار شاشة صندوق النقدية"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        
    def init_ui(self):
        self.setWindowTitle("اختبار شاشة صندوق النقدية")
        self.setGeometry(100, 100, 1200, 800)
        
        # إنشاء مستخدم وهمي للاختبار
        user = DummyUser()
        
        # إنشاء شاشة صندوق النقدية
        self.cash_box_screen = شاشة_صندوق_النقدية(user)
        
        # تعيين الشاشة كمحتوى رئيسي
        self.setCentralWidget(self.cash_box_screen)
        
        # إضافة رسالة ترحيب
        self.show_welcome_message()
        
    def show_welcome_message(self):
        """عرض رسالة ترحيب وتعليمات الاختبار"""
        QMessageBox.information(self, "مرحباً", 
            "مرحباً بك في اختبار شاشة صندوق النقدية!\n\n"
            "يمكنك الآن:\n"
            "1. مشاهدة سندات الصرف في الجدول\n"
            "2. البحث بين الحركات المختلفة\n"
            "3. إضافة حركات نقدية جديدة\n"
            "4. التحقق من أن سندات الصرف تظهر بشكل صحيح\n\n"
            "ملاحظة: تأكد من وجود سندات صرف في قاعدة البيانات لرؤية النتائج.")

def main():
    """الدالة الرئيسية لتشغيل اختبار الشاشة"""
    app = QApplication(sys.argv)
    
    # إعداد الخط للنصوص العربية
    app.setLayoutDirection(Qt.LayoutDirection.RightToLeft)
    
    # إنشاء نافذة الاختبار
    window = TestCashBoxWindow()
    window.show()
    
    # تشغيل التطبيق
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
