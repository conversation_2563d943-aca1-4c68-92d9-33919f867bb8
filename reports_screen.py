from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QTabWidget,
                            QPushButton, QLabel, QComboBox, QFormLayout,
                            QHBoxLayout, QTableWidget, QTableWidgetItem,
                            QDateEdit, QHeaderView, QMessageBox,
                            QGroupBox, QGridLayout, QFileDialog, QLineEdit)
from PyQt6.QtPrintSupport import QPrinter
from PyQt6.QtCore import Qt, QDate, QUrl
from PyQt6.QtGui import QFont, QTextDocument, QDesktopServices, QColor
from sqlalchemy import create_engine, func
from sqlalchemy.orm import sessionmaker
from datetime import datetime
import os
import xlsxwriter
import logging
import traceback

# Configure logging
logging.basicConfig(
    filename='error_log.txt',
    level=logging.ERROR,
    format='%(asctime)s - %(levelname)s - %(message)s',
    encoding='utf-8'
)

from database import Sale, Purchase, Receipt, Customer, Supplier, JournalEntry, CompanyInfo, OpeningBalance
from translations import get_translation as _

class ReportsScreen(QWidget):
    def __init__(self, user):
        super().__init__()
        self.user = user
        self.init_db()
        self.init_ui()

    def init_db(self):
        # إنشاء اتصال بقاعدة البيانات
        engine = create_engine('sqlite:///diamond_sales.db')
        Session = sessionmaker(bind=engine)
        self.session = Session()

    def init_ui(self):
        self.setWindowTitle(_("reports_title", "نظام إدارة مبيعات الألماس - التقارير"))
        self.setGeometry(100, 100, 1000, 600)
        self.setLayoutDirection(Qt.LayoutDirection.RightToLeft)

        # تعريف أنماط الأزرار المشتركة
        self.btn_style = """
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 5px;
                font-size: 12pt;
                font-weight: bold;
                padding: 8px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:pressed {
                background-color: #1c6ea4;
            }
        """
        
        self.btn_update_style = self.btn_style.replace("3498db", "27ae60")  # لون أخضر لأزرار التحديث
        self.btn_print_style = self.btn_style  # لون أزرق افتراضي لأزرار الطباعة
        self.btn_export_style = self.btn_style.replace("3498db", "e67e22")  # لون برتقالي لأزرار التصدير

        # Create main layout
        main_layout = QVBoxLayout(self)

        # Create tab widget
        self.tab_widget = QTabWidget()

        # Create tabs
        self.sales_report_tab = QWidget()
        self.purchases_report_tab = QWidget()
        self.financial_report_tab = QWidget()
        self.customer_report_tab = QWidget()  # إضافة تاب تقرير العملاء
        self.supplier_report_tab = QWidget()  # إضافة تاب تقرير الموردين
        self.inventory_movement_tab = QWidget()  # إضافة تاب تقرير حركة الأصناف
        self.vouchers_report_tab = QWidget()  # إضافة تاب تقرير السندات

        # Setup tabs
        self.setup_sales_report_tab()
        self.setup_purchases_report_tab()
        self.setup_financial_report_tab()
        self.setup_customer_report_tab()  # إعداد تاب تقرير العملاء
        self.setup_supplier_report_tab()  # إعداد تاب تقرير الموردين
        self.setup_inventory_movement_tab()  # إعداد تاب تقرير حركة الأصناف
        self.setup_vouchers_report_tab()  # إعداد تاب تقرير السندات

        # Add tabs to tab widget
        self.tab_widget.addTab(self.sales_report_tab, "تقرير المبيعات")
        self.tab_widget.addTab(self.purchases_report_tab, "تقرير المشتريات")
        self.tab_widget.addTab(self.financial_report_tab, "التقرير المالي")
        self.tab_widget.addTab(self.customer_report_tab, "تقارير العملاء")
        self.tab_widget.addTab(self.supplier_report_tab, "تقارير الموردين")
        self.tab_widget.addTab(self.inventory_movement_tab, "تقرير حركة الأصناف")
        self.tab_widget.addTab(self.vouchers_report_tab, "تقارير السندات")

        # Add tab widget to main layout
        main_layout.addWidget(self.tab_widget)

    def setup_sales_report_tab(self):
        layout = QVBoxLayout(self.sales_report_tab)

        # عنوان التقرير
        title_label = QLabel("تقرير المبيعات")
        title_font = QFont()
        title_font.setPointSize(14)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title_label)

        # فلاتر التقرير
        filter_layout = QHBoxLayout()

        # تاريخ البداية
        start_date_layout = QFormLayout()
        self.sales_start_date = QDateEdit()
        self.sales_start_date.setDate(QDate.currentDate().addMonths(-1))
        self.sales_start_date.setCalendarPopup(True)
        start_date_layout.addRow("من تاريخ:", self.sales_start_date)
        filter_layout.addLayout(start_date_layout)

        # تاريخ النهاية
        end_date_layout = QFormLayout()
        self.sales_end_date = QDateEdit()
        self.sales_end_date.setDate(QDate.currentDate())
        self.sales_end_date.setCalendarPopup(True)
        end_date_layout.addRow("إلى تاريخ:", self.sales_end_date)
        filter_layout.addLayout(end_date_layout)

        # فلتر العميل
        customer_layout = QFormLayout()
        self.sales_customer_combo = QComboBox()
        self.sales_customer_combo.addItem("جميع العملاء", -1)
        customers = self.session.query(Customer).all()
        for customer in customers:
            self.sales_customer_combo.addItem(customer.name, customer.id)
        customer_layout.addRow("العميل:", self.sales_customer_combo)
        filter_layout.addLayout(customer_layout)

        # زر تحديث التقرير
        self.update_sales_report_btn = QPushButton("تحديث التقرير")
        self.update_sales_report_btn.setStyleSheet(self.btn_update_style)
        self.update_sales_report_btn.clicked.connect(self.update_sales_report)
        filter_layout.addWidget(self.update_sales_report_btn)

        # زر طباعة التقرير
        self.print_sales_report_btn = QPushButton("طباعة التقرير")
        self.print_sales_report_btn.setStyleSheet(self.btn_print_style)
        self.print_sales_report_btn.clicked.connect(lambda: self.print_report(self.generate_sales_report_html()))
        filter_layout.addWidget(self.print_sales_report_btn)

        # زر تصدير HTML
        self.export_sales_html_btn = QPushButton("تصدير HTML")
        self.export_sales_html_btn.setStyleSheet(self.btn_export_style)
        self.export_sales_html_btn.clicked.connect(self.export_sales_report_html)
        filter_layout.addWidget(self.export_sales_html_btn)

        layout.addLayout(filter_layout)

        # جدول تقرير المبيعات
        self.sales_table = QTableWidget()
        self.sales_table.setColumnCount(9)
        self.sales_table.setHorizontalHeaderLabels([
            "رقم الفاتورة", "العميل", "نوع الألماس", "الوزن (قيراط)",
            "السعر للقيراط ($)", "السعر الإجمالي ($)", "السعر الإجمالي (ريال)",
            "تاريخ البيع", "المبلغ المدفوع"
        ])
        self.sales_table.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        layout.addWidget(self.sales_table)

        # ملخص المبيعات
        summary_layout = QHBoxLayout()
        self.sales_count_label = QLabel("عدد المبيعات: 0")
        self.sales_total_usd_label = QLabel("إجمالي المبيعات ($): 0.00")
        self.sales_total_sar_label = QLabel("إجمالي المبيعات (ريال): 0.00")

        summary_layout.addWidget(self.sales_count_label)
        summary_layout.addWidget(self.sales_total_usd_label)
        summary_layout.addWidget(self.sales_total_sar_label)
        layout.addLayout(summary_layout)

        # تحديث التقرير الأولي
        self.update_sales_report()

    def setup_purchases_report_tab(self):
        layout = QVBoxLayout(self.purchases_report_tab)

        # عنوان التقرير
        title_label = QLabel("تقرير المشتريات")
        title_font = QFont()
        title_font.setPointSize(14)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title_label)

        # فلاتر التقرير
        filter_layout = QHBoxLayout()

        # تاريخ البداية
        start_date_layout = QFormLayout()
        self.purchases_start_date = QDateEdit()
        self.purchases_start_date.setDate(QDate.currentDate().addMonths(-1))
        self.purchases_start_date.setCalendarPopup(True)
        start_date_layout.addRow("من تاريخ:", self.purchases_start_date)
        filter_layout.addLayout(start_date_layout)

        # تاريخ النهاية
        end_date_layout = QFormLayout()
        self.purchases_end_date = QDateEdit()
        self.purchases_end_date.setDate(QDate.currentDate())
        self.purchases_end_date.setCalendarPopup(True)
        end_date_layout.addRow("إلى تاريخ:", self.purchases_end_date)
        filter_layout.addLayout(end_date_layout)

        # فلتر المورد
        supplier_layout = QFormLayout()
        self.purchases_supplier_combo = QComboBox()
        self.purchases_supplier_combo.addItem("جميع الموردين", -1)
        suppliers = self.session.query(Supplier).all()
        for supplier in suppliers:
            self.purchases_supplier_combo.addItem(supplier.name, supplier.id)
        supplier_layout.addRow("المورد:", self.purchases_supplier_combo)
        filter_layout.addLayout(supplier_layout)

        # زر تحديث التقرير
        self.update_purchases_report_btn = QPushButton("تحديث التقرير")
        self.update_purchases_report_btn.setStyleSheet(self.btn_update_style)
        self.update_purchases_report_btn.clicked.connect(self.update_purchases_report)
        filter_layout.addWidget(self.update_purchases_report_btn)

        # زر طباعة التقرير
        self.print_purchases_report_btn = QPushButton("طباعة التقرير")
        self.print_purchases_report_btn.setStyleSheet(self.btn_print_style)
        self.print_purchases_report_btn.clicked.connect(lambda: self.print_report(self.generate_purchases_report_html()))
        filter_layout.addWidget(self.print_purchases_report_btn)

        # زر تصدير HTML
        self.export_purchases_html_btn = QPushButton("تصدير HTML")
        self.export_purchases_html_btn.setStyleSheet(self.btn_export_style)
        self.export_purchases_html_btn.clicked.connect(self.export_purchases_report_html)
        filter_layout.addWidget(self.export_purchases_html_btn)

        layout.addLayout(filter_layout)

        # جدول تقرير المشتريات
        self.purchases_table = QTableWidget()
        self.purchases_table.setColumnCount(9)
        self.purchases_table.setHorizontalHeaderLabels([
            "رقم الفاتورة", "المورد", "نوع الألماس", "الوزن (قيراط)",
            "السعر للقيراط ($)", "السعر الإجمالي ($)", "السعر الإجمالي (ريال)",
            "تاريخ الشراء", "المبلغ المدفوع"
        ])
        self.purchases_table.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        layout.addWidget(self.purchases_table)

        # ملخص المشتريات
        summary_layout = QHBoxLayout()
        self.purchases_count_label = QLabel("عدد المشتريات: 0")
        self.purchases_total_usd_label = QLabel("إجمالي المشتريات ($): 0.00")
        self.purchases_total_sar_label = QLabel("إجمالي المشتريات (ريال): 0.00")

        summary_layout.addWidget(self.purchases_count_label)
        summary_layout.addWidget(self.purchases_total_usd_label)
        summary_layout.addWidget(self.purchases_total_sar_label)
        layout.addLayout(summary_layout)

        # تحديث التقرير الأولي
        self.update_purchases_report()

    def setup_financial_report_tab(self):
        layout = QVBoxLayout(self.financial_report_tab)

        # عنوان التقرير
        title_label = QLabel("التقرير المالي")
        title_font = QFont()
        title_font.setPointSize(14)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title_label)

        # فلاتر التقرير
        filter_layout = QHBoxLayout()

        # تاريخ البداية
        start_date_layout = QFormLayout()
        self.financial_start_date = QDateEdit()
        self.financial_start_date.setDate(QDate.currentDate().addMonths(-1))
        self.financial_start_date.setCalendarPopup(True)
        start_date_layout.addRow("من تاريخ:", self.financial_start_date)
        filter_layout.addLayout(start_date_layout)

        # تاريخ النهاية
        end_date_layout = QFormLayout()
        self.financial_end_date = QDateEdit()
        self.financial_end_date.setDate(QDate.currentDate())
        self.financial_end_date.setCalendarPopup(True)
        end_date_layout.addRow("إلى تاريخ:", self.financial_end_date)
        filter_layout.addLayout(end_date_layout)

        # زر تحديث التقرير
        self.update_financial_report_btn = QPushButton("تحديث التقرير")
        self.update_financial_report_btn.setStyleSheet(self.btn_update_style)
        self.update_financial_report_btn.clicked.connect(self.update_financial_report)
        filter_layout.addWidget(self.update_financial_report_btn)

        # زر طباعة التقرير
        self.print_financial_report_btn = QPushButton("طباعة التقرير")
        self.print_financial_report_btn.setStyleSheet(self.btn_print_style)
        self.print_financial_report_btn.clicked.connect(lambda: self.print_report(self.generate_financial_report_html()))
        filter_layout.addWidget(self.print_financial_report_btn)

        # زر تصدير HTML
        self.export_financial_html_btn = QPushButton("تصدير HTML")
        self.export_financial_html_btn.setStyleSheet(self.btn_export_style)
        self.export_financial_html_btn.clicked.connect(self.export_financial_report_html)
        filter_layout.addWidget(self.export_financial_html_btn)

        layout.addLayout(filter_layout)

        # ملخص مالي
        self.financial_summary = QTableWidget()
        self.financial_summary.setColumnCount(3)
        self.financial_summary.setHorizontalHeaderLabels(["البيان", "المبلغ ($)", "المبلغ (ريال)"])
        self.financial_summary.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        self.financial_summary.setRowCount(6)

        # تعيين أسماء الصفوف
        self.financial_summary.setVerticalHeaderLabels([
            "المخزون الافتتاحي",
            "إجمالي المبيعات",
            "إجمالي المشتريات",
            "صافي الربح",
            "المبالغ المستحقة (مدينون)",
            "المبالغ المستحقة (دائنون)"
        ])

        layout.addWidget(self.financial_summary)

        # تحديث التقرير المالي
        self.update_financial_report()

    def setup_customer_report_tab(self):
        layout = QVBoxLayout(self.customer_report_tab)

        # Customer selection group
        selection_group = QGroupBox("اختيار العميل")
        selection_layout = QHBoxLayout()

        self.customer_combo = QComboBox()
        self.load_customers()
        selection_layout.addWidget(QLabel("العميل:"))
        selection_layout.addWidget(self.customer_combo)

        # Add date filters
        self.customer_start_date = QDateEdit()
        self.customer_start_date.setDate(QDate.currentDate().addMonths(-1))
        self.customer_start_date.setCalendarPopup(True)
        selection_layout.addWidget(QLabel("من تاريخ:"))
        selection_layout.addWidget(self.customer_start_date)

        self.customer_end_date = QDateEdit()
        self.customer_end_date.setDate(QDate.currentDate())
        self.customer_end_date.setCalendarPopup(True)
        selection_layout.addWidget(QLabel("إلى تاريخ:"))
        selection_layout.addWidget(self.customer_end_date)

        generate_btn = QPushButton("عرض التقرير")
        generate_btn.setStyleSheet(self.btn_update_style)
        generate_btn.clicked.connect(self.generate_customer_report)
        selection_layout.addWidget(generate_btn)

        # Add print and export HTML buttons
        print_btn = QPushButton("طباعة التقرير")
        print_btn.setStyleSheet(self.btn_print_style)
        print_btn.clicked.connect(lambda: self.print_report(self.generate_customer_report_html()))
        selection_layout.addWidget(print_btn)

        export_btn = QPushButton("تصدير HTML")
        export_btn.setStyleSheet(self.btn_export_style)
        export_btn.clicked.connect(self.export_customer_report_html)
        selection_layout.addWidget(export_btn)

        selection_group.setLayout(selection_layout)
        layout.addWidget(selection_group)

        # Report display area
        self.customer_report_table = QTableWidget()
        self.customer_report_table.setColumnCount(9)  # تم تقليل عدد الأعمدة بعد حذف أعمدة المبلغ المستحق
        self.customer_report_table.setHorizontalHeaderLabels([
            "الوزن", "نوع الألماس", "سعر القيراط ($)", "السعر بالريال للكمية",
            "المبلغ المسدد ($)", "المبلغ المسدد (ريال)",
            "الرصيد ($)", "الرصيد (ريال)", "ملاحظات"
        ])
        self.customer_report_table.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        layout.addWidget(self.customer_report_table)
        
        # Totals group
        totals_group = QGroupBox("الإجماليات")
        totals_layout = QGridLayout()
        
        self.customer_total_weight_label = QLabel("0")
        self.customer_total_due_label = QLabel("0")
        self.customer_total_paid_label = QLabel("0")
        self.customer_total_balance_label = QLabel("0")
        
        totals_layout.addWidget(QLabel("إجمالي الوزن:"), 0, 0)
        totals_layout.addWidget(self.customer_total_weight_label, 0, 1)
        totals_layout.addWidget(QLabel("إجمالي المستحق:"), 0, 2)
        totals_layout.addWidget(self.customer_total_due_label, 0, 3)
        totals_layout.addWidget(QLabel("إجمالي المسدد:"), 1, 0)
        totals_layout.addWidget(self.customer_total_paid_label, 1, 1)
        totals_layout.addWidget(QLabel("إجمالي الرصيد:"), 1, 2)
        totals_layout.addWidget(self.customer_total_balance_label, 1, 3)

        totals_group.setLayout(totals_layout)
        layout.addWidget(totals_group)
        
    def load_customers(self):
        customers = self.session.query(Customer).all()
        self.customer_combo.clear()
        # إضافة خيار "الكل" في بداية القائمة
        self.customer_combo.addItem("الكل", -1)
        for customer in customers:
            self.customer_combo.addItem(customer.name, customer.id)
            
    def generate_customer_report(self):
        customer_id = self.customer_combo.currentData()
        
        # إذا تم اختيار "الكل"، عرض تقرير الأرصدة الإجمالية
        if customer_id == -1 and self.customer_combo.currentText() == "الكل":
            self.generate_all_customers_summary()
            return
        
        if customer_id == -1 or customer_id is None:
            QMessageBox.warning(self, "تنبيه", "الرجاء اختيار عميل")
            return

        # Get the date range
        start_date = self.customer_start_date.date().toPyDate()
        end_date = self.customer_end_date.date().toPyDate()
        # تعديل النهاية لتشمل كامل اليوم
        end_date_time = datetime.combine(end_date, datetime.max.time())

        try:
            # Get all sales for this customer within the date range and order by date
            sales = self.session.query(Sale).filter_by(customer_id=customer_id).filter(
                Sale.sale_date >= start_date,
                Sale.sale_date <= end_date_time
            ).order_by(Sale.sale_date).all()

            # Get all receipts without reference for this customer within the date range
            receipts_without_ref = self.session.query(Receipt).filter_by(
                customer_id=customer_id,
                sale_id=None,
                receipt_type="CashIn"
            ).filter(
                Receipt.issue_date >= start_date,
                Receipt.issue_date <= end_date_time
            ).order_by(Receipt.issue_date).all()

            # الحصول على الرصيد الافتتاحي للعميل
            customer = self.session.query(Customer).filter_by(id=customer_id).first()
            opening_balance_usd = customer.opening_balance if customer and customer.opening_balance else 0.0
            opening_balance_sar = opening_balance_usd * 3.75  # تحويل إلى ريال
            
            if not sales and not receipts_without_ref:
                # إذا لم توجد معاملات ولكن يوجد رصيد افتتاحي، اعرض الرصيد الافتتاحي
                if opening_balance_usd != 0:
                    self.customer_report_table.setRowCount(1)
                    # إضافة صف الرصيد الافتتاحي
                    self.customer_report_table.setItem(0, 0, QTableWidgetItem("0.000"))  # الوزن
                    self.customer_report_table.setItem(0, 1, QTableWidgetItem("رصيد افتتاحي"))  # نوع الماس
                    self.customer_report_table.setItem(0, 2, QTableWidgetItem("0.000"))  # السعر للقيراط
                    self.customer_report_table.setItem(0, 3, QTableWidgetItem("0.000"))  # المبلغ بالريال
                    self.customer_report_table.setItem(0, 4, QTableWidgetItem("0.000"))  # المدفوع بالدولار
                    self.customer_report_table.setItem(0, 5, QTableWidgetItem("0.000"))  # المدفوع بالريال
                    self.customer_report_table.setItem(0, 6, QTableWidgetItem(f"{opening_balance_usd:.3f}"))  # الرصيد بالدولار
                    self.customer_report_table.setItem(0, 7, QTableWidgetItem(f"{opening_balance_sar:.3f}"))  # الرصيد بالريال
                    self.customer_report_table.setItem(0, 8, QTableWidgetItem("رصيد افتتاحي للعميل"))  # الملاحظات
                    
                    # تحديث الملصقات
                    self.customer_total_weight_label.setText("0.00")
                    self.customer_total_due_label.setText("0.00")
                    self.customer_total_paid_label.setText("0.00")
                    self.customer_total_balance_label.setText(f"{opening_balance_usd:.3f}")
                    return
                else:
                    QMessageBox.information(self, "معلومات", "لا توجد معاملات أو رصيد افتتاحي لهذا العميل في الفترة المحددة")
                    self.customer_report_table.setRowCount(0)
                    self.customer_total_weight_label.setText("0.00")
                    self.customer_total_due_label.setText("0.00")
                    self.customer_total_paid_label.setText("0.00")
                    self.customer_total_balance_label.setText("0.00")
                    return

            # دمج المبيعات وسندات القبض بدون مرجع في قائمة واحدة للعرض
            all_transactions = []

            # إضافة المبيعات
            for sale in sales:
                all_transactions.append({
                    'type': 'sale',
                    'date': sale.sale_date,
                    'data': sale
                })

            # إضافة سندات القبض بدون مرجع
            for receipt in receipts_without_ref:
                all_transactions.append({
                    'type': 'receipt',
                    'date': receipt.issue_date,
                    'data': receipt
                })

            # ترتيب جميع المعاملات حسب التاريخ
            all_transactions.sort(key=lambda x: x['date'] if x['date'] else datetime.min)

            # تحديث عدد الصفوف في الجدول
            self.customer_report_table.setRowCount(len(all_transactions))

            total_weight = 0
            total_due = 0
            total_paid = 0
            total_balance = 0
            opening_balance_sar = opening_balance_usd * 3.75  # تحويل إلى ريال
            
            # متغيرات للرصيد التراكمي (تبدأ بالرصيد الافتتاحي)
            cumulative_balance_usd = opening_balance_usd
            cumulative_balance_sar = opening_balance_sar

            # إضافة صف للرصيد الافتتاحي إذا كان أكبر من صفر
            row_offset = 0
            if opening_balance_usd != 0:
                self.customer_report_table.setRowCount(len(all_transactions) + 1)
                # إضافة صف الرصيد الافتتاحي
                self.customer_report_table.setItem(0, 0, QTableWidgetItem("0.000"))  # الوزن
                self.customer_report_table.setItem(0, 1, QTableWidgetItem("رصيد افتتاحي"))  # نوع الماس
                self.customer_report_table.setItem(0, 2, QTableWidgetItem("0.000"))  # السعر للقيراط
                self.customer_report_table.setItem(0, 3, QTableWidgetItem("0.000"))  # المبلغ بالريال
                self.customer_report_table.setItem(0, 4, QTableWidgetItem("0.000"))  # المدفوع بالدولار
                self.customer_report_table.setItem(0, 5, QTableWidgetItem("0.000"))  # المدفوع بالريال
                self.customer_report_table.setItem(0, 6, QTableWidgetItem(f"{opening_balance_usd:.3f}"))  # الرصيد بالدولار
                self.customer_report_table.setItem(0, 7, QTableWidgetItem(f"{opening_balance_sar:.3f}"))  # الرصيد بالريال
                self.customer_report_table.setItem(0, 8, QTableWidgetItem("رصيد افتتاحي للعميل"))  # الملاحظات
                row_offset = 1
            else:
                self.customer_report_table.setRowCount(len(all_transactions))

            for i, transaction in enumerate(all_transactions):
                row = i + row_offset
                if transaction['type'] == 'sale':
                    # معاملة بيع
                    sale = transaction['data']

                    # التحقق من القيم قبل إضافتها
                    weight = sale.carat_weight if sale.carat_weight is not None else 0
                    price_per_carat = sale.price_per_carat_usd if sale.price_per_carat_usd is not None else 0
                    exchange_rate = sale.exchange_rate if sale.exchange_rate is not None else 3.75
                    amount_due_usd = sale.total_price_usd if sale.total_price_usd is not None else 0
                    amount_due_sar = sale.total_price_sar if sale.total_price_sar is not None else 0
                    amount_paid_usd = sale.amount_paid if sale.amount_paid is not None else 0
                    amount_paid_sar = amount_paid_usd * exchange_rate

                    # حساب الرصيد لهذه العملية
                    transaction_balance_usd = amount_due_usd - amount_paid_usd
                    transaction_balance_sar = amount_due_sar - amount_paid_sar

                    # تحديث الرصيد التراكمي
                    cumulative_balance_usd += transaction_balance_usd
                    cumulative_balance_sar += transaction_balance_sar

                    # Display with 3 decimal places for weight and price per carat
                    self.customer_report_table.setItem(row, 0, QTableWidgetItem(f"{weight:.3f}"))
                    self.customer_report_table.setItem(row, 1, QTableWidgetItem(str(sale.diamond_type or "")))
                    self.customer_report_table.setItem(row, 2, QTableWidgetItem(f"{price_per_carat:.3f}"))
                    self.customer_report_table.setItem(row, 3, QTableWidgetItem(f"{amount_due_sar:.3f}"))
                    # حذف أعمدة المبلغ المستحق
                    self.customer_report_table.setItem(row, 4, QTableWidgetItem(f"{amount_paid_usd:.3f}"))
                    self.customer_report_table.setItem(row, 5, QTableWidgetItem(f"{amount_paid_sar:.3f}"))

                    # عرض الرصيد التراكمي بدلاً من رصيد العملية الفردية
                    self.customer_report_table.setItem(row, 6, QTableWidgetItem(f"{cumulative_balance_usd:.3f}"))
                    self.customer_report_table.setItem(row, 7, QTableWidgetItem(f"{cumulative_balance_sar:.3f}"))                    # إضافة التاريخ في عمود الملاحظات
                    sale_date_str = sale.sale_date.strftime('%Y-%m-%d') if sale.sale_date else ""
                    notes = sale.notes if sale.notes else ""
                    display_notes = f"{sale_date_str} - {notes}" if notes else sale_date_str
                    self.customer_report_table.setItem(row, 8, QTableWidgetItem(display_notes))
                    
                    total_weight += weight
                    total_due += amount_due_usd
                    total_paid += amount_paid_usd
                else:
                    # سند قبض بدون مرجع
                    receipt = transaction['data']

                    try:
                        # التحقق من القيم قبل إضافتها
                        exchange_rate = receipt.amount_sar / receipt.amount_usd if receipt.amount_usd and receipt.amount_usd > 0 else 3.75
                        amount_paid_usd = receipt.amount_usd if receipt.amount_usd is not None else 0
                        amount_paid_sar = receipt.amount_sar if receipt.amount_sar is not None else 0

                        # سندات القبض بدون مرجع تخصم من الرصيد التراكمي
                        cumulative_balance_usd -= amount_paid_usd
                        cumulative_balance_sar -= amount_paid_sar

                        # عرض سند القبض في الجدول
                        self.customer_report_table.setItem(row, 0, QTableWidgetItem("0.000"))  # لا يوجد وزن
                        self.customer_report_table.setItem(row, 1, QTableWidgetItem("سند قبض"))  # نوع المعاملة
                        self.customer_report_table.setItem(row, 2, QTableWidgetItem("0.000"))  # لا يوجد سعر للقيراط
                        self.customer_report_table.setItem(row, 3, QTableWidgetItem("0.000"))  # لا يوجد مبلغ مستحق
                        self.customer_report_table.setItem(row, 4, QTableWidgetItem(f"{amount_paid_usd:.3f}"))  # المبلغ المدفوع بالدولار
                        self.customer_report_table.setItem(row, 5, QTableWidgetItem(f"{amount_paid_sar:.3f}"))  # المبلغ المدفوع بالريال

                        # عرض الرصيد التراكمي
                        self.customer_report_table.setItem(row, 6, QTableWidgetItem(f"{cumulative_balance_usd:.3f}"))
                        self.customer_report_table.setItem(row, 7, QTableWidgetItem(f"{cumulative_balance_sar:.3f}"))

                        # إضافة التاريخ في عمود الملاحظات
                        receipt_date = receipt.issue_date.strftime('%Y-%m-%d') if receipt.issue_date else ""
                        display_notes = f"{receipt_date} - سند قبض رقم {receipt.id}"
                        self.customer_report_table.setItem(row, 8, QTableWidgetItem(display_notes))

                        # تحديث إجمالي المبالغ المدفوعة
                        total_paid += amount_paid_usd
                    except Exception as e:
                        import logging
                        logging.error(f"Error processing receipt {receipt.id}: {str(e)}")
                        # Continue with the next transaction instead of stopping the whole report

            # الرصيد الإجمالي هو نفسه الرصيد التراكمي النهائي
            total_balance = cumulative_balance_usd

            # Format and update totals
            self.customer_total_weight_label.setText(f"{total_weight:.2f}")
            self.customer_total_due_label.setText(f"{total_due:.2f}")
            self.customer_total_paid_label.setText(f"{total_paid:.2f}")
            self.customer_total_balance_label.setText(f"{total_balance:.2f}")
            
        except Exception as e:
            import logging
            import traceback
            error_msg = str(e)
            stack_trace = traceback.format_exc()
            logging.error(f"Error generating customer report: {error_msg}\n{stack_trace}")
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تحميل البيانات: {error_msg}")

    def generate_all_customers_summary(self):
        """عرض تقرير الأرصدة الإجمالية لجميع العملاء"""
        try:
            # الحصول على جميع العملاء
            customers = self.session.query(Customer).all()
            
            if not customers:
                QMessageBox.information(self, "معلومات", "لا يوجد عملاء في النظام")
                self.customer_report_table.setRowCount(0)
                return
            
            # إعداد الجدول لعرض ملخص العملاء
            self.customer_report_table.setRowCount(len(customers))
            
            total_weight_all = 0
            total_due_all = 0
            total_paid_all = 0
            total_balance_all = 0
            
            for row, customer in enumerate(customers):
                # حساب إجمالي المبيعات للعميل
                sales = self.session.query(Sale).filter_by(customer_id=customer.id).all()
                
                # حساب إجمالي سندات القبض للعميل
                receipts = self.session.query(Receipt).filter_by(
                    customer_id=customer.id,
                    receipt_type="CashIn"
                ).all()
                
                # حساب الإجماليات للعميل
                customer_weight = sum(sale.carat_weight for sale in sales if sale.carat_weight)
                customer_due = sum(sale.total_price_usd for sale in sales if sale.total_price_usd)
                customer_paid = sum(receipt.amount_usd for receipt in receipts if receipt.amount_usd)
                customer_opening_balance = customer.opening_balance if customer.opening_balance else 0
                customer_balance = customer_due + customer_opening_balance - customer_paid
                
                # إضافة البيانات إلى الجدول
                self.customer_report_table.setItem(row, 0, QTableWidgetItem(f"{customer_weight:.3f}"))
                self.customer_report_table.setItem(row, 1, QTableWidgetItem(customer.name))
                self.customer_report_table.setItem(row, 2, QTableWidgetItem("-"))
                self.customer_report_table.setItem(row, 3, QTableWidgetItem(f"{customer_due:.3f}"))
                self.customer_report_table.setItem(row, 4, QTableWidgetItem(f"{customer_paid:.3f}"))
                self.customer_report_table.setItem(row, 5, QTableWidgetItem(f"{customer_paid * 3.75:.3f}"))
                self.customer_report_table.setItem(row, 6, QTableWidgetItem(f"{customer_balance:.3f}"))
                self.customer_report_table.setItem(row, 7, QTableWidgetItem(f"{customer_balance * 3.75:.3f}"))
                self.customer_report_table.setItem(row, 8, QTableWidgetItem(f"رصيد افتتاحي: {customer_opening_balance:.3f}"))
                
                # إضافة إلى الإجماليات العامة
                total_weight_all += customer_weight
                total_due_all += customer_due
                total_paid_all += customer_paid
                total_balance_all += customer_balance
            
            # تحديث ملصقات الإجماليات
            self.customer_total_weight_label.setText(f"{total_weight_all:.2f}")
            self.customer_total_due_label.setText(f"{total_due_all:.2f}")
            self.customer_total_paid_label.setText(f"{total_paid_all:.2f}")
            self.customer_total_balance_label.setText(f"{total_balance_all:.2f}")
            
        except Exception as e:
            import logging
            import traceback
            error_msg = str(e)
            stack_trace = traceback.format_exc()
            logging.error(f"Error generating all customers summary: {error_msg}\n{stack_trace}")
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تحميل البيانات: {error_msg}")

    def setup_supplier_report_tab(self):
        layout = QVBoxLayout(self.supplier_report_tab)

        # Supplier selection group
        selection_group = QGroupBox("اختيار المورد")
        selection_layout = QHBoxLayout()

        self.supplier_combo = QComboBox()
        self.load_suppliers()
        selection_layout.addWidget(QLabel("المورد:"))
        selection_layout.addWidget(self.supplier_combo)

        # Add date filters
        self.supplier_start_date = QDateEdit()
        self.supplier_start_date.setDate(QDate.currentDate().addMonths(-1))
        self.supplier_start_date.setCalendarPopup(True)
        selection_layout.addWidget(QLabel("من تاريخ:"))
        selection_layout.addWidget(self.supplier_start_date)

        self.supplier_end_date = QDateEdit()
        self.supplier_end_date.setDate(QDate.currentDate())
        self.supplier_end_date.setCalendarPopup(True)
        selection_layout.addWidget(QLabel("إلى تاريخ:"))
        selection_layout.addWidget(self.supplier_end_date)

        generate_btn = QPushButton("عرض التقرير")
        generate_btn.setStyleSheet(self.btn_update_style)
        generate_btn.clicked.connect(self.generate_supplier_report)
        selection_layout.addWidget(generate_btn)

        # Add print and export HTML buttons
        print_btn = QPushButton("طباعة التقرير")
        print_btn.setStyleSheet(self.btn_print_style)
        print_btn.clicked.connect(lambda: self.print_report(self.generate_supplier_report_html()))
        selection_layout.addWidget(print_btn)

        export_btn = QPushButton("تصدير HTML")
        export_btn.setStyleSheet(self.btn_export_style)
        export_btn.clicked.connect(self.export_supplier_report_html)
        selection_layout.addWidget(export_btn)

        selection_group.setLayout(selection_layout)
        layout.addWidget(selection_group)

        # Report display area
        self.supplier_report_table = QTableWidget()
        self.supplier_report_table.setColumnCount(10)  # تم تقليل عدد الأعمدة بعد حذف أعمدة المبلغ المستحق
        self.supplier_report_table.setHorizontalHeaderLabels([
            "الوزن", "نوع الألماس", "سعر القيراط ($)", "السعر بالريال للكمية",
            "المبلغ المسدد ($)", "المبلغ المسدد (ريال)",
            "الرصيد ($)", "الرصيد التراكمي (ريال)",
            "التاريخ", "نوع المستند"
        ])
        self.supplier_report_table.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        layout.addWidget(self.supplier_report_table)
        
        # Totals group
        totals_group = QGroupBox("الإجماليات")
        totals_layout = QGridLayout()
        
        self.supplier_total_weight_label = QLabel("0")
        self.supplier_total_due_label = QLabel("0")
        self.supplier_total_paid_label = QLabel("0")
        self.supplier_total_balance_label = QLabel("0")
        
        totals_layout.addWidget(QLabel("إجمالي الوزن:"), 0, 0)
        totals_layout.addWidget(self.supplier_total_weight_label, 0, 1)
        totals_layout.addWidget(QLabel("إجمالي المستحق:"), 0, 2)
        totals_layout.addWidget(self.supplier_total_due_label, 0, 3)
        totals_layout.addWidget(QLabel("إجمالي المسدد:"), 1, 0)
        totals_layout.addWidget(self.supplier_total_paid_label, 1, 1)
        totals_layout.addWidget(QLabel("إجمالي الرصيد:"), 1, 2)
        totals_layout.addWidget(self.supplier_total_balance_label, 1, 3)

        totals_group.setLayout(totals_layout)
        layout.addWidget(totals_group)
        
    def load_suppliers(self):
        suppliers = self.session.query(Supplier).all()
        self.supplier_combo.clear()
        # إضافة خيار "الكل" في بداية القائمة
        self.supplier_combo.addItem("الكل", -1)
        for supplier in suppliers:
            self.supplier_combo.addItem(supplier.name, supplier.id)
    
    def load_diamond_types_for_filter(self):
        """تحميل أنواع الألماس من جدول الأصناف لفلتر التقرير"""
        try:
            from database import Category
            
            # مسح القائمة الحالية
            self.inventory_diamond_type.clear()
            
            # إضافة خيار "جميع الأنواع"
            self.inventory_diamond_type.addItem("جميع الأنواع")
            
            # تحميل الأصناف من قاعدة البيانات
            categories = self.session.query(Category).all()
            
            # إضافة كل صنف إلى القائمة
            for category in categories:
                self.inventory_diamond_type.addItem(category.name)
                
        except Exception as e:
            # في حالة حدوث خطأ، استخدم القيم الافتراضية
            self.inventory_diamond_type.clear()
            self.inventory_diamond_type.addItem("جميع الأنواع")
            self.inventory_diamond_type.addItem("الماس مدور")
            self.inventory_diamond_type.addItem("باجيت")
            print(f"خطأ في تحميل أنواع الألماس: {str(e)}")
            
    def generate_supplier_report(self):
        """دالة لإنشاء تقرير المورد"""
        supplier_id = self.supplier_combo.currentData()
        
        # إذا تم اختيار "الكل"، عرض تقرير الأرصدة الإجمالية
        if supplier_id == -1 and self.supplier_combo.currentText() == "الكل":
            self.generate_all_suppliers_summary()
            return
        
        if supplier_id == -1 or supplier_id is None:
            QMessageBox.warning(self, "تنبيه", "الرجاء اختيار مورد")
            return

        # الحصول على نطاق التاريخ
        start_date = self.supplier_start_date.date().toPyDate()
        end_date = self.supplier_end_date.date().toPyDate()
        # تعديل النهاية لتشمل كامل اليوم
        end_date_time = datetime.combine(end_date, datetime.max.time())

        # الحصول على جميع المشتريات لهذا المورد ضمن نطاق التاريخ
        purchases = self.session.query(Purchase).filter_by(supplier_id=supplier_id).filter(
            Purchase.purchase_date >= start_date,
            Purchase.purchase_date <= end_date_time
        ).order_by(Purchase.purchase_date).all()

        # الحصول على جميع سندات الصرف بدون مرجع لهذا المورد ضمن نطاق التاريخ
        receipts_without_ref = self.session.query(Receipt).filter_by(
            supplier_id=supplier_id,
            purchase_id=None,
            receipt_type="CashOut"
        ).filter(
            Receipt.issue_date >= start_date,
            Receipt.issue_date <= end_date_time
        ).order_by(Receipt.issue_date).all()

        # الحصول على كل سندات الصرف المرتبطة بالمشتريات
        purchase_ids = [p.id for p in purchases]
        receipts_with_ref = []
        if purchase_ids:
            receipts_with_ref = self.session.query(Receipt).filter(
                Receipt.purchase_id.in_(purchase_ids),
                Receipt.receipt_type == "CashOut",
                Receipt.issue_date >= start_date,
                Receipt.issue_date <= end_date_time
            ).all()

        # الحصول على الرصيد الافتتاحي للمورد
        supplier = self.session.query(Supplier).filter_by(id=supplier_id).first()
        opening_balance_usd = supplier.opening_balance if supplier and supplier.opening_balance else 0.0
        opening_balance_sar = opening_balance_usd * 3.75  # تحويل إلى ريال
        
        if not purchases and not receipts_without_ref:
            # إذا لم توجد معاملات ولكن يوجد رصيد افتتاحي، اعرض الرصيد الافتتاحي
            if opening_balance_usd != 0:
                self.supplier_report_table.setRowCount(1)
                # إضافة صف الرصيد الافتتاحي
                self.supplier_report_table.setItem(0, 0, QTableWidgetItem("0.000"))  # الوزن
                self.supplier_report_table.setItem(0, 1, QTableWidgetItem("رصيد افتتاحي"))  # نوع الماس
                self.supplier_report_table.setItem(0, 2, QTableWidgetItem("0.000"))  # السعر للقيراط
                self.supplier_report_table.setItem(0, 3, QTableWidgetItem("0.000"))  # المبلغ بالريال
                self.supplier_report_table.setItem(0, 4, QTableWidgetItem("0.000"))  # المدفوع بالدولار
                self.supplier_report_table.setItem(0, 5, QTableWidgetItem("0.000"))  # المدفوع بالريال
                self.supplier_report_table.setItem(0, 6, QTableWidgetItem(f"{opening_balance_usd:.3f}"))  # الرصيد بالدولار
                self.supplier_report_table.setItem(0, 7, QTableWidgetItem(f"{opening_balance_sar:.3f}"))  # الرصيد بالريال
                self.supplier_report_table.setItem(0, 8, QTableWidgetItem(""))  # التاريخ
                self.supplier_report_table.setItem(0, 9, QTableWidgetItem("رصيد افتتاحي للمورد"))  # نوع المستند
                
                # تحديث الملصقات
                self.supplier_total_weight_label.setText("0.00")
                self.supplier_total_due_label.setText("0.00")
                self.supplier_total_paid_label.setText("0.00")
                self.supplier_total_balance_label.setText(f"{opening_balance_usd:.3f}")
                return
            else:
                QMessageBox.information(self, "معلومات", "لا توجد معاملات أو رصيد افتتاحي لهذا المورد في الفترة المحددة")
                self.supplier_report_table.setRowCount(0)
                self.supplier_total_weight_label.setText("0.00")
                self.supplier_total_due_label.setText("0.00")
                self.supplier_total_paid_label.setText("0.00")
                self.supplier_total_balance_label.setText("0.00")
                return

        try:
            # تجميع المشتريات وسندات الصرف بدون مرجع وترتيبها حسب التاريخ
            all_transactions = []

            # إضافة المشتريات
            for purchase in purchases:
                transaction_date = purchase.purchase_date
                all_transactions.append({
                    'type': 'purchase',
                    'date': transaction_date,
                    'data': purchase
                })

            # إضافة سندات الصرف بدون مرجع
            for receipt in receipts_without_ref:
                transaction_date = receipt.issue_date
                all_transactions.append({
                    'type': 'receipt',
                    'date': transaction_date,
                    'data': receipt
                })

            # ترتيب المعاملات حسب التاريخ
            all_transactions.sort(key=lambda x: x['date'] if x['date'] else datetime.min)

            # تحديث عدد الصفوف في الجدول
            self.supplier_report_table.setRowCount(len(all_transactions))

            total_weight = 0
            total_due = 0
            total_paid = 0
            total_balance = 0
            opening_balance_sar = opening_balance_usd * 3.75  # تحويل إلى ريال
            
            # متغيرات لحساب الرصيد التراكمي (تبدأ بالرصيد الافتتاحي)
            cumulative_balance_usd = opening_balance_usd
            cumulative_balance_sar = opening_balance_sar

            # إضافة صف للرصيد الافتتاحي إذا كان أكبر من صفر
            row_offset = 0
            if opening_balance_usd != 0:
                self.supplier_report_table.setRowCount(len(all_transactions) + 1)
                # إضافة صف الرصيد الافتتاحي
                self.supplier_report_table.setItem(0, 0, QTableWidgetItem("0.000"))  # الوزن
                self.supplier_report_table.setItem(0, 1, QTableWidgetItem("رصيد افتتاحي"))  # نوع الماس
                self.supplier_report_table.setItem(0, 2, QTableWidgetItem("0.000"))  # السعر للقيراط
                self.supplier_report_table.setItem(0, 3, QTableWidgetItem("0.000"))  # المبلغ بالريال
                self.supplier_report_table.setItem(0, 4, QTableWidgetItem("0.000"))  # المدفوع بالدولار
                self.supplier_report_table.setItem(0, 5, QTableWidgetItem("0.000"))  # المدفوع بالريال
                self.supplier_report_table.setItem(0, 6, QTableWidgetItem(f"{opening_balance_usd:.3f}"))  # الرصيد بالدولار
                self.supplier_report_table.setItem(0, 7, QTableWidgetItem(f"{opening_balance_sar:.3f}"))  # الرصيد بالريال
                self.supplier_report_table.setItem(0, 8, QTableWidgetItem(""))  # التاريخ
                self.supplier_report_table.setItem(0, 9, QTableWidgetItem("رصيد افتتاحي للمورد"))  # نوع المستند
                row_offset = 1
            else:
                self.supplier_report_table.setRowCount(len(all_transactions))

            for i, transaction in enumerate(all_transactions):
                row = i + row_offset
                if transaction['type'] == 'purchase':
                    purchase = transaction['data']

                    # التحقق من القيم قبل إضافتها
                    weight = purchase.carat_weight if purchase.carat_weight is not None else 0
                    price_per_carat = purchase.price_per_carat_usd if purchase.price_per_carat_usd is not None else 0
                    exchange_rate = purchase.exchange_rate if purchase.exchange_rate is not None else 3.75
                    amount_due_usd = purchase.total_price_usd if purchase.total_price_usd is not None else 0
                    amount_due_sar = purchase.total_price_sar if purchase.total_price_sar is not None else 0
                    amount_paid_usd = purchase.amount_paid if purchase.amount_paid is not None else 0
                    amount_paid_sar = amount_paid_usd * exchange_rate
                    balance_usd = amount_due_usd - amount_paid_usd
                    balance_sar = amount_due_sar - amount_paid_sar

                    # تحديث الرصيد التراكمي
                    cumulative_balance_usd += balance_usd
                    cumulative_balance_sar += balance_sar

                    # تحديث الإجماليات
                    total_weight += weight
                    total_due += amount_due_usd
                    total_paid += amount_paid_usd
                    total_balance += balance_usd

                    # إضافة البيانات إلى الجدول
                    self.supplier_report_table.setItem(row, 0, QTableWidgetItem(f"{weight:.3f}"))
                    self.supplier_report_table.setItem(row, 1, QTableWidgetItem(str(purchase.diamond_type or "")))
                    self.supplier_report_table.setItem(row, 2, QTableWidgetItem(f"{price_per_carat:.3f}"))
                    self.supplier_report_table.setItem(row, 3, QTableWidgetItem(f"{amount_due_sar:.3f}"))
                    # حذف أعمدة المبلغ المستحق
                    self.supplier_report_table.setItem(row, 4, QTableWidgetItem(f"{amount_paid_usd:.3f}"))
                    self.supplier_report_table.setItem(row, 5, QTableWidgetItem(f"{amount_paid_sar:.3f}"))
                    self.supplier_report_table.setItem(row, 6, QTableWidgetItem(f"{cumulative_balance_usd:.3f}"))  # استخدام الرصيد التراكمي بالدولار
                    self.supplier_report_table.setItem(row, 7, QTableWidgetItem(f"{cumulative_balance_sar:.3f}"))  # استخدام الرصيد التراكمي بالريال
                    self.supplier_report_table.setItem(row, 8, QTableWidgetItem(str(purchase.purchase_date.strftime('%Y-%m-%d') if purchase.purchase_date else "")))
                    self.supplier_report_table.setItem(row, 9, QTableWidgetItem(f"فاتورة مشتريات"))

                elif transaction['type'] == 'receipt':
                    receipt = transaction['data']

                    # القيم الافتراضية للسندات
                    weight = 0
                    price_per_carat = 0
                    exchange_rate = receipt.exchange_rate if hasattr(receipt, 'exchange_rate') and receipt.exchange_rate else 3.75

                    # سند صرف بدون مرجع - المبلغ المدفوع فقط
                    amount_due_usd = 0
                    amount_due_sar = 0
                    amount_paid_usd = receipt.amount_usd if receipt.amount_usd is not None else 0
                    amount_paid_sar = receipt.amount_sar if receipt.amount_sar is not None else 0

                    # الرصيد هو سالب المبلغ المدفوع (لأنه دفعة بدون فاتورة)
                    balance_usd = -amount_paid_usd
                    balance_sar = -amount_paid_sar

                    # تحديث الرصيد التراكمي
                    cumulative_balance_usd += balance_usd
                    cumulative_balance_sar += balance_sar

                    # تحديث الإجماليات
                    total_paid += amount_paid_usd
                    total_balance += balance_usd

                    # إضافة البيانات إلى الجدول مع تمييز سندات الصرف بلون مختلف
                    self.supplier_report_table.setItem(row, 0, QTableWidgetItem("0.000"))
                    self.supplier_report_table.setItem(row, 1, QTableWidgetItem("سند صرف"))  # إضافة اسم السند في عمود نوع الألماس
                    self.supplier_report_table.setItem(row, 2, QTableWidgetItem("0.000"))
                    self.supplier_report_table.setItem(row, 3, QTableWidgetItem("0.000"))
                    self.supplier_report_table.setItem(row, 4, QTableWidgetItem(f"{amount_paid_usd:.3f}"))  # المبلغ المدفوع بالدولار
                    self.supplier_report_table.setItem(row, 5, QTableWidgetItem(f"{amount_paid_sar:.3f}"))  # المبلغ المدفوع بالريال
                    self.supplier_report_table.setItem(row, 6, QTableWidgetItem(f"{cumulative_balance_usd:.3f}"))  # استخدام الرصيد التراكمي بالدولار
                    self.supplier_report_table.setItem(row, 7, QTableWidgetItem(f"{cumulative_balance_sar:.3f}"))  # استخدام الرصيد التراكمي بالريال
                    self.supplier_report_table.setItem(row, 8, QTableWidgetItem(str(receipt.issue_date.strftime('%Y-%m-%d') if receipt.issue_date else "")))
                    self.supplier_report_table.setItem(row, 9, QTableWidgetItem(f"سند صرف بدون مرجع"))
                    # تمييز سندات الصرف بلون مختلف
                    for col in range(self.supplier_report_table.columnCount()):
                        item = self.supplier_report_table.item(row, col)
                        if item:
                            item.setBackground(QColor(230, 230, 250))  # لون خفيف للتمييز

            # إجراء المزيد من الدفعات المرتبطة بالمشتريات
            for purchase in purchases:
                # البحث عن جميع الدفعات الإضافية لكل عملية شراء
                additional_payments = self.get_additional_payments_for_purchase(purchase.id, start_date, end_date_time)
                if additional_payments:
                    for payment in additional_payments:
                        total_paid += payment.amount_usd

            # تحديث الإجماليات في واجهة المستخدم مع التنسيق المناسب
            self.supplier_total_weight_label.setText(f"{total_weight:.3f}")
            self.supplier_total_due_label.setText(f"{total_due:.3f}")
            self.supplier_total_paid_label.setText(f"{total_paid:.3f}")
            self.supplier_total_balance_label.setText(f"{total_balance:.3f}")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تحميل البيانات: {str(e)}")
            with open('error_log.txt', 'a', encoding='utf-8') as log_file:
                log_file.write(f"{datetime.now()}: خطأ في إنشاء تقرير المورد: {str(e)}\n")

    def generate_all_suppliers_summary(self):
        """عرض تقرير الأرصدة الإجمالية لجميع الموردين"""
        try:
            # الحصول على جميع الموردين
            suppliers = self.session.query(Supplier).all()
            
            if not suppliers:
                QMessageBox.information(self, "معلومات", "لا يوجد موردين في النظام")
                self.supplier_report_table.setRowCount(0)
                return
            
            # إعداد الجدول لعرض ملخص الموردين
            self.supplier_report_table.setRowCount(len(suppliers))
            
            total_weight_all = 0
            total_due_all = 0
            total_paid_all = 0
            total_balance_all = 0
            
            for row, supplier in enumerate(suppliers):
                # حساب إجمالي المشتريات للمورد
                purchases = self.session.query(Purchase).filter_by(supplier_id=supplier.id).all()
                
                # حساب إجمالي سندات الصرف للمورد
                receipts = self.session.query(Receipt).filter_by(
                    supplier_id=supplier.id,
                    receipt_type="CashOut"
                ).all()
                
                # حساب الإجماليات للمورد
                supplier_weight = sum(purchase.carat_weight for purchase in purchases if purchase.carat_weight)
                supplier_due = sum(purchase.total_price_usd for purchase in purchases if purchase.total_price_usd)
                supplier_paid = sum(receipt.amount_usd for receipt in receipts if receipt.amount_usd)
                supplier_opening_balance = supplier.opening_balance if supplier.opening_balance else 0
                supplier_balance = supplier_due + supplier_opening_balance - supplier_paid
                
                # إضافة البيانات إلى الجدول
                self.supplier_report_table.setItem(row, 0, QTableWidgetItem(f"{supplier_weight:.3f}"))
                self.supplier_report_table.setItem(row, 1, QTableWidgetItem(supplier.name))
                self.supplier_report_table.setItem(row, 2, QTableWidgetItem("-"))
                self.supplier_report_table.setItem(row, 3, QTableWidgetItem(f"{supplier_due:.3f}"))
                self.supplier_report_table.setItem(row, 4, QTableWidgetItem(f"{supplier_paid:.3f}"))
                self.supplier_report_table.setItem(row, 5, QTableWidgetItem(f"{supplier_paid * 3.75:.3f}"))
                self.supplier_report_table.setItem(row, 6, QTableWidgetItem(f"{supplier_balance:.3f}"))
                self.supplier_report_table.setItem(row, 7, QTableWidgetItem(f"{supplier_balance * 3.75:.3f}"))
                self.supplier_report_table.setItem(row, 8, QTableWidgetItem("-"))
                self.supplier_report_table.setItem(row, 9, QTableWidgetItem(f"رصيد افتتاحي: {supplier_opening_balance:.3f}"))
                
                # إضافة إلى الإجماليات العامة
                total_weight_all += supplier_weight
                total_due_all += supplier_due
                total_paid_all += supplier_paid
                total_balance_all += supplier_balance
            
            # تحديث ملصقات الإجماليات
            self.supplier_total_weight_label.setText(f"{total_weight_all:.2f}")
            self.supplier_total_due_label.setText(f"{total_due_all:.2f}")
            self.supplier_total_paid_label.setText(f"{total_paid_all:.2f}")
            self.supplier_total_balance_label.setText(f"{total_balance_all:.2f}")
            
        except Exception as e:
            import logging
            import traceback
            error_msg = str(e)
            stack_trace = traceback.format_exc()
            logging.error(f"Error generating all suppliers summary: {error_msg}\n{stack_trace}")
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تحميل البيانات: {error_msg}")
                
    def get_additional_payments_for_purchase(self, purchase_id, start_date, end_date):
        """
        Obtener pagos adicionales para una compra específica dentro de un rango de fechas
        
        Args:
            purchase_id: ID de la compra
            start_date: Fecha de inicio del rango
            end_date: Fecha de fin del rango
            
        Returns:
            Lista de recibos de pagos adicionales
        """
        try:
            # Buscar todos los recibos relacionados con esta compra en el rango de fechas especificado
            payments = self.session.query(Receipt).filter(
                Receipt.purchase_id == purchase_id,
                Receipt.receipt_type == "CashOut",
                Receipt.issue_date >= start_date,
                Receipt.issue_date <= end_date
            ).all()
            
            return payments
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء البحث عن المدفوعات: {str(e)}")
            with open('error_log.txt', 'a', encoding='utf-8') as log_file:
                log_file.write(f"{datetime.now()}: خطأ في الحصول على دفعات إضافية للشراء {purchase_id}: {str(e)}\n")
            return []
                
    def get_total_paid_for_purchase(self, purchase_id):
        total_paid = 0
        try:
            # Buscar todos los recibos relacionados con esta compra
            payments = self.session.query(Receipt).filter_by(
                purchase_id=purchase_id, 
                receipt_type="CashOut"
            ).all()
            
            # Sumar todos los pagos
            for payment in payments:
                total_paid += payment.amount_usd if payment.amount_usd is not None else 0
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تحميل المدفوعات: {str(e)}")
            with open('error_log.txt', 'a', encoding='utf-8') as log_file:
                log_file.write(f"{datetime.now()}: خطأ في حساب المبالغ المدفوعة للشراء {purchase_id}: {str(e)}\n")
                
        return total_paid

    def update_sales_report(self):
        start_date = self.sales_start_date.date().toPyDate()
        end_date = self.sales_end_date.date().toPyDate()
        # تعديل النهاية لتشمل كامل اليوم
        end_date_time = datetime.combine(end_date, datetime.max.time())

        # إنشاء استعلام
        query = self.session.query(Sale).filter(
            Sale.sale_date >= start_date,
            Sale.sale_date <= end_date_time
        )

        # تطبيق فلتر العميل
        customer_id = self.sales_customer_combo.currentData()
        if customer_id != -1:
            query = query.filter(Sale.customer_id == customer_id)

        # الحصول على النتائج وتحديث الجدول
        sales = query.all()

        self.sales_table.setRowCount(len(sales))

        total_usd = 0
        total_sar = 0

        for row, sale in enumerate(sales):
            self.sales_table.setItem(row, 0, QTableWidgetItem(str(sale.id)))
            self.sales_table.setItem(row, 1, QTableWidgetItem(sale.customer.name if sale.customer else ""))
            self.sales_table.setItem(row, 2, QTableWidgetItem(sale.diamond_type))
            self.sales_table.setItem(row, 3, QTableWidgetItem(str(sale.carat_weight)))
            self.sales_table.setItem(row, 4, QTableWidgetItem(f"{sale.price_per_carat_usd:.2f}"))
            self.sales_table.setItem(row, 5, QTableWidgetItem(f"{sale.total_price_usd:.2f}"))
            self.sales_table.setItem(row, 6, QTableWidgetItem(f"{sale.total_price_sar:.2f}"))
            self.sales_table.setItem(row, 7, QTableWidgetItem(sale.sale_date.strftime('%Y-%m-%d')))
            self.sales_table.setItem(row, 8, QTableWidgetItem(f"{sale.amount_paid:.2f}"))

            total_usd += sale.total_price_usd
            total_sar += sale.total_price_sar
            
        # تحديث ملخص التقرير
        self.sales_count_label.setText(f"عدد المبيعات: {len(sales)}")
        self.sales_total_usd_label.setText(f"إجمالي المبيعات ($): {total_usd:.2f}")
        self.sales_total_sar_label.setText(f"إجمالي المبيعات (ريال): {total_sar:.2f}")
        
    def update_purchases_report(self):
        """تحديث تقرير المشتريات بناءً على المعايير المحددة"""
        try:
            start_date = self.purchases_start_date.date().toPyDate()
            end_date = self.purchases_end_date.date().toPyDate()
            # تعديل النهاية لتشمل كامل اليوم
            end_date_time = datetime.combine(end_date, datetime.max.time())

            # إنشاء استعلام
            query = self.session.query(Purchase).filter(
                Purchase.purchase_date >= start_date,
                Purchase.purchase_date <= end_date_time
            )

            # تطبيق فلتر المورد
            supplier_id = self.purchases_supplier_combo.currentData()
            if supplier_id != -1:
                query = query.filter(Purchase.supplier_id == supplier_id)

            # الحصول على النتائج وتحديث الجدول
            purchases = query.order_by(Purchase.purchase_date).all()

            self.purchases_table.setRowCount(len(purchases))

            total_usd = 0
            total_sar = 0
            total_paid = 0
            total_due = 0

            for row, purchase in enumerate(purchases):
                # حساب المبلغ المدفوع الفعلي (مع دفعات إضافية محتملة)
                amount_paid = purchase.amount_paid or 0
                
                # إضافة البيانات إلى الجدول
                self.purchases_table.setItem(row, 0, QTableWidgetItem(str(purchase.id)))
                self.purchases_table.setItem(row, 1, QTableWidgetItem(purchase.supplier.name if purchase.supplier else ""))
                self.purchases_table.setItem(row, 2, QTableWidgetItem(purchase.diamond_type))
                self.purchases_table.setItem(row, 3, QTableWidgetItem(f"{purchase.carat_weight:.3f}"))
                self.purchases_table.setItem(row, 4, QTableWidgetItem(f"{purchase.price_per_carat_usd:.3f}"))
                self.purchases_table.setItem(row, 5, QTableWidgetItem(f"{purchase.total_price_usd:.3f}"))
                self.purchases_table.setItem(row, 6, QTableWidgetItem(f"{purchase.total_price_sar:.3f}"))
                self.purchases_table.setItem(row, 7, QTableWidgetItem(purchase.purchase_date.strftime('%Y-%m-%d')))
                self.purchases_table.setItem(row, 8, QTableWidgetItem(f"{amount_paid:.3f}"))

                # تجميع الإجماليات
                total_usd += purchase.total_price_usd
                total_sar += purchase.total_price_sar
                total_paid += amount_paid
                total_due += (purchase.total_price_usd - amount_paid)

            # تحديث ملخص التقرير
            self.purchases_count_label.setText(f"عدد المشتريات: {len(purchases)}")
            self.purchases_total_usd_label.setText(f"إجمالي المشتريات ($): {total_usd:.3f}")
            self.purchases_total_sar_label.setText(f"إجمالي المشتريات (ريال): {total_sar:.3f}")
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تحديث تقرير المشتريات: {str(e)}")
            with open('error_log.txt', 'a', encoding='utf-8') as log_file:
                log_file.write(f"{datetime.now()}: خطأ في تحديث تقرير المشتريات: {str(e)}\n")

    def update_financial_report(self):
        start_date = self.financial_start_date.date().toPyDate()
        end_date = self.financial_end_date.date().toPyDate()
        # تعديل النهاية لتشمل كامل اليوم
        end_date_time = datetime.combine(end_date, datetime.max.time())

        # استعلام المبيعات
        sales_query = self.session.query(Sale).filter(
            Sale.sale_date >= start_date,
            Sale.sale_date <= end_date_time
        )

        # استعلام المشتريات
        purchases_query = self.session.query(Purchase).filter(
            Purchase.purchase_date >= start_date,
            Purchase.purchase_date <= end_date_time
        )

        # حساب المخزون الافتتاحي
        opening_inventory_query = self.session.query(OpeningBalance).filter(
            OpeningBalance.date_created <= end_date_time
        )
        opening_inventory_usd = sum(balance.total_value_usd for balance in opening_inventory_query.all())
        opening_inventory_sar = opening_inventory_usd * 3.75  # تحويل إلى ريال

        # إجمالي المبيعات
        total_sales_usd = sum(sale.total_price_usd for sale in sales_query.all())
        total_sales_sar = sum(sale.total_price_sar for sale in sales_query.all())

        # إجمالي المشتريات
        total_purchases_usd = sum(purchase.total_price_usd for purchase in purchases_query.all())
        total_purchases_sar = sum(purchase.total_price_sar for purchase in purchases_query.all())

        # صافي الربح = إجمالي المبيعات - (إجمالي المشتريات + المخزون الافتتاحي)
        net_profit_usd = total_sales_usd - (total_purchases_usd + opening_inventory_usd)
        net_profit_sar = total_sales_sar - (total_purchases_sar + opening_inventory_sar)

        # المبالغ المستحقة (مدينون) - تشمل الأرصدة الافتتاحية للعملاء
        receivables_usd = sum(sale.total_price_usd - sale.amount_paid for sale in sales_query.all())
        receivables_sar = sum(sale.total_price_sar - sale.amount_paid for sale in sales_query.all())
        
        # إضافة الأرصدة الافتتاحية للعملاء
        customers_opening_balances = self.session.query(Customer).all()
        total_customers_opening_balance = sum(customer.opening_balance for customer in customers_opening_balances if customer.opening_balance)
        receivables_usd += total_customers_opening_balance
        receivables_sar += total_customers_opening_balance * 3.75

        # المبالغ المستحقة (دائنون) - تشمل الأرصدة الافتتاحية للموردين
        payables_usd = sum(purchase.total_price_usd - purchase.amount_paid for purchase in purchases_query.all())
        payables_sar = sum(purchase.total_price_sar - purchase.amount_paid for purchase in purchases_query.all())
        
        # إضافة الأرصدة الافتتاحية للموردين
        suppliers_opening_balances = self.session.query(Supplier).all()
        total_suppliers_opening_balance = sum(supplier.opening_balance for supplier in suppliers_opening_balances if supplier.opening_balance)
        payables_usd += total_suppliers_opening_balance
        payables_sar += total_suppliers_opening_balance * 3.75

        # تحديث جدول الملخص المالي
        # تحديث جميع القيم بما في ذلك أسماء البيانات
        self.financial_summary.setItem(0, 0, QTableWidgetItem("المخزون الافتتاحي"))
        self.financial_summary.setItem(0, 1, QTableWidgetItem(f"{opening_inventory_usd:.2f}"))
        self.financial_summary.setItem(0, 2, QTableWidgetItem(f"{opening_inventory_sar:.2f}"))

        self.financial_summary.setItem(1, 0, QTableWidgetItem("إجمالي المبيعات"))
        self.financial_summary.setItem(1, 1, QTableWidgetItem(f"{total_sales_usd:.2f}"))
        self.financial_summary.setItem(1, 2, QTableWidgetItem(f"{total_sales_sar:.2f}"))

        self.financial_summary.setItem(2, 0, QTableWidgetItem("إجمالي المشتريات"))
        self.financial_summary.setItem(2, 1, QTableWidgetItem(f"{total_purchases_usd:.2f}"))
        self.financial_summary.setItem(2, 2, QTableWidgetItem(f"{total_purchases_sar:.2f}"))

        self.financial_summary.setItem(3, 0, QTableWidgetItem("صافي الربح"))
        self.financial_summary.setItem(3, 1, QTableWidgetItem(f"{net_profit_usd:.2f}"))
        self.financial_summary.setItem(3, 2, QTableWidgetItem(f"{net_profit_sar:.2f}"))

        self.financial_summary.setItem(4, 0, QTableWidgetItem("المبالغ المستحقة (مدينون)"))
        self.financial_summary.setItem(4, 1, QTableWidgetItem(f"{receivables_usd:.2f}"))
        self.financial_summary.setItem(4, 2, QTableWidgetItem(f"{receivables_sar:.2f}"))

        self.financial_summary.setItem(5, 0, QTableWidgetItem("المبالغ المستحقة (دائنون)"))
        self.financial_summary.setItem(5, 1, QTableWidgetItem(f"{payables_usd:.2f}"))
        self.financial_summary.setItem(5, 2, QTableWidgetItem(f"{payables_sar:.2f}"))

    def update_customer_report(self):
        start_date = self.customer_start_date.date().toPyDate()
        end_date = self.customer_end_date.date().toPyDate()
        # تعديل النهاية لتشمل كامل اليوم
        end_date_time = datetime.combine(end_date, datetime.max.time())

        # إنشاء استعلام
        query = self.session.query(Customer).filter(
            Customer.created_at >= start_date,
            Customer.created_at <= end_date_time
        )

        # الحصول على النتائج وتحديث الجدول
        customers = query.all()

        self.customer_table.setRowCount(len(customers))

        for row, customer in enumerate(customers):
            self.customer_table.setItem(row, 0, QTableWidgetItem(str(customer.id)))
            self.customer_table.setItem(row, 1, QTableWidgetItem(customer.name))
            self.customer_table.setItem(row, 2, QTableWidgetItem(f"{customer.total_purchases_usd:.2f}"))
            self.customer_table.setItem(row, 3, QTableWidgetItem(f"{customer.total_purchases_sar:.2f}"))
            self.customer_table.setItem(row, 4, QTableWidgetItem(customer.last_purchase_date.strftime('%Y-%m-%d')))

    def update_supplier_report(self):
        start_date = self.supplier_start_date.date().toPyDate()
        end_date = self.supplier_end_date.date().toPyDate()
        # تعديل النهاية لتشمل كامل اليوم
        end_date_time = datetime.combine(end_date, datetime.max.time())

        # إنشاء استعلام
        query = self.session.query(Supplier).filter(
            Supplier.created_at >= start_date,
            Supplier.created_at <= end_date_time
        )

        # الحصول على النتائج وتحديث الجدول
        suppliers = query.all()

        self.supplier_table.setRowCount(len(suppliers))

        for row, supplier in enumerate(suppliers):
            self.supplier_table.setItem(row, 0, QTableWidgetItem(str(supplier.id)))
            self.supplier_table.setItem(row, 1, QTableWidgetItem(supplier.name))
            self.supplier_table.setItem(row, 2, QTableWidgetItem(f"{supplier.total_sales_usd:.2f}"))
            self.supplier_table.setItem(row, 3, QTableWidgetItem(f"{supplier.total_sales_sar:.2f}"))
            self.supplier_table.setItem(row, 4, QTableWidgetItem(supplier.last_sale_date.strftime('%Y-%m-%d')))

    def print_report(self, html_content):
        try:
            if not html_content:
                QMessageBox.warning(self, "تنبيه", "لا توجد بيانات للطباعة")
                return

            # Add print button to HTML content
            print_button_script = """
            <script>
                window.onload = function() {
                    // Add print button
                    var printBtn = document.createElement('button');
                    printBtn.innerHTML = 'طباعة التقرير';
                    printBtn.style.padding = '10px 20px';
                    printBtn.style.backgroundColor = '#3498db';
                    printBtn.style.color = 'white';
                    printBtn.style.border = 'none';
                    printBtn.style.borderRadius = '5px';
                    printBtn.style.cursor = 'pointer';
                    printBtn.style.fontSize = '16px';
                    printBtn.style.margin = '20px auto';
                    printBtn.style.display = 'block';

                    printBtn.onclick = function() {
                        this.style.display = 'none';
                        window.print();
                        setTimeout(function() {
                            printBtn.style.display = 'block';
                        }, 1000);
                    };

                    document.body.insertBefore(printBtn, document.body.firstChild);
                };
            </script>
            """

            # Insert the script before the closing </head> tag
            html_content = html_content.replace('</head>', f'{print_button_script}</head>')

            # Create a temporary HTML file
            import tempfile
            import os

            with tempfile.NamedTemporaryFile(delete=False, suffix='.html', mode='w', encoding='utf-8') as f:
                f.write(html_content)
                temp_path = f.name

            # Open the file in the default browser
            QDesktopServices.openUrl(QUrl.fromLocalFile(os.path.realpath(temp_path)))

            QMessageBox.information(self, "نجاح", "تم فتح التقرير في المتصفح، يمكنك الطباعة من هناك")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء فتح التقرير: {str(e)}")
            with open('error_log.txt', 'a', encoding='utf-8') as log_file:
                log_file.write(f"{datetime.now()}: خطأ في فتح التقرير: {str(e)}\n")

    def generate_sales_report_html(self):
        """Generate enhanced HTML for sales report."""
        try:
            # Get date range
            start_date = self.sales_start_date.date().toString("yyyy-MM-dd")
            end_date = self.sales_end_date.date().toString("yyyy-MM-dd")
            date_range = f"للفترة من {start_date} إلى {end_date}"

            # Build table content
            table_content = """
            <table>
                <thead>
                    <tr>
                        <th>رقم الفاتورة</th>
                        <th>العميل</th>
                        <th>نوع الألماس</th>
                        <th>الوزن (قيراط)</th>
                        <th>السعر للقيراط ($)</th>
                        <th>السعر الإجمالي ($)</th>
                        <th>السعر الإجمالي (ريال)</th>
                        <th>تاريخ البيع</th>
                        <th>المبلغ المدفوع</th>
                    </tr>
                </thead>
                <tbody>
            """

            # Extract data from table
            rows_count = self.sales_table.rowCount()
            for row in range(rows_count):
                invoice_number = self.sales_table.item(row, 0).text()
                customer = self.sales_table.item(row, 1).text()
                diamond_type = self.sales_table.item(row, 2).text()
                weight = self.sales_table.item(row, 3).text()
                price_per_carat = self.sales_table.item(row, 4).text()
                total_price_usd = self.sales_table.item(row, 5).text()
                total_price_sar = self.sales_table.item(row, 6).text()
                sale_date = self.sales_table.item(row, 7).text()
                paid_amount = self.sales_table.item(row, 8).text()

                table_content += f"""
                    <tr>
                        <td>{invoice_number}</td>
                        <td>{customer}</td>
                        <td>{diamond_type}</td>
                        <td>{weight}</td>
                        <td>{price_per_carat}</td>
                        <td>{total_price_usd}</td>
                        <td>{total_price_sar}</td>
                        <td>{sale_date}</td>
                        <td>{paid_amount}</td>
                    </tr>
                """

            # Add table footer
            table_content += """
                </tbody>
            </table>
            """

            # Build summary content
            sales_count = self.sales_count_label.text().split(": ")[1]
            sales_total_usd = self.sales_total_usd_label.text().split(": ")[1]
            sales_total_sar = self.sales_total_sar_label.text().split(": ")[1]

            summary_content = f"""
                <div class="summary-item">
                    <div class="summary-label">عدد المبيعات</div>
                    <div class="summary-value neutral">{sales_count}</div>
                </div>
                <div class="summary-item">
                    <div class="summary-label">إجمالي المبيعات ($)</div>
                    <div class="summary-value positive">{sales_total_usd}</div>
                </div>
                <div class="summary-item">
                    <div class="summary-label">إجمالي المبيعات (ريال)</div>
                    <div class="summary-value positive">{sales_total_sar}</div>
                </div>
            """

            # Generate complete HTML using the template
            html = self.generate_enhanced_html_template(
                title="تقرير المبيعات",
                subtitle="تقرير المبيعات",
                date_range=date_range,
                content=table_content,
                summary=summary_content
            )

            return html
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء إنشاء تقرير المبيعات: {str(e)}")
            with open('error_log.txt', 'a', encoding='utf-8') as log_file:
                log_file.write(f"{datetime.now()}: خطأ في إنشاء HTML لتقرير المبيعات: {str(e)}\n")
            return ""

    def generate_purchases_report_html(self):
        """Generate enhanced HTML for purchases report."""
        try:
            # Get date range
            start_date = self.purchases_start_date.date().toString("yyyy-MM-dd")
            end_date = self.purchases_end_date.date().toString("yyyy-MM-dd")
            date_range = f"للفترة من {start_date} إلى {end_date}"

            # Build table content
            table_content = """
            <table>
                <thead>
                    <tr>
                        <th>رقم الفاتورة</th>
                        <th>المورد</th>
                        <th>نوع الألماس</th>
                        <th>الوزن (قيراط)</th>
                        <th>السعر للقيراط ($)</th>
                        <th>السعر الإجمالي ($)</th>
                        <th>السعر الإجمالي (ريال)</th>
                        <th>تاريخ الشراء</th>
                        <th>المبلغ المدفوع</th>
                    </tr>
                </thead>
                <tbody>
            """

            # Extract data from table
            rows_count = self.purchases_table.rowCount()
            for row in range(rows_count):
                invoice_number = self.purchases_table.item(row, 0).text()
                supplier = self.purchases_table.item(row, 1).text()
                diamond_type = self.purchases_table.item(row, 2).text()
                carat_weight = self.purchases_table.item(row, 3).text()
                price_per_carat = self.purchases_table.item(row, 4).text()
                total_price_usd = self.purchases_table.item(row, 5).text()
                total_price_sar = self.purchases_table.item(row, 6).text()
                purchase_date = self.purchases_table.item(row, 7).text()
                paid_amount = self.purchases_table.item(row, 8).text()

                table_content += f"""
                    <tr>
                        <td>{invoice_number}</td>
                        <td>{supplier}</td>
                        <td>{diamond_type}</td>
                        <td>{carat_weight}</td>
                        <td>{price_per_carat}</td>
                        <td>{total_price_usd}</td>
                        <td>{total_price_sar}</td>
                        <td>{purchase_date}</td>
                        <td>{paid_amount}</td>
                    </tr>
                """

            # Add table footer
            table_content += """
                </tbody>
            </table>
            """

            # Build summary content
            purchases_count = self.purchases_count_label.text().split(": ")[1]
            purchases_total_usd = self.purchases_total_usd_label.text().split(": ")[1]
            purchases_total_sar = self.purchases_total_sar_label.text().split(": ")[1]

            summary_content = f"""
                <div class="summary-item">
                    <div class="summary-label">عدد المشتريات</div>
                    <div class="summary-value neutral">{purchases_count}</div>
                </div>
                <div class="summary-item">
                    <div class="summary-label">إجمالي المشتريات ($)</div>
                    <div class="summary-value negative">{purchases_total_usd}</div>
                </div>
                <div class="summary-item">
                    <div class="summary-label">إجمالي المشتريات (ريال)</div>
                    <div class="summary-value negative">{purchases_total_sar}</div>
                </div>
            """

            # Generate complete HTML using the template
            html = self.generate_enhanced_html_template(
                title="تقرير المشتريات",
                subtitle="تقرير المشتريات",
                date_range=date_range,
                content=table_content,
                summary=summary_content
            )

            return html
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء إنشاء تقرير المشتريات: {str(e)}")
            with open('error_log.txt', 'a', encoding='utf-8') as log_file:
                log_file.write(f"{datetime.now()}: خطأ في إنشاء HTML لتقرير المشتريات: {str(e)}\n")
            return ""

    def generate_financial_report_html(self):
        """Generate enhanced HTML for financial report."""
        try:
            # Get date range
            start_date = self.financial_start_date.date().toString("yyyy-MM-dd")
            end_date = self.financial_end_date.date().toString("yyyy-MM-dd")
            date_range = f"للفترة من {start_date} إلى {end_date}"

            # Build table content
            table_content = """
            <table>
                <thead>
                    <tr>
                        <th>البيان</th>
                        <th>المبلغ ($)</th>
                        <th>المبلغ (ريال)</th>
                    </tr>
                </thead>
                <tbody>
            """

            # Extract data from financial summary table
            rows_count = self.financial_summary.rowCount()

            # Variables to store net profit values
            net_profit_usd = "0.00"
            net_profit_sar = "0.00"

            for row in range(rows_count):
                # Check if items exist before calling .text()
                statement_item = self.financial_summary.item(row, 0)
                amount_usd_item = self.financial_summary.item(row, 1)
                amount_sar_item = self.financial_summary.item(row, 2)
                
                if not statement_item or not amount_usd_item or not amount_sar_item:
                    continue
                    
                statement = statement_item.text()
                amount_usd = amount_usd_item.text()
                amount_sar = amount_sar_item.text()

                # Determine row style based on statement type
                row_class = ""
                if "المخزون الافتتاحي" in statement:
                    row_class = "class='inventory'"
                elif "إجمالي المبيعات" in statement:
                    row_class = "class='positive'"
                elif "إجمالي المشتريات" in statement:
                    row_class = "class='negative'"
                elif "صافي الربح" in statement:
                    row_class = "class='neutral'"
                    net_profit_usd = amount_usd
                    net_profit_sar = amount_sar
                elif "مدينون" in statement:
                    row_class = "class='positive'"
                elif "دائنون" in statement:
                    row_class = "class='negative'"

                table_content += f"""
                    <tr {row_class}>
                        <td><strong>{statement}</strong></td>
                        <td>{amount_usd}</td>
                        <td>{amount_sar}</td>
                    </tr>
                """

            # Add table footer
            table_content += """
                </tbody>
            </table>
            """

            # Build summary content with cards for key metrics
            summary_content = f"""
                <div class="summary-item">
                    <div class="summary-label">صافي الربح ($)</div>
                    <div class="summary-value neutral">{net_profit_usd}</div>
                </div>
                <div class="summary-item">
                    <div class="summary-label">صافي الربح (ريال)</div>
                    <div class="summary-value neutral">{net_profit_sar}</div>
                </div>
            """

            # Generate complete HTML using the template
            html = self.generate_enhanced_html_template(
                title="التقرير المالي",
                subtitle="التقرير المالي",
                date_range=date_range,
                content=table_content,
                summary=summary_content
            )

            return html
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء إنشاء التقرير المالي: {str(e)}")
            with open('error_log.txt', 'a', encoding='utf-8') as log_file:
                log_file.write(f"{datetime.now()}: خطأ في إنشاء HTML للتقرير المالي: {str(e)}\n")
            return ""

    def generate_customer_report_html(self):
        """Generate enhanced HTML for customer report."""
        try:
            # Check if customer is selected
            customer_id = self.customer_combo.currentData()
            if customer_id is None:
                return ""

            # Handle "All" customers case
            if customer_id == "all":
                customer_name = "جميع العملاء"
                # Remove customer info section for all customers
                extra_info = None
            else:
                customer = self.session.query(Customer).filter_by(id=customer_id).first()
                if not customer:
                    return ""
                customer_name = customer.name
                # Remove customer info section for individual customers too
                extra_info = None

            # Get date range
            start_date = self.customer_start_date.date().toPyDate()
            end_date = self.customer_end_date.date().toPyDate()
            date_range = f"للفترة من {start_date.strftime('%Y-%m-%d')} إلى {end_date.strftime('%Y-%m-%d')}"



            # Build table content
            table_content = """
            <h3>تفاصيل المعاملات</h3>
            <table>
                <thead>
                    <tr>
                        <th>التاريخ</th>
                        <th>نوع الألماس</th>
                        <th>الوزن</th>
                        <th>سعر القيراط ($)</th>
                        <th>السعر بالريال للكمية</th>
                        <th>المبلغ المسدد ($)</th>
                        <th>المبلغ المسدد (ريال)</th>
                        <th>الرصيد ($)</th>
                        <th>الرصيد (ريال)</th>
                        <th>ملاحظات</th>
                    </tr>
                </thead>
                <tbody>
            """

            # Extract data from table
            rows_count = self.customer_report_table.rowCount()
            sales_data = []

            for row in range(rows_count):
                # معالجة الخلايا الفارغة بأمان
                weight = self.customer_report_table.item(row, 0).text() if self.customer_report_table.item(row, 0) else ""
                diamond_type = self.customer_report_table.item(row, 1).text() if self.customer_report_table.item(row, 1) else ""
                price_per_carat = self.customer_report_table.item(row, 2).text() if self.customer_report_table.item(row, 2) else ""
                price_sar_total = self.customer_report_table.item(row, 3).text() if self.customer_report_table.item(row, 3) else ""
                # Después de eliminar las columnas de monto debido, los índices cambian
                amount_paid_usd = self.customer_report_table.item(row, 4).text() if self.customer_report_table.item(row, 4) else ""
                amount_paid_sar = self.customer_report_table.item(row, 5).text() if self.customer_report_table.item(row, 5) else ""
                # الرصيد التراكمي
                cumulative_balance_usd = self.customer_report_table.item(row, 6).text() if self.customer_report_table.item(row, 6) else ""
                cumulative_balance_sar = self.customer_report_table.item(row, 7).text() if self.customer_report_table.item(row, 7) else ""
                # استخراج التاريخ والملاحظات
                notes_text = self.customer_report_table.item(row, 8).text() if self.customer_report_table.item(row, 8) else ""

                # استخراج التاريخ من الملاحظات (تم دمجهما في دالة generate_customer_report)
                parts = notes_text.split(" - ", 1)
                date = parts[0]
                notes = parts[1] if len(parts) > 1 else ""

                # Para mantener la compatibilidad con el formato HTML existente, agregamos valores cero para los montos debidos
                amount_due_usd = "0.000"
                amount_due_sar = "0.000"

                sales_data.append({
                    "weight": weight,
                    "diamond_type": diamond_type,
                    "price_per_carat": price_per_carat,
                    "price_sar_total": price_sar_total,
                    "amount_due_usd": amount_due_usd,
                    "amount_due_sar": amount_due_sar,
                    "amount_paid_usd": amount_paid_usd,
                    "amount_paid_sar": amount_paid_sar,
                    "balance_usd": cumulative_balance_usd,  # استخدام الرصيد التراكمي
                    "balance_sar": cumulative_balance_sar,  # استخدام الرصيد التراكمي
                    "date": date,
                    "notes": notes
                })

            # Add sales data to table
            if sales_data:
                for sale in sales_data:
                    # تحديد لون الصف بناءً على نوع المعاملة
                    row_class = ""
                    if sale['diamond_type'] == "سند قبض":
                        row_class = "style='background-color: #e8f8f5;'"  # لون فاتح للسندات

                    table_content += f"""
                        <tr {row_class}>
                            <td>{sale['date']}</td>
                            <td>{sale['diamond_type']}</td>
                            <td>{sale['weight']}</td>
                            <td>{sale['price_per_carat']}</td>
                            <td>{sale['price_sar_total']}</td>
                            <td>{sale['amount_paid_usd']}</td>
                            <td>{sale['amount_paid_sar']}</td>
                            <td>{sale['balance_usd']}</td>
                            <td>{sale['balance_sar']}</td>
                            <td>{sale['notes']}</td>
                        </tr>
                    """
            else:
                table_content += """
                    <tr>
                        <td colspan="10" style="text-align: center;">لا توجد معاملات في الفترة المحددة</td>
                    </tr>
                """

            # Get totals with safe handling
            total_weight = self.customer_total_weight_label.text() if hasattr(self, 'customer_total_weight_label') else "0.000"
            total_due = self.customer_total_due_label.text() if hasattr(self, 'customer_total_due_label') else "0.000"
            total_paid = self.customer_total_paid_label.text() if hasattr(self, 'customer_total_paid_label') else "0.000"
            total_balance = self.customer_total_balance_label.text() if hasattr(self, 'customer_total_balance_label') else "0.000"

            # حساب إجماليات الأعمدة
            total_price_per_carat = 0
            total_price_sar_total = 0
            total_amount_due_sar = 0
            total_amount_paid_sar = 0
            total_balance_sar = 0

            # حساب متوسط سعر القيراط إذا كان هناك بيانات
            if sales_data:
                for sale in sales_data:
                    try:
                        total_price_per_carat += float(sale['price_per_carat'])
                        total_price_sar_total += float(sale['price_sar_total'])
                        total_amount_due_sar += float(sale['amount_due_sar'])
                        total_amount_paid_sar += float(sale['amount_paid_sar'])
                    except (ValueError, TypeError):
                        pass  # تجاهل القيم غير الصالحة

                # حساب متوسط سعر القيراط
                avg_price_per_carat = total_price_per_carat / len(sales_data)
            else:
                avg_price_per_carat = 0

            # استخراج الرصيد النهائي بالريال من آخر عملية إذا كان هناك بيانات
            if sales_data:
                try:
                    total_balance_sar = float(sales_data[-1]['balance_sar'])
                except (ValueError, TypeError, IndexError):
                    total_balance_sar = 0

            # Add table footer with totals for all columns
            table_content += f"""
                </tbody>
                <tfoot>
                    <tr>
                        <th>الإجمالي</th>
                        <th></th>
                        <th>{total_weight}</th>
                        <th>{avg_price_per_carat:.3f}</th>
                        <th>{total_price_sar_total:.3f}</th>
                        <th>{total_paid}</th>
                        <th>{total_amount_paid_sar:.3f}</th>
                        <th>{total_balance}</th>
                        <th>{total_balance_sar:.3f}</th>
                        <th></th>
                    </tr>
                </tfoot>
            </table>
            """

            # Extract numeric values and calculate SAR equivalents
            try:
                # Extract USD values from labels
                weight_value = total_weight.split()[0] if total_weight else "0"
                due_usd_value = total_due.split()[0] if total_due else "0"
                paid_usd_value = total_paid.split()[0] if total_paid else "0"
                balance_usd_value = total_balance.split()[0] if total_balance else "0"

                # Calculate SAR equivalents (assuming exchange rate of 3.75)
                exchange_rate = 3.75
                due_sar_value = float(due_usd_value) * exchange_rate
                paid_sar_value = float(paid_usd_value) * exchange_rate
                balance_sar_value = float(balance_usd_value) * exchange_rate

            except (ValueError, IndexError):
                due_sar_value = 0
                paid_sar_value = 0
                balance_sar_value = 0
                weight_value = "0"

            # Build summary content with full width to match table alignment
            summary_content = f"""
                <div class="summary-container" style="display: grid; grid-template-columns: 1fr 1fr; grid-template-rows: 1fr 1fr; gap: 0; margin-top: 30px; width: 100%; background-color: #f5f5f5; border-radius: 8px; overflow: hidden; border: 2px solid #ddd; box-shadow: 0 4px 8px rgba(0,0,0,0.1);">

                    <!-- Top Left: Weight -->
                    <div class="summary-item" style="display: flex; justify-content: space-between; align-items: center; padding: 25px 30px; background-color: #f0f8ff; border-bottom: 1px solid #ddd; border-right: 1px solid #ddd;">
                        <span style="color: #2ecc71; font-weight: bold; font-size: 20px;">قيراط {weight_value}</span>
                        <span style="color: #2ecc71; font-weight: bold; font-size: 20px;">إجمالي الوزن:</span>
                    </div>

                    <!-- Top Right: Due Amount -->
                    <div class="summary-item" style="display: flex; justify-content: space-between; align-items: center; padding: 25px 30px; background-color: #fff5f5; border-bottom: 1px solid #ddd;">
                        <div style="display: flex; align-items: center; gap: 20px;">
                            <span style="color: #e74c3c; font-weight: bold; font-size: 20px;">ريال {due_sar_value:.2f}</span>
                            <span style="color: #999; font-weight: bold; font-size: 22px;">|</span>
                            <span style="color: #2ecc71; font-weight: bold; font-size: 20px;">$ {due_usd_value}</span>
                        </div>
                        <span style="color: #e74c3c; font-weight: bold; font-size: 20px;">إجمالي المستحق</span>
                    </div>

                    <!-- Bottom Left: Paid Amount -->
                    <div class="summary-item" style="display: flex; justify-content: space-between; align-items: center; padding: 25px 30px; background-color: #f0f8ff; border-right: 1px solid #ddd;">
                        <div style="display: flex; align-items: center; gap: 20px;">
                            <span style="color: #e74c3c; font-weight: bold; font-size: 20px;">ريال {paid_sar_value:.2f}</span>
                            <span style="color: #999; font-weight: bold; font-size: 22px;">|</span>
                            <span style="color: #2ecc71; font-weight: bold; font-size: 20px;">$ {paid_usd_value}</span>
                        </div>
                        <span style="color: #3498db; font-weight: bold; font-size: 20px;">إجمالي المسدد</span>
                    </div>

                    <!-- Bottom Right: Balance -->
                    <div class="summary-item" style="display: flex; justify-content: space-between; align-items: center; padding: 25px 30px; background-color: #fff5f5;">
                        <div style="display: flex; align-items: center; gap: 20px;">
                            <span style="color: {'#e74c3c' if float(balance_usd_value) > 0 else '#2ecc71'}; font-weight: bold; font-size: 20px;">ريال {balance_sar_value:.2f}</span>
                            <span style="color: #999; font-weight: bold; font-size: 22px;">|</span>
                            <span style="color: {'#e74c3c' if float(balance_usd_value) > 0 else '#2ecc71'}; font-weight: bold; font-size: 20px;">$ {balance_usd_value}</span>
                        </div>
                        <span style="color: {'#e74c3c' if float(balance_usd_value) > 0 else '#2ecc71'}; font-weight: bold; font-size: 20px;">إجمالي الرصيد</span>
                    </div>
                </div>
            """

            # Generate complete HTML using the template
            html = self.generate_enhanced_html_template(
                title=f"تقرير العميل - {customer_name}",
            subtitle=f"تقرير حساب العميل: {customer_name}",
                date_range=date_range,
                content=table_content,
                summary=summary_content,
                extra_info=extra_info
            )

            return html
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء إنشاء تقرير العميل: {str(e)}")
            with open('error_log.txt', 'a', encoding='utf-8') as log_file:
                log_file.write(f"{datetime.now()}: خطأ في إنشاء HTML لتقرير العميل: {str(e)}\n")
            return ""

    def generate_supplier_report_html(self):
        """Generate enhanced HTML for supplier report."""
        try:
            # Check if supplier is selected
            supplier_id = self.supplier_combo.currentData()
            if supplier_id is None:
                return ""

            # Handle "All" suppliers case
            if supplier_id == "all":
                supplier_name = "جميع الموردين"
                extra_info = f"""
                <h3>معلومات التقرير</h3>
                <p><strong>نوع التقرير:</strong> ملخص جميع الموردين</p>
                <p><strong>عدد الموردين:</strong> {self.supplier_report_table.rowCount()}</p>
                """
            else:
                supplier = self.session.query(Supplier).filter_by(id=supplier_id).first()
                if not supplier:
                    return ""
                supplier_name = supplier.name
                # Create supplier info section
                extra_info = f"""
                <h3>معلومات المورد</h3>
                <p><strong>اسم المورد:</strong> {supplier.name}</p>
                <p><strong>رقم الهاتف:</strong> {supplier.phone or '-'}</p>
                <p><strong>العنوان:</strong> {supplier.address or '-'}</p>
                """

            # Get date range
            start_date = self.supplier_start_date.date().toPyDate()
            end_date = self.supplier_end_date.date().toPyDate()
            date_range = f"للفترة من {start_date.strftime('%Y-%m-%d')} إلى {end_date.strftime('%Y-%m-%d')}"



            # Build table content
            table_content = """
            <h3>تفاصيل المشتريات والسندات</h3>
            <table>
                <thead>
                    <tr>
                        <th>التاريخ</th>
                        <th>نوع المستند</th>
                        <th>الوزن</th>
                        <th>سعر القيراط ($)</th>
                        <th>السعر بالريال للكمية</th>
                        <th>المبلغ المسدد ($)</th>
                        <th>المبلغ المسدد (ريال)</th>
                        <th>الرصيد ($)</th>
                        <th>الرصيد التراكمي (ريال)</th>
                    </tr>
                </thead>
                <tbody>
            """

            # Extract data from table
            rows_count = self.supplier_report_table.rowCount()
            purchase_data = []

            for row in range(rows_count):
                # معالجة الخلايا الفارغة بأمان
                weight = self.supplier_report_table.item(row, 0).text() if self.supplier_report_table.item(row, 0) else ""
                diamond_type = self.supplier_report_table.item(row, 1).text() if self.supplier_report_table.item(row, 1) else ""
                price_per_carat = self.supplier_report_table.item(row, 2).text() if self.supplier_report_table.item(row, 2) else ""
                price_sar_total = self.supplier_report_table.item(row, 3).text() if self.supplier_report_table.item(row, 3) else ""
                # Después de eliminar las columnas de monto debido, los índices cambian
                amount_paid_usd = self.supplier_report_table.item(row, 4).text() if self.supplier_report_table.item(row, 4) else ""
                amount_paid_sar = self.supplier_report_table.item(row, 5).text() if self.supplier_report_table.item(row, 5) else ""
                balance_usd = self.supplier_report_table.item(row, 6).text() if self.supplier_report_table.item(row, 6) else ""
                balance_sar = self.supplier_report_table.item(row, 7).text() if self.supplier_report_table.item(row, 7) else ""  # الرصيد التراكمي بالريال
                date = self.supplier_report_table.item(row, 8).text() if self.supplier_report_table.item(row, 8) else ""
                document_type = self.supplier_report_table.item(row, 9).text() if self.supplier_report_table.item(row, 9) else ""

                # تحديد نوع المعاملة (مشتريات أو سند صرف بدون مرجع)
                is_receipt = "سند صرف" in document_type

                # Para mantener la compatibilidad con el formato HTML existente, agregamos valores cero para los montos debidos
                amount_due_usd = "0.000"
                amount_due_sar = "0.000"

                purchase_data.append({
                    "weight": weight,
                    "diamond_type": diamond_type,
                    "price_per_carat": price_per_carat,
                    "price_sar_total": price_sar_total,
                    "amount_due_usd": amount_due_usd,
                    "amount_due_sar": amount_due_sar,
                    "amount_paid_usd": amount_paid_usd,
                    "amount_paid_sar": amount_paid_sar,
                    "balance_usd": balance_usd,
                    "balance_sar": balance_sar,
                    "date": date,
                    "document_type": document_type,
                    "is_receipt": is_receipt
                })

            # Add purchase data to table
            if purchase_data:
                for purchase in purchase_data:
                    # تحديد نمط الصف بناءً على نوع المعاملة
                    row_style = 'style="background-color: #E8F8F5;"' if purchase['is_receipt'] else ''

                    table_content += f"""
                        <tr {row_style}>
                            <td>{purchase['date']}</td>
                            <td>{purchase['document_type']}</td>
                            <td>{purchase['weight']}</td>
                            <td>{purchase['price_per_carat']}</td>
                            <td>{purchase['price_sar_total']}</td>
                            <td>{purchase['amount_paid_usd']}</td>
                            <td>{purchase['amount_paid_sar']}</td>
                            <td>{purchase['balance_usd']}</td>
                            <td>{purchase['balance_sar']}</td>
                        </tr>
                    """
            else:
                table_content += """
                    <tr>
                        <td colspan="9" style="text-align: center;">لا توجد معاملات في الفترة المحددة</td>
                    </tr>
                """

            # Get totals
            total_weight = self.supplier_total_weight_label.text()
            total_due = self.supplier_total_due_label.text()
            total_paid = self.supplier_total_paid_label.text()
            total_balance = self.supplier_total_balance_label.text()

            # حساب إجماليات الأعمدة
            total_price_per_carat = 0
            total_price_sar_total = 0
            total_amount_due_sar = 0
            total_amount_paid_sar = 0
            total_balance_sar = 0

            # حساب متوسط سعر القيراط إذا كان هناك بيانات
            if purchase_data:
                for purchase in purchase_data:
                    try:
                        total_price_per_carat += float(purchase['price_per_carat'])
                        total_price_sar_total += float(purchase['price_sar_total'])
                        total_amount_due_sar += float(purchase['amount_due_sar'])
                        total_amount_paid_sar += float(purchase['amount_paid_sar'])
                    except (ValueError, TypeError):
                        pass  # تجاهل القيم غير الصالحة

                # حساب متوسط سعر القيراط
                avg_price_per_carat = total_price_per_carat / len(purchase_data)
            else:
                avg_price_per_carat = 0

            # استخراج الرصيد النهائي بالريال من آخر عملية إذا كان هناك بيانات
            if purchase_data:
                try:
                    total_balance_sar = float(purchase_data[-1]['balance_sar'])
                except (ValueError, TypeError, IndexError):
                    total_balance_sar = 0

            # Add table footer with totals for all columns
            table_content += f"""
                </tbody>
                <tfoot>
                    <tr>
                        <th>الإجمالي</th>
                        <th></th>
                        <th>{total_weight}</th>
                        <th>{avg_price_per_carat:.3f}</th>
                        <th>{total_price_sar_total:.3f}</th>
                        <th>{total_paid}</th>
                        <th>{total_amount_paid_sar:.3f}</th>
                        <th>{total_balance}</th>
                        <th>{total_balance_sar:.3f}</th>
                    </tr>
                </tfoot>
            </table>
            """

            # Extract values for enhanced summary
            weight_value = total_weight.replace("قيراط", "").strip()

            # Parse due amount (format: "$ X.XX")
            due_usd_value = total_due.replace("$", "").strip()
            try:
                due_sar_value = float(due_usd_value) * 3.75
            except (ValueError, TypeError):
                due_sar_value = 0.0

            # Parse paid amount (format: "$ X.XX")
            paid_usd_value = total_paid.replace("$", "").strip()
            try:
                paid_sar_value = float(paid_usd_value) * 3.75
            except (ValueError, TypeError):
                paid_sar_value = 0.0

            # Parse balance amount (format: "$ X.XX")
            balance_usd_value = total_balance.replace("$", "").strip()
            try:
                balance_sar_value = float(balance_usd_value) * 3.75
            except (ValueError, TypeError):
                balance_sar_value = 0.0

            # Build summary content with full width to match table alignment
            summary_content = f"""
                <div class="summary-container" style="display: grid; grid-template-columns: 1fr 1fr; grid-template-rows: 1fr 1fr; gap: 0; margin-top: 30px; width: 100%; background-color: #f5f5f5; border-radius: 8px; overflow: hidden; border: 2px solid #ddd; box-shadow: 0 4px 8px rgba(0,0,0,0.1);">

                    <!-- Top Left: Weight -->
                    <div class="summary-item" style="display: flex; justify-content: space-between; align-items: center; padding: 25px 30px; background-color: #f0f8ff; border-bottom: 1px solid #ddd; border-right: 1px solid #ddd;">
                        <span style="color: #2ecc71; font-weight: bold; font-size: 20px;">قيراط {weight_value}</span>
                        <span style="color: #2ecc71; font-weight: bold; font-size: 20px;">إجمالي الوزن:</span>
                    </div>

                    <!-- Top Right: Due Amount -->
                    <div class="summary-item" style="display: flex; justify-content: space-between; align-items: center; padding: 25px 30px; background-color: #fff5f5; border-bottom: 1px solid #ddd;">
                        <div style="display: flex; align-items: center; gap: 20px;">
                            <span style="color: #e74c3c; font-weight: bold; font-size: 20px;">ريال {due_sar_value:.2f}</span>
                            <span style="color: #999; font-weight: bold; font-size: 22px;">|</span>
                            <span style="color: #2ecc71; font-weight: bold; font-size: 20px;">$ {due_usd_value}</span>
                        </div>
                        <span style="color: #e74c3c; font-weight: bold; font-size: 20px;">إجمالي المستحق</span>
                    </div>

                    <!-- Bottom Left: Paid Amount -->
                    <div class="summary-item" style="display: flex; justify-content: space-between; align-items: center; padding: 25px 30px; background-color: #f0f8ff; border-right: 1px solid #ddd;">
                        <div style="display: flex; align-items: center; gap: 20px;">
                            <span style="color: #e74c3c; font-weight: bold; font-size: 20px;">ريال {paid_sar_value:.2f}</span>
                            <span style="color: #999; font-weight: bold; font-size: 22px;">|</span>
                            <span style="color: #2ecc71; font-weight: bold; font-size: 20px;">$ {paid_usd_value}</span>
                        </div>
                        <span style="color: #3498db; font-weight: bold; font-size: 20px;">إجمالي المسدد</span>
                    </div>

                    <!-- Bottom Right: Balance -->
                    <div class="summary-item" style="display: flex; justify-content: space-between; align-items: center; padding: 25px 30px; background-color: #fff5f5;">
                        <div style="display: flex; align-items: center; gap: 20px;">
                            <span style="color: {'#e74c3c' if float(balance_usd_value) > 0 else '#2ecc71'}; font-weight: bold; font-size: 20px;">ريال {balance_sar_value:.2f}</span>
                            <span style="color: #999; font-weight: bold; font-size: 22px;">|</span>
                            <span style="color: {'#e74c3c' if float(balance_usd_value) > 0 else '#2ecc71'}; font-weight: bold; font-size: 20px;">$ {balance_usd_value}</span>
                        </div>
                        <span style="color: {'#e74c3c' if float(balance_usd_value) > 0 else '#2ecc71'}; font-weight: bold; font-size: 20px;">إجمالي الرصيد</span>
                    </div>
                </div>
            """

            # Generate complete HTML using the template
            html = self.generate_enhanced_html_template(
                title=f"تقرير المورد - {supplier_name}",
            subtitle=f"تقرير حساب المورد: {supplier_name}",
                date_range=date_range,
                content=table_content,
                summary=summary_content,
                extra_info=extra_info
            )

            return html
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء إنشاء تقرير المورد: {str(e)}")
            with open('error_log.txt', 'a', encoding='utf-8') as log_file:
                log_file.write(f"{datetime.now()}: خطأ في إنشاء HTML لتقرير المورد: {str(e)}\n")
            return ""

    def export_customer_report_html(self):
        """Export customer report as HTML file and open in browser."""
        try:
            customer_id = self.customer_combo.currentData()
            customer = self.session.query(Customer).filter_by(id=customer_id).first()

            if not customer:
                QMessageBox.warning(self, "تنبيه", "يرجى اختيار عميل أولاً")
                return

            # Generate enhanced HTML content
            html_content = self.generate_customer_report_html()
            if not html_content:
                return

            # Ask user for save location
            file_path, _ = QFileDialog.getSaveFileName(
                self,
                "حفظ تقرير العميل",
                f"تقرير_العميل_{customer.name}_{datetime.now().strftime('%Y%m%d')}.html",
                "HTML Files (*.html)"
            )

            if not file_path:
                return  # User canceled

            # Write HTML to file
            with open(file_path, 'w', encoding='utf-8') as file:
                file.write(html_content)

            # Open the HTML file in the default browser
            QDesktopServices.openUrl(QUrl.fromLocalFile(file_path))

            QMessageBox.information(
                self,
                "تم التصدير",
                f"تم تصدير تقرير العميل بنجاح إلى:\n{file_path} وفتحه في المتصفح"
            )

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تصدير التقرير: {str(e)}")
            with open('error_log.txt', 'a', encoding='utf-8') as log_file:
                log_file.write(f"{datetime.now()}: خطأ في تصدير تقرير العميل HTML: {str(e)}\n")



    def export_supplier_report_html(self):
        """Export supplier report as HTML file and open in browser."""
        try:
            supplier_id = self.supplier_combo.currentData()
            if supplier_id is None:
                QMessageBox.warning(self, "تنبيه", "الرجاء اختيار مورد")
                return

            # Generate enhanced HTML content
            html_content = self.generate_supplier_report_html()
            if not html_content:
                QMessageBox.warning(self, "تنبيه", "لا يمكن إنشاء التقرير، تأكد من وجود بيانات")
                return

            # Get supplier data
            supplier = self.session.query(Supplier).filter_by(id=supplier_id).first()

            # Ask user for save location
            file_path, _ = QFileDialog.getSaveFileName(
                self,
                "حفظ تقرير المورد",
                f"تقرير_المورد_{supplier.name}_{datetime.now().strftime('%Y%m%d')}.html",
                "HTML Files (*.html)"
            )

            if not file_path:
                return  # User canceled

            # Write HTML to file
            with open(file_path, 'w', encoding='utf-8') as file:
                file.write(html_content)

            # Open the HTML file in the default browser
            QDesktopServices.openUrl(QUrl.fromLocalFile(file_path))

            QMessageBox.information(
                self,
                "تم التصدير",
                f"تم تصدير تقرير المورد بنجاح إلى:\n{file_path} وفتحه في المتصفح"
            )

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تصدير التقرير: {str(e)}")
            with open('error_log.txt', 'a', encoding='utf-8') as log_file:
                log_file.write(f"{datetime.now()}: خطأ في تصدير تقرير المورد HTML: {str(e)}\n")

    def export_customer_report_excel(self):
        customer_id = self.customer_combo.currentData()
        if customer_id is None:
            QMessageBox.warning(self, "تنبيه", "الرجاء اختيار عميل")
            return

        customer_name = self.customer_combo.currentText()
        start_date = self.customer_start_date.date().toPyDate()
        end_date = self.customer_end_date.date().toPyDate()

        file_path, _ = QFileDialog.getSaveFileName(
            self,
            "حفظ التقرير",
            f"تقرير العميل {customer_name}.xlsx",
            "Excel Files (*.xlsx)"
        )

        if not file_path:
            return

        try:
            workbook = xlsxwriter.Workbook(file_path)
            worksheet = workbook.add_worksheet("تقرير العميل")

            # Enhanced formatting
            title_format = workbook.add_format({
                'bold': True,
                'font_size': 16,
                'align': 'center',
                'valign': 'vcenter',
                'font_color': '#1F497D',
                'bg_color': '#E6EFF9',
                'border': 1,
                'text_wrap': True
            })

            header_format = workbook.add_format({
                'bold': True,
                'font_size': 12,
                'align': 'center',
                'valign': 'vcenter',
                'bg_color': '#BDD7EE',
                'border': 1,
                'text_wrap': True,
                'border_color': '#4472C4'
            })

            cell_format = workbook.add_format({
                'border': 1,
                'align': 'center',
                'valign': 'vcenter',
                'border_color': '#B4C6E7',
                'text_wrap': True
            })

            # تنسيق خاص لسندات القبض بدون مرجع
            receipt_format = workbook.add_format({
                'border': 1,
                'align': 'center',
                'valign': 'vcenter',
                'border_color': '#B4C6E7',
                'text_wrap': True,
                'bg_color': '#E8F8F5'  # لون فاتح للسندات
            })

            receipt_num_format = workbook.add_format({
                'border': 1,
                'align': 'center',
                'valign': 'vcenter',
                'num_format': '#,##0.00',
                'border_color': '#B4C6E7',
                'bg_color': '#E8F8F5'  # لون فاتح للسندات
            })

            num_format = workbook.add_format({
                'border': 1,
                'align': 'center',
                'valign': 'vcenter',
                'num_format': '#,##0.00',
                'border_color': '#B4C6E7'
            })

            date_format = workbook.add_format({
                'border': 1,
                'align': 'center',
                'valign': 'vcenter',
                'num_format': 'yyyy-mm-dd',
                'border_color': '#B4C6E7'
            })

            total_format = workbook.add_format({
                'bold': True,
                'border': 1,
                'align': 'center',
                'valign': 'vcenter',
                'bg_color': '#D9E2F3',
                'num_format': '#,##0.00',
                'border_color': '#4472C4'
            })

            # Set column widths
            worksheet.set_column('A:A', 15)  # Date
            worksheet.set_column('B:B', 18)  # Diamond Type
            worksheet.set_column('C:L', 15)  # Values
            worksheet.set_column('L:L', 30)  # Notes

            # RTL direction
            worksheet.right_to_left()

            # Add title and info
            company_info = self.session.query(CompanyInfo).first()
            company_name = company_info.name if company_info and company_info.name else "نظام مبيعات الألماس"

            worksheet.merge_range('A1:L1', company_name, title_format)
            worksheet.merge_range('A2:L2', f"تقرير العميل: {customer_name}", title_format)
            worksheet.merge_range('A3:L3',
                                f"للفترة من {start_date.strftime('%Y-%m-%d')} إلى {end_date.strftime('%Y-%m-%d')}",
                                title_format)

            # Headers - Eliminamos las columnas de monto debido
            headers = ['التاريخ', 'نوع الألماس', 'الوزن', 'سعر القيراط ($)', 'السعر بالريال للكمية',
                      'المبلغ المسدد ($)', 'المبلغ المسدد (ريال)',
                      'الرصيد ($)', 'الرصيد (ريال)', 'ملاحظات']
            for col, header in enumerate(headers):
                worksheet.write(4, col, header, header_format)

            # Data rows
            row = 5
            total_weight = 0
            total_price_per_carat = 0
            total_price_sar_total = 0
            total_paid_usd = 0
            total_paid_sar = 0
            total_balance_usd = 0
            total_balance_sar = 0

            for i in range(self.customer_report_table.rowCount()):
                weight = float(self.customer_report_table.item(i, 0).text())
                diamond_type = self.customer_report_table.item(i, 1).text()
                price_per_carat = float(self.customer_report_table.item(i, 2).text())
                price_sar_total = float(self.customer_report_table.item(i, 3).text())
                amount_paid_usd = float(self.customer_report_table.item(i, 4).text())
                amount_paid_sar = float(self.customer_report_table.item(i, 5).text())
                balance_usd = float(self.customer_report_table.item(i, 6).text())
                balance_sar = float(self.customer_report_table.item(i, 7).text())

                # Get date and notes from the appropriate column
                date_str = ""
                notes = ""
                if self.customer_report_table.columnCount() > 8 and self.customer_report_table.item(i, 8):
                    notes_text = self.customer_report_table.item(i, 8).text()
                    # استخراج التاريخ من الملاحظات (تم دمجهما في دالة generate_customer_report)
                    parts = notes_text.split(" - ", 1)
                    date_str = parts[0]
                    notes = parts[1] if len(parts) > 1 else ""

                # اختيار التنسيق المناسب بناءً على نوع المعاملة
                current_cell_format = receipt_format if diamond_type == "سند قبض" else cell_format
                current_num_format = receipt_num_format if diamond_type == "سند قبض" else num_format

                worksheet.write(row, 0, date_str, date_format)
                worksheet.write(row, 1, diamond_type, current_cell_format)
                worksheet.write(row, 2, weight, current_num_format)
                worksheet.write(row, 3, price_per_carat, current_num_format)
                worksheet.write(row, 4, price_sar_total, current_num_format)
                worksheet.write(row, 5, amount_paid_usd, current_num_format)
                worksheet.write(row, 6, amount_paid_sar, current_num_format)
                worksheet.write(row, 7, balance_usd, current_num_format)
                worksheet.write(row, 8, balance_sar, current_num_format)
                worksheet.write(row, 9, notes, current_cell_format)

                # تجميع الإجماليات لكل عمود
                # إذا كان نوع المعاملة "سند قبض" فلا نضيف الوزن وسعر القيراط
                if diamond_type != "سند قبض":
                    total_weight += weight
                    total_price_per_carat += price_per_carat
                    total_price_sar_total += price_sar_total

                # دائماً نضيف المبالغ المدفوعة
                total_paid_usd += amount_paid_usd
                total_paid_sar += amount_paid_sar

                # الرصيد النهائي هو آخر رصيد تراكمي
                if i == self.customer_report_table.rowCount() - 1:
                    total_balance_usd = balance_usd
                    total_balance_sar = balance_sar

                row += 1

            # حساب متوسط سعر القيراط (فقط للمبيعات وليس لسندات القبض)
            # نحسب عدد المبيعات (بدون سندات القبض)
            sales_count = 0
            for i in range(self.customer_report_table.rowCount()):
                if self.customer_report_table.item(i, 1).text() != "سند قبض":
                    sales_count += 1

            avg_price_per_carat = total_price_per_carat / sales_count if sales_count > 0 else 0

            # Totals row - إضافة إجماليات لجميع الأعمدة
            worksheet.write(row, 0, "الإجمالي", total_format)
            worksheet.write(row, 1, "", total_format)
            worksheet.write(row, 2, total_weight, total_format)
            worksheet.write(row, 3, avg_price_per_carat, total_format)  # متوسط سعر القيراط
            worksheet.write(row, 4, total_price_sar_total, total_format)  # إجمالي السعر بالريال
            worksheet.write(row, 5, total_paid_usd, total_format)  # إجمالي المبلغ المسدد بالدولار
            worksheet.write(row, 6, total_paid_sar, total_format)  # إجمالي المبلغ المسدد بالريال
            worksheet.write(row, 7, total_balance_usd, total_format)  # الرصيد النهائي بالدولار
            worksheet.write(row, 8, total_balance_sar, total_format)  # الرصيد النهائي بالريال
            worksheet.write(row, 9, "", total_format)

            # Footer
            worksheet.merge_range(f'A{row+3}:L{row+3}',
                                f"تم إنشاء هذا التقرير بواسطة: {self.user.username}",
                                title_format)
            worksheet.merge_range(f'A{row+4}:L{row+4}',
                                f"تاريخ الطباعة: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
                                title_format)

            workbook.close()

            QMessageBox.information(self, "تم", f"تم تصدير التقرير بنجاح")
            QDesktopServices.openUrl(QUrl.fromLocalFile(file_path))

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تصدير التقرير: {str(e)}")
            with open('error_log.txt', 'a', encoding='utf-8') as log_file:
                log_file.write(f"{datetime.now()}: خطأ في تصدير تقرير العميل Excel: {str(e)}\n")

    def export_customer_report(self):
        """Main export function that now defaults to Excel"""
        self.export_customer_report_excel()

    def export_supplier_report_excel(self):
        supplier_id = self.supplier_combo.currentData()
        if supplier_id is None:
            QMessageBox.warning(self, "تنبيه", "الرجاء اختيار مورد")
            return

        supplier_name = self.supplier_combo.currentText()
        start_date = self.supplier_start_date.date().toPyDate()
        end_date = self.supplier_end_date.date().toPyDate()

        file_path, _ = QFileDialog.getSaveFileName(
            self,
            "حفظ التقرير",
            f"تقرير المورد {supplier_name}.xlsx",
            "Excel Files (*.xlsx)"
        )

        if not file_path:
            return

        try:
            workbook = xlsxwriter.Workbook(file_path)
            worksheet = workbook.add_worksheet("تقرير المورد")

            # Enhanced formatting
            title_format = workbook.add_format({
                'bold': True,
                'font_size': 16,
                'align': 'center',
                'valign': 'vcenter',
                'font_color': '#1F497D',
                'bg_color': '#E6EFF9',
                'border': 1,
                'text_wrap': True
            })

            header_format = workbook.add_format({
                'bold': True,
                'font_size': 12,
                'align': 'center',
                'valign': 'vcenter',
                'bg_color': '#BDD7EE',
                'border': 1,
                'text_wrap': True,
                'border_color': '#4472C4'
            })

            cell_format = workbook.add_format({
                'border': 1,
                'align': 'center',
                'valign': 'vcenter',
                'border_color': '#B4C6E7',
                'text_wrap': True
            })

            # تنسيق خاص لسندات الصرف بدون مرجع
            receipt_format = workbook.add_format({
                'border': 1,
                'align': 'center',
                'valign': 'vcenter',
                'border_color': '#B4C6E7',
                'text_wrap': True,
                'bg_color': '#E8F8F5'  # لون فاتح للسندات
            })

            num_format = workbook.add_format({
                'border': 1,
                'align': 'center',
                'valign': 'vcenter',
                'num_format': '#,##0.00',
                'border_color': '#B4C6E7'
            })

            # تنسيق خاص للأرقام في سندات الصرف بدون مرجع
            receipt_num_format = workbook.add_format({
                'border': 1,
                'align': 'center',
                'valign': 'vcenter',
                'num_format': '#,##0.00',
                'border_color': '#B4C6E7',
                'bg_color': '#E8F8F5'  # لون فاتح للسندات
            })

            date_format = workbook.add_format({
                'border': 1,
                'align': 'center',
                'valign': 'vcenter',
                'num_format': 'yyyy-mm-dd',
                'border_color': '#B4C6E7'
            })

            total_format = workbook.add_format({
                'bold': True,
                'border': 1,
                'align': 'center',
                'valign': 'vcenter',
                'bg_color': '#D9E2F3',
                'num_format': '#,##0.00',
                'border_color': '#4472C4'
            })

            # Set column widths
            worksheet.set_column('A:A', 15)  # Date
            worksheet.set_column('B:B', 18)  # Diamond Type
            worksheet.set_column('C:K', 15)  # Values
            worksheet.set_column('L:L', 30)  # Notes            # RTL direction
            worksheet.right_to_left()

            # Add title and info
            company_info = self.session.query(CompanyInfo).first()
            company_name = company_info.name if company_info and company_info.name else "نظام مبيعات الألماس"

            worksheet.merge_range('A1:L1', company_name, title_format)
            worksheet.merge_range('A2:L2', f"تقرير المورد: {supplier_name}", title_format)
            worksheet.merge_range('A3:L3',
                                f"للفترة من {start_date.strftime('%Y-%m-%d')} إلى {end_date.strftime('%Y-%m-%d')}",
                                title_format)

            # Headers - Eliminamos las columnas de monto debido
            headers = ['التاريخ', 'نوع السند', 'المرجع', 'العميل/المورد',
                      'المبلغ ($)', 'المبلغ (ريال)', 'تاريخ السند']
            for col, header in enumerate(headers):
                worksheet.write(4, col, header, header_format)

            # Data rows
            row = 5
            total_weight = 0
            total_price_per_carat = 0
            total_price_sar_total = 0
            total_paid_usd = 0
            total_paid_sar = 0
            total_balance_usd = 0
            total_balance_sar = 0

            for i in range(self.supplier_report_table.rowCount()):
                weight = float(self.supplier_report_table.item(i, 0).text())
                diamond_type = self.supplier_report_table.item(i, 1).text()
                price_per_carat = float(self.supplier_report_table.item(i, 2).text())
                price_sar_total = float(self.supplier_report_table.item(i, 3).text())
                amount_paid_usd = float(self.supplier_report_table.item(i, 4).text())
                amount_paid_sar = float(self.supplier_report_table.item(i, 5).text())
                balance_usd = float(self.supplier_report_table.item(i, 6).text())
                balance_sar = float(self.supplier_report_table.item(i, 7).text())

                # استخراج التاريخ ونوع المستند
                date_str = self.supplier_report_table.item(i, 8).text() if self.supplier_report_table.item(i, 8) else ""
                document_type = self.supplier_report_table.item(i, 9).text() if self.supplier_report_table.item(i, 9) else ""

                # تحديد نوع المعاملة (مشتريات أو سند صرف)
                is_receipt = "سند صرف" in document_type

                # اختيار التنسيق المناسب بناءً على نوع المعاملة
                current_cell_format = receipt_format if is_receipt else cell_format
                current_num_format = receipt_num_format if is_receipt else num_format

                worksheet.write(row, 0, date_str, date_format)
                worksheet.write(row, 1, document_type, current_cell_format)
                worksheet.write(row, 2, weight, current_num_format)
                worksheet.write(row, 3, price_per_carat, current_num_format)
                worksheet.write(row, 4, price_sar_total, current_num_format)
                worksheet.write(row, 5, amount_paid_usd, current_num_format)
                worksheet.write(row, 6, amount_paid_sar, current_num_format)
                worksheet.write(row, 7, balance_usd, current_num_format)
                worksheet.write(row, 8, balance_sar, current_num_format)
                worksheet.write(row, 9, "", current_cell_format)

                # تجميع الإجماليات لكل عمود
                # إذا كان نوع المعاملة "سند صرف بدون مرجع" فلا نضيف الوزن وسعر القيراط
                if not is_receipt:
                    total_weight += weight
                    total_price_per_carat += price_per_carat
                    total_price_sar_total += price_sar_total

                # دائماً نضيف المبالغ المدفوعة
                total_paid_usd += amount_paid_usd
                total_paid_sar += amount_paid_sar

                # الرصيد النهائي هو آخر رصيد تراكمي
                if i == self.supplier_report_table.rowCount() - 1:
                    total_balance_usd = balance_usd
                    total_balance_sar = balance_sar

                row += 1

            # حساب متوسط سعر القيراط - فقط للمشتريات وليس لسندات الصرف
            # نحسب عدد المشتريات (بدون سندات الصرف)
            purchases_count = 0
            for i in range(self.supplier_report_table.rowCount()):
                if self.supplier_report_table.columnCount() > 11 and self.supplier_report_table.item(i, 11):
                    document_type = self.supplier_report_table.item(i, 11).text()
                    if "فاتورة مشتريات" in document_type:
                        purchases_count += 1

            avg_price_per_carat = total_price_per_carat / purchases_count if purchases_count > 0 else 0

            # Totals row - إضافة إجماليات لجميع الأعمدة
            worksheet.write(row, 0, "الإجمالي", total_format)
            worksheet.write(row, 1, "", total_format)
            worksheet.write(row, 2, total_weight, total_format)
            worksheet.write(row, 3, avg_price_per_carat, total_format)  # متوسط سعر القيراط
            worksheet.write(row, 4, total_price_sar_total, total_format)  # إجمالي السعر بالريال
            worksheet.write(row, 5, total_paid_usd, total_format)  # إجمالي المبلغ المسدد بالدولار
            worksheet.write(row, 6, total_paid_sar, total_format)  # إجمالي المبلغ المدفوع بالريال
            worksheet.write(row, 7, total_balance_usd, total_format)  # الرصيد النهائي بالدولار
            worksheet.write(row, 8, total_balance_sar, total_format)  # الرصيد النهائي بالريال
            worksheet.write(row, 9, "", total_format)

            # Footer
            worksheet.merge_range(f'A{row+3}:L{row+3}',
                                f"تم إنشاء هذا التقرير بواسطة: {self.user.username}",
                                title_format)
            worksheet.merge_range(f'A{row+4}:L{row+4}',
                                f"تاريخ الطباعة: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
                                title_format)

            workbook.close()

            QMessageBox.information(self, "تم", f"تم تصدير التقرير بنجاح")
            QDesktopServices.openUrl(QUrl.fromLocalFile(file_path))

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تصدير التقرير: {str(e)}")
            with open('error_log.txt', 'a', encoding='utf-8') as log_file:
                log_file.write(f"{datetime.now()}: خطأ في تصدير تقرير المورد Excel: {str(e)}\n")

    def export_supplier_report(self):
        """Main export function that now defaults to Excel"""
        self.export_supplier_report_excel()

    def print_direct(self, report_type):
        """Print report directly to printer without preview"""
        try:
            printer = QPrinter(QPrinter.PrinterMode.HighResolution)

            # Create QTextDocument to hold the report content
            document = QTextDocument()

            # Get the appropriate HTML content based on report type
            if report_type == "customer":
                html_content = self.generate_customer_report_html()
            elif report_type == "supplier":
                html_content = self.generate_supplier_report_html()
            elif report_type == "sales":
                html_content = self.generate_sales_report_html()
            elif report_type == "purchases":
                html_content = self.generate_purchases_report_html()
            elif report_type == "financial":
                html_content = self.generate_financial_report_html()
            else:
                QMessageBox.warning(self, "تنبيه", "نوع تقرير غير معروف")
                return

            document.setHtml(html_content)

            # Print directly to default printer
            document.print(printer)

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء الطباعة: {str(e)}")
            with open('error_log.txt', 'a', encoding='utf-8') as log_file:
                log_file.write(f"{datetime.now()}: خطأ في الطباعة المباشرة: {str(e)}\n")

    def setup_inventory_movement_tab(self):
        """Setup inventory movement report tab."""
        # Create layout for inventory movement tab
        layout = QVBoxLayout(self.inventory_movement_tab)

        # Create form layout for search criteria
        form_layout = QFormLayout()

        # Date range
        self.inventory_start_date = QDateEdit()
        self.inventory_start_date.setDate(QDate.currentDate().addMonths(-1))
        self.inventory_start_date.setCalendarPopup(True)
        form_layout.addRow("من تاريخ:", self.inventory_start_date)

        self.inventory_end_date = QDateEdit()
        self.inventory_end_date.setDate(QDate.currentDate())
        self.inventory_end_date.setCalendarPopup(True)
        form_layout.addRow("إلى تاريخ:", self.inventory_end_date)

        # Diamond type selection
        self.inventory_diamond_type = QComboBox()
        self.load_diamond_types_for_filter()
        form_layout.addRow("نوع الألماس:", self.inventory_diamond_type)

        # Search button
        search_button = QPushButton("عرض التقرير")
        search_button.setStyleSheet(self.btn_update_style)
        search_button.clicked.connect(self.generate_inventory_movement_report)

        # Search button
        search_button = QPushButton("عرض التقرير")
        search_button.setStyleSheet(self.btn_update_style)
        search_button.clicked.connect(self.generate_inventory_movement_report)

        # Add form layout to main layout
        layout.addLayout(form_layout)
        layout.addWidget(search_button)

        # Create table for inventory movement report
        self.inventory_movement_table = QTableWidget()
        self.inventory_movement_table.setColumnCount(9)
        self.inventory_movement_table.setHorizontalHeaderLabels([
            "نوع الألماس",
            "الرصيد الافتتاحي", "الرصيد الافتتاحي ($)",
            "المشتريات", "المشتريات ($)",
            "المبيعات", "المبيعات ($)",
            "المتبقي", "المتبقي ($)"
        ])
        header = self.inventory_movement_table.horizontalHeader()
        for i in range(9):
            header.setSectionResizeMode(i, QHeaderView.ResizeMode.Stretch)
        layout.addWidget(self.inventory_movement_table)

        # Create summary labels
        summary_layout = QHBoxLayout()

        self.inventory_total_purchases_weight_label = QLabel("إجمالي المشتريات: 0.000 قيراط")
        self.inventory_total_purchases_amount_label = QLabel("إجمالي المشتريات: 0.000 $")
        self.inventory_total_sales_weight_label = QLabel("إجمالي المبيعات: 0.000 قيراط")
        self.inventory_total_sales_amount_label = QLabel("إجمالي المبيعات: 0.000 $")

        summary_layout.addWidget(self.inventory_total_purchases_weight_label)
        summary_layout.addWidget(self.inventory_total_purchases_amount_label)
        summary_layout.addWidget(self.inventory_total_sales_weight_label)
        summary_layout.addWidget(self.inventory_total_sales_amount_label)

        layout.addLayout(summary_layout)

        # Create second row of summary labels
        summary_layout2 = QHBoxLayout()

        self.inventory_total_balance_weight_label = QLabel("إجمالي المتبقي: 0.000 قيراط")
        self.inventory_total_balance_amount_label = QLabel("إجمالي المتبقي: 0.000 $")

        summary_layout2.addWidget(self.inventory_total_balance_weight_label)
        summary_layout2.addWidget(self.inventory_total_balance_amount_label)

        layout.addLayout(summary_layout2)

        # Create export buttons
        export_layout = QHBoxLayout()

        export_html_button = QPushButton("تصدير HTML")
        export_html_button.setStyleSheet(self.btn_export_style)
        export_html_button.clicked.connect(self.export_inventory_movement_html_file)

        export_excel_button = QPushButton("تصدير Excel")
        export_excel_button.setStyleSheet(self.btn_export_style)
        export_excel_button.clicked.connect(self.export_inventory_movement_excel)

        print_button = QPushButton("طباعة")
        print_button.setStyleSheet(self.btn_print_style)
        print_button.clicked.connect(self.print_inventory_movement_report)

        export_layout.addWidget(export_html_button)
        export_layout.addWidget(export_excel_button)
        export_layout.addWidget(print_button)

        layout.addLayout(export_layout)

    def generate_inventory_movement_report(self):
        """Generate inventory movement report."""
        try:
            # Get date range
            start_date = self.inventory_start_date.date().toPyDate()
            end_date = self.inventory_end_date.date().toPyDate()
            end_date_time = datetime.combine(end_date, datetime.max.time())

            # Get diamond type filter
            diamond_type_filter = self.inventory_diamond_type.currentText()

            # Connect to database
            engine = create_engine('sqlite:///diamond_sales.db')
            Session = sessionmaker(bind=engine)
            session = Session()

            # Get all diamond types to report on
            diamond_types = []
            if diamond_type_filter == "جميع الأنواع":
                # Get unique diamond types from sales, purchases, and opening balances
                purchase_types = session.query(Purchase.diamond_type).distinct().all()
                sale_types = session.query(Sale.diamond_type).distinct().all()
                
                # Import OpeningBalance if available
                try:
                    from database import OpeningBalance
                    opening_balance_types = session.query(OpeningBalance.diamond_type).distinct().all()
                    opening_types = [t[0] for t in opening_balance_types]
                except:
                    opening_types = []

                # Combine and remove duplicates
                all_types = [t[0] for t in purchase_types] + [t[0] for t in sale_types] + opening_types
                diamond_types = list(set(all_types))
            else:
                diamond_types = [diamond_type_filter]

            # Clear table
            self.inventory_movement_table.setRowCount(len(diamond_types))

            # Initialize totals
            total_opening_weight = 0
            total_opening_amount = 0
            total_purchases_weight = 0
            total_purchases_amount = 0
            total_sales_weight = 0
            total_sales_amount = 0
            total_balance_weight = 0
            total_balance_amount = 0

            # Process each diamond type
            for row, diamond_type in enumerate(diamond_types):
                # Get opening balances for this diamond type
                opening_weight = 0
                opening_amount = 0
                try:
                    from database import OpeningBalance
                    opening_query = session.query(
                        func.sum(OpeningBalance.quantity).label('total_weight'),
                        func.sum(OpeningBalance.total_value_usd).label('total_amount')
                    ).filter(
                        OpeningBalance.diamond_type == diamond_type,
                        OpeningBalance.date_created <= end_date_time
                    )
                    
                    opening_result = opening_query.first()
                    opening_weight = opening_result.total_weight or 0
                    opening_amount = opening_result.total_amount or 0
                except:
                    pass

                # Get purchases for this diamond type
                purchases_query = session.query(
                    func.sum(Purchase.carat_weight).label('total_weight'),
                    func.sum(Purchase.total_price_usd).label('total_amount')
                ).filter(
                    Purchase.diamond_type == diamond_type,
                    Purchase.purchase_date >= start_date,
                    Purchase.purchase_date <= end_date_time
                )

                purchases_result = purchases_query.first()
                purchases_weight = purchases_result.total_weight or 0
                purchases_amount = purchases_result.total_amount or 0

                # Get sales for this diamond type
                sales_query = session.query(
                    func.sum(Sale.carat_weight).label('total_weight'),
                    func.sum(Sale.total_price_usd).label('total_amount')
                ).filter(
                    Sale.diamond_type == diamond_type,
                    Sale.sale_date >= start_date,
                    Sale.sale_date <= end_date_time
                )

                sales_result = sales_query.first()
                sales_weight = sales_result.total_weight or 0
                sales_amount = sales_result.total_amount or 0

                # Calculate balance weight (opening + purchases - sales)
                balance_weight = opening_weight + purchases_weight - sales_weight
                
                # Calculate weighted average price per carat for remaining balance
                total_weight = opening_weight + purchases_weight
                if total_weight > 0:
                    weighted_avg_price = (opening_amount + purchases_amount) / total_weight
                    balance_amount = balance_weight * weighted_avg_price
                else:
                    balance_amount = 0

                # Update totals
                total_opening_weight += opening_weight
                total_opening_amount += opening_amount
                total_purchases_weight += purchases_weight
                total_purchases_amount += purchases_amount
                total_sales_weight += sales_weight
                total_sales_amount += sales_amount
                total_balance_weight += balance_weight
                total_balance_amount += balance_amount

                # Add row to table
                self.inventory_movement_table.setItem(row, 0, QTableWidgetItem(diamond_type))
                self.inventory_movement_table.setItem(row, 1, QTableWidgetItem(f"{opening_weight:.3f}"))
                self.inventory_movement_table.setItem(row, 2, QTableWidgetItem(f"{opening_amount:.3f}"))
                self.inventory_movement_table.setItem(row, 3, QTableWidgetItem(f"{purchases_weight:.3f}"))
                self.inventory_movement_table.setItem(row, 4, QTableWidgetItem(f"{purchases_amount:.3f}"))
                self.inventory_movement_table.setItem(row, 5, QTableWidgetItem(f"{sales_weight:.3f}"))
                self.inventory_movement_table.setItem(row, 6, QTableWidgetItem(f"{sales_amount:.3f}"))
                self.inventory_movement_table.setItem(row, 7, QTableWidgetItem(f"{balance_weight:.3f}"))
                self.inventory_movement_table.setItem(row, 8, QTableWidgetItem(f"{balance_amount:.3f}"))

            # Update summary labels
            self.inventory_total_purchases_weight_label.setText(f"إجمالي المشتريات: {total_purchases_weight:.3f} قيراط")
            self.inventory_total_purchases_amount_label.setText(f"إجمالي المشتريات: {total_purchases_amount:.3f} $")
            self.inventory_total_sales_weight_label.setText(f"إجمالي المبيعات: {total_sales_weight:.3f} قيراط")
            self.inventory_total_sales_amount_label.setText(f"إجمالي المبيعات: {total_sales_amount:.3f} $")
            self.inventory_total_balance_weight_label.setText(f"إجمالي المتبقي: {total_balance_weight:.3f} قيراط")
            self.inventory_total_balance_amount_label.setText(f"إجمالي المتبقي: {total_balance_amount:.3f} $")

            session.close()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء إنشاء التقرير: {str(e)}")
            with open('error_log.txt', 'a', encoding='utf-8') as log_file:
                log_file.write(f"{datetime.now()}: خطأ في تقرير حركة الأصناف: {str(e)}\n")

    def generate_inventory_movement_html(self):
        """Generate HTML for inventory movement report."""
        try:
            # Get company info
            company_info = self.session.query(CompanyInfo).first()
            company_name = company_info.name if company_info else "نظام إدارة مبيعات الألماس"

            # Get date range
            start_date = self.inventory_start_date.date().toPyDate().strftime("%Y-%m-%d")
            end_date = self.inventory_end_date.date().toPyDate().strftime("%Y-%m-%d")

            # Build HTML content
            html = f"""<!DOCTYPE html>
            <html dir="rtl" lang="ar">
            <head>
                <meta charset="UTF-8">
                <title>تقرير حركة الأصناف</title>
                <style>
                    body {{ font-family: 'Arial', sans-serif; margin: 20px; direction: rtl; }}
                    .header {{ text-align: center; margin-bottom: 20px; }}
                    .title {{ font-size: 24px; font-weight: bold; }}
                    .subtitle {{ font-size: 18px; margin: 10px 0; }}
                    table {{ width: 100%; border-collapse: collapse; margin: 15px 0; }}
                    th, td {{ border: 1px solid #ddd; padding: 8px; text-align: right; }}
                    th {{ background-color: #f2f2f2; }}
                    tr:nth-child(even) {{ background-color: #f9f9f9; }}
                    .summary {{ margin-top: 20px; font-weight: bold; }}
                    .footer {{ margin-top: 30px; text-align: center; font-size: 12px; color: #777; }}
                </style>
            </head>
            <body>
                <div class="header">
                    <div class="title">{company_name}</div>
                    <div class="subtitle">تقرير حركة الأصناف</div>
                    <div>للفترة من {start_date} إلى {end_date}</div>
                </div>

                <table>
                    <thead>
                        <tr>
                            <th>نوع الألماس</th>
                            <th>المشتريات (قيراط)</th>
                            <th>المشتريات ($)</th>
                            <th>المبيعات (قيراط)</th>
                            <th>المبيعات ($)</th>
                            <th>المتبقي (قيراط)</th>
                            <th>المتبقي ($)</th>
                        </tr>
                    </thead>
                    <tbody>
            """

            # Add rows from table
            rows_count = self.inventory_movement_table.rowCount()
            for row in range(rows_count):
                diamond_type = self.inventory_movement_table.item(row, 0).text()
                purchases_weight = self.inventory_movement_table.item(row, 1).text()
                purchases_amount = self.inventory_movement_table.item(row, 2).text()
                sales_weight = self.inventory_movement_table.item(row, 3).text()
                sales_amount = self.inventory_movement_table.item(row, 4).text()
                balance_weight = self.inventory_movement_table.item(row, 5).text()
                balance_amount = self.inventory_movement_table.item(row, 6).text()

                html += f"""
                        <tr>
                            <td>{diamond_type}</td>
                            <td>{purchases_weight}</td>
                            <td>{purchases_amount}</td>
                            <td>{sales_weight}</td>
                            <td>{sales_amount}</td>
                            <td>{balance_weight}</td>
                            <td>{balance_amount}</td>
                        </tr>
                """

            # Add summary
            total_purchases_weight = self.inventory_total_purchases_weight_label.text().split(": ")[1].split(" ")[0]
            total_purchases_amount = self.inventory_total_purchases_amount_label.text().split(": ")[1].split(" ")[0]
            total_sales_weight = self.inventory_total_sales_weight_label.text().split(": ")[1].split(" ")[0]
            total_sales_amount = self.inventory_total_sales_amount_label.text().split(": ")[1].split(" ")[0]
            total_balance_weight = self.inventory_total_balance_weight_label.text().split(": ")[1].split(" ")[0]
            total_balance_amount = self.inventory_total_balance_amount_label.text().split(": ")[1].split(" ")[0]

            html += f"""
                    </tbody>
                    <tfoot>
                        <tr>
                            <th>الإجمالي</th>
                            <th>{total_purchases_weight} قيراط</th>
                            <th>{total_purchases_amount} $</th>
                            <th>{total_sales_weight} قيراط</th>
                            <th>{total_sales_amount} $</th>
                            <th>{total_balance_weight} قيراط</th>
                            <th>{total_balance_amount} $</th>
                        </tr>
                    </tfoot>
                </table>

                <div class="footer">
                    <p>تم إنشاء هذا التقرير في {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}</p>
                </div>
            </body>
            </html>
            """

            return html
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء إنشاء التقرير: {str(e)}")
            with open('error_log.txt', 'a', encoding='utf-8') as log_file:
                log_file.write(f"{datetime.now()}: خطأ في إنشاء HTML لتقرير حركة الأصناف: {str(e)}\n")
            return ""



    def export_inventory_movement_excel(self):
        """Export inventory movement report as Excel."""
        try:
            # Ask user for save location
            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ التقرير كملف Excel", "", "ملفات Excel (*.xlsx)"
            )

            if not file_path:
                return

            # Add .xlsx extension if not present
            if not file_path.endswith('.xlsx'):
                file_path += '.xlsx'

            # Create workbook
            workbook = xlsxwriter.Workbook(file_path)
            worksheet = workbook.add_worksheet("تقرير حركة الأصناف")

            # Add headers
            headers = [
                "نوع الألماس",
                "المشتريات (قيراط)", "المشتريات ($)",
                "المبيعات (قيراط)", "المبيعات ($)",
                "المتبقي (قيراط)", "المتبقي ($)"
            ]

            # Create formats
            header_format = workbook.add_format({
                'bold': True,
                'align': 'right',
                'valign': 'vcenter',
                'fg_color': '#D3D3D3',
                'border': 1
            })

            cell_format = workbook.add_format({
                'align': 'right',
                'valign': 'vcenter',
                'border': 1
            })

            # Write headers
            for col, header in enumerate(headers):
                worksheet.write(0, col, header, header_format)

            # Write data
            rows_count = self.inventory_movement_table.rowCount()
            for row in range(rows_count):
                for col in range(7):
                    value = self.inventory_movement_table.item(row, col).text()
                    worksheet.write(row + 1, col, value, cell_format)

            # Write totals
            total_row = rows_count + 1
            worksheet.write(total_row, 0, "الإجمالي", header_format)

            total_purchases_weight = self.inventory_total_purchases_weight_label.text().split(": ")[1].split(" ")[0]
            total_purchases_amount = self.inventory_total_purchases_amount_label.text().split(": ")[1].split(" ")[0]
            total_sales_weight = self.inventory_total_sales_weight_label.text().split(": ")[1].split(" ")[0]
            total_sales_amount = self.inventory_total_sales_amount_label.text().split(": ")[1].split(" ")[0]
            total_balance_weight = self.inventory_total_balance_weight_label.text().split(": ")[1].split(" ")[0]
            total_balance_amount = self.inventory_total_balance_amount_label.text().split(": ")[1].split(" ")[0]

            worksheet.write(total_row, 1, total_purchases_weight, header_format)
            worksheet.write(total_row, 2, total_purchases_amount, header_format)
            worksheet.write(total_row, 3, total_sales_weight, header_format)
            worksheet.write(total_row, 4, total_sales_amount, header_format)
            worksheet.write(total_row, 5, total_balance_weight, header_format)
            worksheet.write(total_row, 6, total_balance_amount, header_format)

            # Auto-fit columns
            for col in range(7):
                max_width = max(len(headers[col]), 15)
                worksheet.set_column(col, col, max_width)

            # Close workbook
            workbook.close()

            QMessageBox.information(self, "تم", f"تم تصدير التقرير بنجاح إلى {file_path}")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تصدير التقرير: {str(e)}")
            with open('error_log.txt', 'a', encoding='utf-8') as log_file:
                log_file.write(f"{datetime.now()}: خطأ في تصدير تقرير حركة الأصناف Excel: {str(e)}\n")

    def export_inventory_movement_html_file(self):
        """Export inventory movement report as HTML file and open in browser."""
        try:
            # Generate enhanced HTML content with better styling
            html_content = self.generate_enhanced_inventory_movement_html()
            if not html_content:
                return

            # Ask user for save location
            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ التقرير كملف HTML", "", "ملفات HTML (*.html)"
            )

            if not file_path:
                return

            # Add .html extension if not present
            if not file_path.endswith('.html'):
                file_path += '.html'

            # Write HTML content to file
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(html_content)

            # Open the HTML file in the default browser
            QDesktopServices.openUrl(QUrl.fromLocalFile(file_path))

            QMessageBox.information(self, "تم", f"تم تصدير التقرير بنجاح إلى {file_path} وفتحه في المتصفح")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تصدير التقرير: {str(e)}")
            with open('error_log.txt', 'a', encoding='utf-8') as log_file:
                log_file.write(f"{datetime.now()}: خطأ في تصدير تقرير حركة الأصناف HTML: {str(e)}\n")

    def export_sales_report_html(self):
        """Export sales report as HTML file and open in browser."""
        try:
            # Generate enhanced HTML content
            html_content = self.generate_sales_report_html()
            if not html_content:
                return

            # Ask user for save location
            file_path, _ = QFileDialog.getSaveFileName(
                self,
                "حفظ تقرير المبيعات",
                f"تقرير_المبيعات_{datetime.now().strftime('%Y%m%d')}.html",
                "HTML Files (*.html)"
            )

            if not file_path:
                return  # User canceled

            # Write HTML to file
            with open(file_path, 'w', encoding='utf-8') as file:
                file.write(html_content)

            # Open the HTML file in the default browser
            QDesktopServices.openUrl(QUrl.fromLocalFile(file_path))

            QMessageBox.information(
                self,
                "تم التصدير",
                f"تم تصدير تقرير المبيعات بنجاح إلى:\n{file_path} وفتحه في المتصفح"
            )

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تصدير التقرير: {str(e)}")
            with open('error_log.txt', 'a', encoding='utf-8') as log_file:
                log_file.write(f"{datetime.now()}: خطأ في تصدير تقرير المبيعات HTML: {str(e)}\n")

    def export_purchases_report_html(self):
        """Export purchases report as HTML file and open in browser."""
        try:
            # Generate enhanced HTML content
            html_content = self.generate_purchases_report_html()
            if not html_content:
                return

            # Ask user for save location
            file_path, _ = QFileDialog.getSaveFileName(
                self,
                "حفظ تقرير المشتريات",
                f"تقرير_المشتريات_{datetime.now().strftime('%Y%m%d')}.html",
                "HTML Files (*.html)"
            )

            if not file_path:
                return  # User canceled

            # Write HTML to file
            with open(file_path, 'w', encoding='utf-8') as file:
                file.write(html_content)

            # Open the HTML file in the default browser
            QDesktopServices.openUrl(QUrl.fromLocalFile(file_path))

            QMessageBox.information(
                self,
                "تم التصدير",
                f"تم تصدير تقرير المشتريات بنجاح إلى:\n{file_path} وفتحه في المتصفح"
            )

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تصدير التقرير: {str(e)}")
            with open('error_log.txt', 'a', encoding='utf-8') as log_file:
                log_file.write(f"{datetime.now()}: خطأ في تصدير تقرير المشتريات HTML: {str(e)}\n")

    def export_financial_report_html(self):
        """Export financial report as HTML file and open in browser."""
        try:
            # Generate enhanced HTML content
            html_content = self.generate_financial_report_html()
            if not html_content:
                return

            # Ask user for save location
            file_path, _ = QFileDialog.getSaveFileName(
                self,
                "حفظ التقرير المالي",
                f"التقرير_المالي_{datetime.now().strftime('%Y%m%d')}.html",
                "HTML Files (*.html)"
            )

            if not file_path:
                return  # User canceled

            # Write HTML to file
            with open(file_path, 'w', encoding='utf-8') as file:
                file.write(html_content)

            # Open the HTML file in the default browser
            QDesktopServices.openUrl(QUrl.fromLocalFile(file_path))

            QMessageBox.information(
                self,
                "تم التصدير",
                f"تم تصدير التقرير المالي بنجاح إلى:\n{file_path} وفتحه في المتصفح"
            )

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تصدير التقرير: {str(e)}")
            with open('error_log.txt', 'a', encoding='utf-8') as log_file:
                log_file.write(f"{datetime.now()}: خطأ في تصدير التقرير المالي HTML: {str(e)}\n")

    def generate_enhanced_inventory_movement_html(self):
        """Generate enhanced HTML with better styling for inventory movement report."""
        try:
            # Get company info
            company_info = self.session.query(CompanyInfo).first()
            company_name = company_info.name if company_info else "نظام إدارة مبيعات الألماس"
            company_logo = company_info.logo_path if company_info and company_info.logo_path else ""

            # Get date range
            start_date = self.inventory_start_date.date().toPyDate().strftime("%Y-%m-%d")
            end_date = self.inventory_end_date.date().toPyDate().strftime("%Y-%m-%d")

            # Build HTML content with enhanced styling
            html = f"""<!DOCTYPE html>
            <html dir="rtl" lang="ar">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>تقرير حركة الأصناف</title>
                <style>
                    @import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap');

                    body {{
                        font-family: 'Tajawal', Arial, sans-serif;
                        margin: 0;
                        padding: 20px;
                        direction: rtl;
                        background-color: #f9f9f9;
                        color: #333;
                    }}

                    .container {{
                        max-width: 1200px;
                        margin: 0 auto;
                        background-color: #fff;
                        padding: 30px;
                        border-radius: 10px;
                        box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
                    }}

                    .header {{
                        text-align: center;
                        margin-bottom: 30px;
                        padding-bottom: 20px;
                        border-bottom: 2px solid #eee;
                    }}

                    .logo {{
                        max-width: 150px;
                        margin-bottom: 15px;
                    }}

                    .title {{
                        font-size: 28px;
                        font-weight: 700;
                        color: #2c3e50;
                        margin-bottom: 10px;
                    }}

                    .subtitle {{
                        font-size: 20px;
                        font-weight: 500;
                        color: #3498db;
                        margin: 10px 0;
                    }}

                    .date-range {{
                        font-size: 16px;
                        color: #7f8c8d;
                        margin-top: 10px;
                    }}

                    table {{
                        width: 100%;
                        border-collapse: collapse;
                        margin: 25px 0;
                        font-size: 16px;
                        box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
                        border-radius: 5px;
                        overflow: hidden;
                    }}

                    th, td {{
                        padding: 8px 10px;
                        text-align: right;
                        font-size: 14px;
                    }}

                    th {{
                        background-color: #3498db;
                        color: white;
                        font-weight: 500;
                        text-transform: uppercase;
                        letter-spacing: 1px;
                    }}

                    tr:nth-child(even) {{
                        background-color: #f2f2f2;
                    }}

                    tr:hover {{
                        background-color: #e9f7fe;
                    }}

                    tfoot tr {{
                        background-color: #2c3e50;
                        color: white;
                        font-weight: bold;
                    }}

                    .summary {{
                        display: flex;
                        flex-wrap: wrap;
                        justify-content: space-between;
                        margin-top: 30px;
                        background-color: #f8f9fa;
                        padding: 20px;
                        border-radius: 5px;
                    }}

                    .summary-item {{
                        flex: 1;
                        min-width: 200px;
                        margin: 10px;
                        padding: 15px;
                        background-color: white;
                        border-radius: 5px;
                        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
                    }}

                    .summary-label {{
                        font-size: 14px;
                        color: #7f8c8d;
                        margin-bottom: 5px;
                    }}

                    .summary-value {{
                        font-size: 18px;
                        font-weight: 700;
                        color: #2c3e50;
                    }}

                    .purchases {{
                        color: #2ecc71;
                    }}

                    .sales {{
                        color: #e74c3c;
                    }}

                    .balance {{
                        color: #3498db;
                    }}

                    .footer {{
                        margin-top: 40px;
                        text-align: center;
                        font-size: 14px;
                        color: #95a5a6;
                        padding-top: 20px;
                        border-top: 1px solid #eee;
                    }}

                    @media print {{
                        body {{
                            font-size: 12px;
                        }}
                        .container {{
                            box-shadow: none;
                            padding: 10px;
                        }}
                        th, td {{
                            padding: 6px 8px;
                            font-size: 11px;
                        }}
                        .summary {{
                            display: none;
                        }}
                        table {{
                            page-break-inside: avoid;
                        }}
                    }}
                </style>
            </head>
            <body>
                <div class="container">
                    <div class="header">
                        {f'<img src="{company_logo}" alt="Company Logo" class="logo">' if company_logo else ''}
                        <div class="title">{company_name}</div>
                        <div class="subtitle">تقرير حركة الأصناف</div>
                        <div class="date-range">للفترة من {start_date} إلى {end_date}</div>
                    </div>

                    <table>
                        <thead>
                            <tr>
                                <th>نوع الألماس</th>
                                <th>الرصيد الافتتاحي (قيراط)</th>
                                <th>الرصيد الافتتاحي ($)</th>
                                <th>المشتريات (قيراط)</th>
                                <th>المشتريات ($)</th>
                                <th>المبيعات (قيراط)</th>
                                <th>المبيعات ($)</th>
                                <th>المتبقي (قيراط)</th>
                                <th>المتبقي ($)</th>
                            </tr>
                        </thead>
                        <tbody>
            """

            # Add rows from table
            rows_count = self.inventory_movement_table.rowCount()
            for row in range(rows_count):
                diamond_type = self.inventory_movement_table.item(row, 0).text()
                opening_weight = self.inventory_movement_table.item(row, 1).text()
                opening_amount = self.inventory_movement_table.item(row, 2).text()
                purchases_weight = self.inventory_movement_table.item(row, 3).text()
                purchases_amount = self.inventory_movement_table.item(row, 4).text()
                sales_weight = self.inventory_movement_table.item(row, 5).text()
                sales_amount = self.inventory_movement_table.item(row, 6).text()
                balance_weight = self.inventory_movement_table.item(row, 7).text()
                balance_amount = self.inventory_movement_table.item(row, 8).text()

                html += f"""
                            <tr>
                                <td>{diamond_type}</td>
                                <td>{opening_weight}</td>
                                <td>{opening_amount}</td>
                                <td>{purchases_weight}</td>
                                <td>{purchases_amount}</td>
                                <td>{sales_weight}</td>
                                <td>{sales_amount}</td>
                                <td>{balance_weight}</td>
                                <td>{balance_amount}</td>
                            </tr>
                """

            # Add summary - calculate totals from table data
            total_opening_weight = 0
            total_opening_amount = 0
            total_purchases_weight = 0
            total_purchases_amount = 0
            total_sales_weight = 0
            total_sales_amount = 0
            total_balance_weight = 0
            total_balance_amount = 0
            
            for row in range(rows_count):
                total_opening_weight += float(self.inventory_movement_table.item(row, 1).text())
                total_opening_amount += float(self.inventory_movement_table.item(row, 2).text())
                total_purchases_weight += float(self.inventory_movement_table.item(row, 3).text())
                total_purchases_amount += float(self.inventory_movement_table.item(row, 4).text())
                total_sales_weight += float(self.inventory_movement_table.item(row, 5).text())
                total_sales_amount += float(self.inventory_movement_table.item(row, 6).text())
                total_balance_weight += float(self.inventory_movement_table.item(row, 7).text())
                total_balance_amount += float(self.inventory_movement_table.item(row, 8).text())

            html += f"""
                        </tbody>
                        <tfoot>
                            <tr>
                                <th>الإجمالي</th>
                                <th>{total_opening_weight:.3f}</th>
                                <th>{total_opening_amount:.3f}</th>
                                <th>{total_purchases_weight:.3f}</th>
                                <th>{total_purchases_amount:.3f}</th>
                                <th>{total_sales_weight:.3f}</th>
                                <th>{total_sales_amount:.3f}</th>
                                <th>{total_balance_weight:.3f}</th>
                                <th>{total_balance_amount:.3f}</th>
                            </tr>
                        </tfoot>
                    </table>

                    <div class="summary">
                        <div class="summary-item">
                            <div class="summary-label">إجمالي الرصيد الافتتاحي (قيراط)</div>
                            <div class="summary-value balance">{total_opening_weight:.3f}</div>
                        </div>
                        <div class="summary-item">
                            <div class="summary-label">إجمالي الرصيد الافتتاحي ($)</div>
                            <div class="summary-value balance">{total_opening_amount:.3f}</div>
                        </div>
                        <div class="summary-item">
                            <div class="summary-label">إجمالي المشتريات (قيراط)</div>
                            <div class="summary-value purchases">{total_purchases_weight:.3f}</div>
                        </div>
                        <div class="summary-item">
                            <div class="summary-label">إجمالي المشتريات ($)</div>
                            <div class="summary-value purchases">{total_purchases_amount:.3f}</div>
                        </div>
                        <div class="summary-item">
                            <div class="summary-label">إجمالي المبيعات (قيراط)</div>
                            <div class="summary-value sales">{total_sales_weight:.3f}</div>
                        </div>
                        <div class="summary-item">
                            <div class="summary-label">إجمالي المبيعات ($)</div>
                            <div class="summary-value sales">{total_sales_amount:.3f}</div>
                        </div>
                        <div class="summary-item">
                            <div class="summary-label">إجمالي المتبقي (قيراط)</div>
                            <div class="summary-value balance">{total_balance_weight:.3f}</div>
                        </div>
                        <div class="summary-item">
                            <div class="summary-label">إجمالي المتبقي ($)</div>
                            <div class="summary-value balance">{total_balance_amount:.3f}</div>
                        </div>
                    </div>

                    <div class="footer">
                        <p>تم إنشاء هذا التقرير في {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}</p>
                        <p>نظام إدارة مبيعات الألماس</p>
                    </div>
                </div>
            </body>
            </html>
            """

            return html
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء إنشاء التقرير: {str(e)}")
            with open('error_log.txt', 'a', encoding='utf-8') as log_file:
                log_file.write(f"{datetime.now()}: خطأ في إنشاء HTML المحسن لتقرير حركة الأصناف: {str(e)}\n")
            return ""

    def print_inventory_movement_report(self):
        """Print inventory movement report by opening it in browser."""
        try:
            # Use the enhanced HTML content for better print output
            html_content = self.generate_enhanced_inventory_movement_html()
            if not html_content:
                return

            # Add print button to HTML content
            print_button_script = """
            <script>
                window.onload = function() {
                    // Add print button
                    var printBtn = document.createElement('button');
                    printBtn.innerHTML = 'طباعة التقرير';
                    printBtn.style.padding = '10px 20px';
                    printBtn.style.backgroundColor = '#3498db';
                    printBtn.style.color = 'white';
                    printBtn.style.border = 'none';
                    printBtn.style.borderRadius = '5px';
                    printBtn.style.cursor = 'pointer';
                    printBtn.style.fontSize = '16px';
                    printBtn.style.margin = '20px auto';
                    printBtn.style.display = 'block';

                    printBtn.onclick = function() {
                        this.style.display = 'none';
                        window.print();
                        setTimeout(function() {
                            printBtn.style.display = 'block';
                        }, 1000);
                    };

                    document.body.insertBefore(printBtn, document.body.firstChild);
                };
            </script>
            """

            # Insert the script before the closing </head> tag
            html_content = html_content.replace('</head>', f'{print_button_script}</head>')

            # Create a temporary HTML file
            import tempfile
            import os

            with tempfile.NamedTemporaryFile(delete=False, suffix='.html', mode='w', encoding='utf-8') as f:
                f.write(html_content)
                temp_path = f.name

            # Open the file in the default browser
            QDesktopServices.openUrl(QUrl.fromLocalFile(os.path.realpath(temp_path)))

            QMessageBox.information(self, "تم", "تم فتح التقرير في المتصفح، يمكنك الطباعة من هناك")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء طباعة التقرير: {str(e)}")
            with open('error_log.txt', 'a', encoding='utf-8') as log_file:
                log_file.write(f"{datetime.now()}: خطأ في طباعة تقرير حركة الأصناف: {str(e)}\n")



    def generate_enhanced_html_template(self, title, subtitle, date_range, content, summary=None, extra_info=None):
        """
        Generate enhanced HTML template with consistent styling for all reports.

        Args:
            title (str): Report title
            subtitle (str): Report subtitle
            date_range (str): Date range string
            content (str): Main HTML content (table body)
            summary (str, optional): Summary HTML content
            extra_info (dict, optional): Additional information to display

        Returns:
            str: Complete HTML document with enhanced styling
        """
        try:
            # Get company info
            company_info = self.session.query(CompanyInfo).first()
            company_name = company_info.name if company_info else "نظام إدارة مبيعات الألماس"
            company_logo = company_info.logo_path if company_info and company_info.logo_path else ""

            # Build HTML content with enhanced styling
            html = f"""<!DOCTYPE html>
            <html dir="rtl" lang="ar">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>{title}</title>
                <style>
                    @import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap');

                    body {{
                        font-family: 'Tajawal', Arial, sans-serif;
                        margin: 0;
                        padding: 20px;
                        direction: rtl;
                        background-color: #f9f9f9;
                        color: #333;
                    }}

                    .container {{
                        max-width: 1200px;
                        margin: 0 auto;
                        background-color: #fff;
                        padding: 30px;
                        border-radius: 10px;
                        box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
                    }}

                    .header {{
                        text-align: center;
                        margin-bottom: 30px;
                        padding-bottom: 20px;
                        border-bottom: 2px solid #eee;
                    }}

                    .logo {{
                        max-width: 150px;
                        margin-bottom: 15px;
                    }}

                    .title {{
                        font-size: 28px;
                        font-weight: 700;
                        color: #2c3e50;
                        margin-bottom: 10px;
                    }}

                    .subtitle {{
                        font-size: 20px;
                        font-weight: 500;
                        color: #3498db;
                        margin: 10px 0;
                    }}

                    .date-range {{
                        font-size: 16px;
                        color: #7f8c8d;
                        margin-top: 10px;
                    }}

                    .info-box {{
                        margin-bottom: 20px;
                        padding: 15px;
                        background-color: #f8f9fa;
                        border-radius: 5px;
                        border-right: 4px solid #3498db;
                    }}

                    .info-box h3 {{
                        margin-top: 0;
                        color: #3498db;
                    }}

                    table {{
                        width: 100%;
                        border-collapse: collapse;
                        margin: 25px 0;
                        font-size: 16px;
                        box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
                        border-radius: 5px;
                        overflow: hidden;
                    }}

                    th, td {{
                        padding: 12px 15px;
                        text-align: center;
                    }}

                    th {{
                        background-color: #3498db;
                        color: white;
                        font-weight: 500;
                        text-transform: uppercase;
                        letter-spacing: 1px;
                    }}

                    tr:nth-child(even) {{
                        background-color: #f2f2f2;
                    }}

                    tr:hover {{
                        background-color: #e9f7fe;
                    }}

                    tfoot tr {{
                        background-color: #2c3e50;
                        color: white;
                        font-weight: bold;
                    }}

                    .summary {{
                        display: flex;
                        flex-wrap: wrap;
                        justify-content: space-between;
                        margin-top: 30px;
                        background-color: #f8f9fa;
                        padding: 20px;
                        border-radius: 5px;
                    }}

                    .summary-item {{
                        flex: 1;
                        min-width: 200px;
                        margin: 10px;
                        padding: 15px;
                        background-color: white;
                        border-radius: 5px;
                        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
                    }}

                    .summary-container {{
                        width: 100%;
                        max-width: none;
                        margin-left: 0;
                        margin-right: 0;
                    }}

                    .summary-label {{
                        font-size: 14px;
                        color: #7f8c8d;
                        margin-bottom: 5px;
                    }}

                    .summary-value {{
                        font-size: 18px;
                        font-weight: 700;
                        color: #2c3e50;
                    }}

                    .positive {{
                        color: #2ecc71;
                    }}

                    .negative {{
                        color: #e74c3c;
                    }}

                    .neutral {{
                        color: #3498db;
                    }}

                    .inventory {{
                        color: #9b59b6;
                        background-color: #f8f4fd;
                    }}

                    .footer {{
                        margin-top: 40px;
                        text-align: center;
                        font-size: 14px;
                        color: #95a5a6;
                        padding-top: 20px;
                        border-top: 1px solid #eee;
                    }}

                    /* Print styles for multi-page reports */
                    @media print {{
                        .summary {{
                            page-break-inside: avoid;
                            break-inside: avoid;
                        }}

                        .summary-container {{
                            page-break-inside: avoid;
                            break-inside: avoid;
                        }}

                        /* Ensure table footer appears on last page only for multi-page reports */
                        tfoot {{
                            page-break-inside: avoid;
                            break-inside: avoid;
                        }}

                        /* Page break settings */
                        table {{
                            page-break-inside: auto;
                        }}

                        tr {{
                            page-break-inside: avoid;
                            page-break-after: auto;
                        }}

                        thead {{
                            display: table-header-group;
                        }}

                        tfoot {{
                            display: table-footer-group;
                        }}
                    }}
                </style>
            </head>
            <body>
                <div class="container">
                    <div class="header">
                        {f'<img src="{company_logo}" alt="Company Logo" class="logo">' if company_logo else ''}
                        <div class="title">{company_name}</div>
                        <div class="subtitle">{subtitle}</div>
                        <div class="date-range">{date_range}</div>
                    </div>

                    {f'<div class="info-box">{extra_info}</div>' if extra_info else ''}

                    {content}

                    {f'<div class="summary">{summary}</div>' if summary else ''}

                    <div class="footer">
                        <p>تم إنشاء هذا التقرير بواسطة: {self.user.username}</p>
                        <p>تاريخ الطباعة: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}</p>
                    </div>
                </div>
            </body>
            </html>
            """

            return html
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء إنشاء قالب HTML: {str(e)}")
            with open('error_log.txt', 'a', encoding='utf-8') as log_file:
                log_file.write(f"{datetime.now()}: خطأ في إنشاء قالب HTML المحسن: {str(e)}\n")
            return ""

    def setup_vouchers_report_tab(self):
        """إعداد تاب تقرير السندات"""
        layout = QVBoxLayout(self.vouchers_report_tab)

        # عنوان التقرير
        title_label = QLabel("تقرير السندات")
        title_font = QFont()
        title_font.setPointSize(14)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title_label)

        # فلاتر التقرير
        filter_group = QGroupBox("خيارات البحث")
        filter_layout = QHBoxLayout()

        # تاريخ البداية
        start_date_layout = QFormLayout()
        self.voucher_start_date = QDateEdit()
        self.voucher_start_date.setDate(QDate.currentDate().addMonths(-1))
        self.voucher_start_date.setCalendarPopup(True)
        start_date_layout.addRow("من تاريخ:", self.voucher_start_date)
        filter_layout.addLayout(start_date_layout)

        # تاريخ النهاية
        end_date_layout = QFormLayout()
        self.voucher_end_date = QDateEdit()
        self.voucher_end_date.setDate(QDate.currentDate())
        self.voucher_end_date.setCalendarPopup(True)
        end_date_layout.addRow("إلى تاريخ:", self.voucher_end_date)
        filter_layout.addLayout(end_date_layout)

        # فلتر نوع السند
        voucher_type_layout = QFormLayout()
        self.voucher_type_combo = QComboBox()
        self.voucher_type_combo.addItem("جميع السندات", "all")
        self.voucher_type_combo.addItem("سندات القبض", "CashIn")
        self.voucher_type_combo.addItem("سندات الصرف", "CashOut")
        voucher_type_layout.addRow("نوع السند:", self.voucher_type_combo)
        filter_layout.addLayout(voucher_type_layout)

        # فلتر العميل/المورد
        name_filter_layout = QFormLayout()
        self.voucher_name_filter = QLineEdit()
        self.voucher_name_filter.setPlaceholderText("اسم العميل أو المورد")
        name_filter_layout.addRow("العميل/المورد:", self.voucher_name_filter)
        filter_layout.addLayout(name_filter_layout)

        # زر تحديث التقرير
        self.update_voucher_report_btn = QPushButton("تحديث التقرير")
        self.update_voucher_report_btn.setStyleSheet(self.btn_update_style)
        self.update_voucher_report_btn.clicked.connect(self.generate_vouchers_report)
        filter_layout.addWidget(self.update_voucher_report_btn)

        # زر طباعة التقرير
        self.print_voucher_report_btn = QPushButton("طباعة التقرير")
        self.print_voucher_report_btn.setStyleSheet(self.btn_print_style)
        self.print_voucher_report_btn.clicked.connect(lambda: self.print_report(self.generate_vouchers_report_html()))
        filter_layout.addWidget(self.print_voucher_report_btn)

        # زر تصدير HTML
        self.export_voucher_html_btn = QPushButton("تصدير HTML")
        self.export_voucher_html_btn.setStyleSheet(self.btn_export_style)
        self.export_voucher_html_btn.clicked.connect(self.export_vouchers_report_html)
        filter_layout.addWidget(self.export_voucher_html_btn)

        filter_group.setLayout(filter_layout)
        layout.addWidget(filter_group)

        # جدول تقرير السندات
        self.vouchers_table = QTableWidget()
        self.vouchers_table.setColumnCount(7)
        self.vouchers_table.setHorizontalHeaderLabels([
            "رقم السند", "نوع السند", "المرجع", "العميل/المورد",
            "المبلغ ($)", "المبلغ (ريال)", "تاريخ السند"
        ])
        self.vouchers_table.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        layout.addWidget(self.vouchers_table)

        # ملخص السندات
        summary_group = QGroupBox("ملخص السندات")
        summary_layout = QGridLayout()

        self.voucher_count_label = QLabel("عدد السندات: 0")
        self.cash_in_total_usd_label = QLabel("إجمالي سندات القبض ($): 0.00")
        self.cash_in_total_sar_label = QLabel("إجمالي سندات القبض (ريال): 0.00")
        self.cash_out_total_usd_label = QLabel("إجمالي سندات الصرف ($): 0.00")
        self.cash_out_total_sar_label = QLabel("إجمالي سندات الصرف (ريال): 0.00")
        self.net_cash_flow_usd_label = QLabel("صافي التدفق النقدي ($): 0.00")
        self.net_cash_flow_sar_label = QLabel("صافي التدفق النقدي (ريال): 0.00")

        summary_layout.addWidget(self.voucher_count_label, 0, 0)
        summary_layout.addWidget(self.cash_in_total_usd_label, 1, 0)
        summary_layout.addWidget(self.cash_in_total_sar_label, 1, 1)
        summary_layout.addWidget(self.cash_out_total_usd_label, 2, 0)
        summary_layout.addWidget(self.cash_out_total_sar_label, 2, 1)
        summary_layout.addWidget(self.net_cash_flow_usd_label, 3, 0)
        summary_layout.addWidget(self.net_cash_flow_sar_label, 3, 1)

        summary_group.setLayout(summary_layout)
        layout.addWidget(summary_group)

        # تحديث التقرير الأولي
        self.generate_vouchers_report()

    def generate_vouchers_report(self):
        """إنشاء تقرير السندات بناءً على المعايير المحددة"""
        try:
            # الحصول على معايير البحث
            start_date = self.voucher_start_date.date().toPyDate()
            end_date = self.voucher_end_date.date().toPyDate()
            # تعديل النهاية لتشمل كامل اليوم
            end_date_time = datetime.combine(end_date, datetime.max.time())

            voucher_type = self.voucher_type_combo.currentData()
            name_filter = self.voucher_name_filter.text().strip()

            # إنشاء استعلام للسندات
            voucher_query = self.session.query(Receipt).filter(
                Receipt.issue_date.between(start_date, end_date_time)
            )

            # تطبيق فلتر نوع السند إذا لم يكن "الكل"
            if voucher_type != "all":
                voucher_query = voucher_query.filter(Receipt.receipt_type == voucher_type)

            # تنفيذ الاستعلام
            vouchers = voucher_query.all()

            # تحضير بيانات الجدول
            table_data = []

            # إحصائيات
            total_count = 0
            cash_in_total_usd = 0
            cash_in_total_sar = 0
            cash_out_total_usd = 0
            cash_out_total_sar = 0

            # معالجة نتائج الاستعلام
            for voucher in vouchers:
                entity_name = "غير محدد"
                reference = "بدون مرجع"

                # تحديد العميل/المورد والمرجع
                if voucher.sale_id:
                    # سند مرتبط بعملية بيع
                    sale = self.session.query(Sale).get(voucher.sale_id)
                    if sale and sale.customer:
                        entity_name = sale.customer.name
                        reference = f"مبيعات #{voucher.sale_id}"                        # تطبيق فلتر الاسم إذا كان موجودًا
                        if name_filter and name_filter.lower() not in entity_name.lower():
                            continue
                elif voucher.purchase_id:
                    # سند مرتبط بعملية شراء
                    purchase = self.session.query(Purchase).get(voucher.purchase_id)
                    if purchase and purchase.supplier:
                        entity_name = purchase.supplier.name
                        reference = f"مشتريات #{voucher.purchase_id}"

                        # تطبيق فلتر الاسم إذا كان موجودًا
                        if name_filter and name_filter.lower() not in entity_name.lower():
                            continue
                elif voucher.customer_id:
                    # سند قبض بدون مرجع ولكن مرتبط بعميل
                    customer = self.session.query(Customer).get(voucher.customer_id)
                    if customer:
                        entity_name = customer.name
                        reference = "بدون مرجع"

                        # تطبيق فلتر الاسم إذا كان موجودًا
                        if name_filter and name_filter.lower() not in entity_name.lower():
                            continue
                elif voucher.supplier_id:
                    # سند صرف بدون مرجع ولكن مرتبط بمورد
                    supplier = self.session.query(Supplier).get(voucher.supplier_id)
                    if supplier:
                        entity_name = supplier.name
                        reference = "بدون مرجع"

                        # تطبيق فلتر الاسم إذا كان موجودًا
                        if name_filter and name_filter.lower() not in entity_name.lower():
                            continue
                elif name_filter:
                    # تخطي السندات بدون مرجع وبدون عميل/مورد إذا كان هناك فلتر اسم
                    continue

                # تحديد نوع السند
                voucher_type_str = "قبض" if voucher.receipt_type == "CashIn" else "صرف"

                # إحصاء السندات                total_count += 1
                if voucher.receipt_type == "CashIn":
                    cash_in_total_usd += voucher.amount_usd if voucher.amount_usd is not None else 0
                    cash_in_total_sar += voucher.amount_sar if voucher.amount_sar is not None else 0
                else:
                    cash_out_total_usd += voucher.amount_usd if voucher.amount_usd is not None else 0
                    cash_out_total_sar += voucher.amount_sar if voucher.amount_sar is not None else 0

                # إضافة البيانات إلى القائمة
                table_data.append({
                    "id": voucher.id,
                    "type": voucher_type_str,
                    "reference": reference,
                    "name": entity_name,
                    "amount_usd": voucher.amount_usd,
                    "amount_sar": voucher.amount_sar,
                    "date": voucher.issue_date
                })

            # فرز البيانات حسب التاريخ (الأحدث أولاً)
            table_data.sort(key=lambda x: x["date"] if x["date"] else datetime.min, reverse=True)

            # تحديث الجدول
            self.vouchers_table.setRowCount(len(table_data))

            for row, data in enumerate(table_data):
                self.vouchers_table.setItem(row, 0, QTableWidgetItem(str(data["id"])))
                self.vouchers_table.setItem(row, 1, QTableWidgetItem(data["type"]))
                self.vouchers_table.setItem(row, 2, QTableWidgetItem(data["reference"]))
                self.vouchers_table.setItem(row, 3, QTableWidgetItem(data["name"]))
                self.vouchers_table.setItem(row, 4, QTableWidgetItem(f"{data['amount_usd']:.3f}"))
                self.vouchers_table.setItem(row, 5, QTableWidgetItem(f"{data['amount_sar']:.3f}"))

                # تنسيق التاريخ
                date_str = data["date"].strftime("%d/%m/%Y") if data["date"] else ""
                self.vouchers_table.setItem(row, 6, QTableWidgetItem(date_str))

            # حساب صافي التدفق النقدي
            net_cash_flow_usd = cash_in_total_usd - cash_out_total_usd
            net_cash_flow_sar = cash_in_total_sar - cash_out_total_sar

            # تحديث ملصقات الملخص
            self.voucher_count_label.setText(f"عدد السندات: {total_count}")
            self.cash_in_total_usd_label.setText(f"إجمالي سندات القبض ($): {cash_in_total_usd:.3f}")
            self.cash_in_total_sar_label.setText(f"إجمالي سندات القبض (ريال): {cash_in_total_sar:.3f}")
            self.cash_out_total_usd_label.setText(f"إجمالي سندات الصرف ($): {cash_out_total_usd:.3f}")
            self.cash_out_total_sar_label.setText(f"إجمالي سندات الصرف (ريال): {cash_out_total_sar:.3f}")
            self.net_cash_flow_usd_label.setText(f"صافي التدفق النقدي ($): {net_cash_flow_usd:.3f}")
            self.net_cash_flow_sar_label.setText(f"صافي التدفق النقدي (ريال): {net_cash_flow_sar:.3f}")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء إنشاء تقرير السندات: {str(e)}")
            with open('error_log.txt', 'a', encoding='utf-8') as log_file:
                log_file.write(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] خطأ في تقرير السندات: {str(e)}\n")

    def generate_vouchers_report_html(self):
        """إنشاء تقرير السندات بتنسيق HTML للطباعة والتصدير"""
        try:
            # الحصول على نطاق التاريخ
            start_date = self.voucher_start_date.date().toString("yyyy-MM-dd")
            end_date = self.voucher_end_date.date().toString("yyyy-MM-dd")
            date_range = f"للفترة من {start_date} إلى {end_date}"

            # الحصول على فلتر نوع السند
            voucher_type = self.voucher_type_combo.currentText()

            # بناء محتوى الجدول
            table_content = """
            <table>
                <thead>
                    <tr>
                        <th>رقم السند</th>
                        <th>نوع السند</th>
                        <th>المرجع</th>
                        <th>العميل/المورد</th>
                        <th>المبلغ ($)</th>
                        <th>المبلغ (ريال)</th>
                        <th>تاريخ السند</th>
                    </tr>
                </thead>
                <tbody>
            """

            # استخراج البيانات من الجدول
            rows_count = self.vouchers_table.rowCount()

            # إذا لم تكن هناك بيانات
            if rows_count == 0:
                table_content += """
                <tr>
                    <td colspan="7" style="text-align: center;">لا توجد سندات تطابق معايير البحث</td>
                </tr>
                """
            else:
                for row in range(rows_count):
                    voucher_id = self.vouchers_table.item(row, 0).text()
                    voucher_type_cell = self.vouchers_table.item(row, 1).text()
                    reference = self.vouchers_table.item(row, 2).text()
                    name = self.vouchers_table.item(row, 3).text()
                    amount_usd = self.vouchers_table.item(row, 4).text()
                    amount_sar = self.vouchers_table.item(row, 5).text()
                    date = self.vouchers_table.item(row, 6).text()

                    # تحديد لون الخلية بناءً على نوع السند
                    row_color = "#e8f7f2" if voucher_type_cell == "قبض" else "#fdf3f3"

                    table_content += f"""
                    <tr style="background-color: {row_color};">
                        <td>{voucher_id}</td>
                        <td>{voucher_type_cell}</td>
                        <td>{reference}</td>
                        <td>{name}</td>
                        <td>{amount_usd}</td>
                        <td>{amount_sar}</td>
                        <td>{date}</td>
                    </tr>
                    """

            # إغلاق الجدول
            table_content += """
                </tbody>
            </table>
            """

            # بناء محتوى الملخص
            voucher_count = self.voucher_count_label.text().split(": ")[1]
            cash_in_total_usd = self.cash_in_total_usd_label.text().split(": ")[1]
            cash_in_total_sar = self.cash_in_total_sar_label.text().split(": ")[1]
            cash_out_total_usd = self.cash_out_total_usd_label.text().split(": ")[1]
            cash_out_total_sar = self.cash_out_total_sar_label.text().split(": ")[1]
            net_cash_flow_usd = self.net_cash_flow_usd_label.text().split(": ")[1]
            net_cash_flow_sar = self.net_cash_flow_sar_label.text().split(": ")[1]

            # تحديد فئة لصافي التدفق النقدي (إيجابي أو سلبي)
            net_cash_class = "positive" if float(net_cash_flow_usd.replace(',', '')) >= 0 else "negative"

            summary_content = f"""
                <div class="summary-item">
                    <div class="summary-label">عدد السندات</div>
                    <div class="summary-value neutral">{voucher_count}</div>
                </div>
                <div class="summary-item">
                    <div class="summary-label">إجمالي سندات القبض ($)</div>
                    <div class="summary-value positive">{cash_in_total_usd}</div>
                </div>
                <div class="summary-item">
                    <div class="summary-label">إجمالي سندات القبض (ريال)</div>
                    <div class="summary-value positive">{cash_in_total_sar}</div>
                </div>
                <div class="summary-item">
                    <div class="summary-label">إجمالي سندات الصرف ($)</div>
                    <div class="summary-value negative">{cash_out_total_usd}</div>
                </div>
                <div class="summary-item">
                    <div class="summary-label">إجمالي سندات الصرف (ريال)</div>
                    <div class="summary-value negative">{cash_out_total_sar}</div>
                </div>
                <div class="summary-item">
                    <div class="summary-label">صافي التدفق النقدي ($)</div>
                    <div class="summary-value {net_cash_class}">{net_cash_flow_usd}</div>
                </div>
                <div class="summary-item">
                    <div class="summary-label">صافي التدفق النقدي (ريال)</div>
                    <div class="summary-value {net_cash_class}">{net_cash_flow_sar}</div>
                </div>
            """

            # إنشاء تقرير HTML كامل باستخدام القالب
            subtitle = "تقرير السندات"
            if voucher_type != "جميع السندات":
                subtitle += f" - {voucher_type}"

            # إنشاء محتوى HTML كامل
            html = f"""<!DOCTYPE html>
            <html dir="rtl" lang="ar">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>تقرير السندات</title>
                <style>
                    @import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap');

                    body {{
                        font-family: 'Tajawal', Arial, sans-serif;
                        margin: 0;
                        padding: 20px;
                        direction: rtl;
                        background-color: #f9f9f9;
                        color: #333;
                    }}

                    .container {{
                        max-width: 1200px;
                        margin: 0 auto;
                        background-color: #fff;
                        padding: 30px;
                        border-radius: 10px;
                        box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
                    }}

                    .header {{
                        text-align: center;
                        margin-bottom: 30px;
                        padding-bottom: 20px;
                        border-bottom: 2px solid #eee;
                    }}

                    .title {{
                        font-size: 28px;
                        font-weight: 700;
                        color: #2c3e50;
                        margin-bottom: 10px;
                    }}

                    .subtitle {{
                        font-size: 20px;
                        font-weight: 500;
                        color: #3498db;
                        margin: 10px 0;
                    }}

                    .date-range {{
                        font-size: 16px;
                        color: #7f8c8d;
                        margin-top: 10px;
                    }}

                    table {{
                        width: 100%;
                        border-collapse: collapse;
                        margin: 25px 0;
                        font-size: 15px;
                    }}

                    th, td {{
                        padding: 12px 15px;
                        text-align: right;
                        border: 1px solid #ddd;
                    }}

                    th {{
                        background-color: #3498db;
                        color: white;
                        font-weight: 500;
                    }}

                    tr:hover {{
                        background-color: #f5f5f5;
                    }}

                    .summary {{
                        display: flex;
                        flex-wrap: wrap;
                        justify-content: space-between;
                        margin-top: 30px;
                        background-color: #f8f9fa;
                        padding: 20px;
                        border-radius: 5px;
                    }}

                    .summary-item {{
                        flex: 1;
                        min-width: 200px;
                        margin: 10px;
                        padding: 15px;
                        background-color: white;
                        border-radius: 5px;
                        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
                    }}

                    .summary-label {{
                        font-size: 14px;
                        color: #7f8c8d;
                        margin-bottom: 5px;
                    }}

                    .summary-value {{
                        font-size: 18px;
                        font-weight: 700;
                    }}

                    .positive {{
                        color: #2ecc71;
                    }}

                    .negative {{
                        color: #e74c3c;
                    }}

                    .neutral {{
                        color: #3498db;
                    }}

                    .footer {{
                        margin-top: 40px;
                        text-align: center;
                        font-size: 14px;
                        color: #95a5a6;
                        padding-top: 20px;
                        border-top: 1px solid #eee;
                    }}

                    @media print {{
                        body {{
                            background-color: white;
                            margin: 0;
                            padding: 0;
                        }}
                        .container {{
                            box-shadow: none;
                            max-width: 100%;
                            padding: 15px;
                        }}
                        table {{
                            page-break-inside: auto;
                        }}
                        tr {{
                            page-break-inside: avoid;
                            page-break-after: auto;
                        }}
                    }}
                </style>
            </head>
            <body>
                <div class="container">
                    <div class="header">
                        <div class="title">نظام إدارة مبيعات الألماس</div>
                        <div class="subtitle">{subtitle}</div>
                        <div class="date-range">{date_range}</div>
                    </div>

                    {table_content}

                    <div class="summary">
                        {summary_content}
                    </div>

                    <div class="footer">
                        <p>تم إنشاء هذا التقرير في {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}</p>
                    </div>
                </div>
            </body>
            </html>
            """

            return html
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء إنشاء تقرير السندات بتنسيق HTML: {str(e)}")
            with open('error_log.txt', 'a', encoding='utf-8') as log_file:
                log_file.write(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] خطأ في إنشاء تقرير السندات HTML: {str(e)}\n")
            return ""

    def export_vouchers_report_html(self):
        """تصدير تقرير السندات كملف HTML وفتحه في المتصفح"""
        try:
            # إنشاء محتوى HTML
            html_content = self.generate_vouchers_report_html()
            if not html_content:
                return

            # سؤال المستخدم عن مكان حفظ الملف
            file_path, _ = QFileDialog.getSaveFileName(
                self,
                "حفظ تقرير السندات",
                f"تقرير_السندات_{datetime.now().strftime('%Y%m%d')}.html",
                "HTML Files (*.html)"
            )

            if not file_path:
                return

            # كتابة محتوى HTML إلى الملف
            with open(file_path, 'w', encoding='utf-8') as file:
                file.write(html_content)

            # فتح الملف في المتصفح الافتراضي
            QDesktopServices.openUrl(QUrl.fromLocalFile(file_path))

            QMessageBox.information(
                self,
                "تم التصدير",
                f"تم تصدير تقرير السندات بنجاح إلى:\n{file_path} وفتحه في المتصفح"
            )

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تصدير التقرير: {str(e)}")
            with open('error_log.txt', 'a', encoding='utf-8') as log_file:
                log_file.write(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] خطأ في تصدير تقرير السندات: {str(e)}\n")
