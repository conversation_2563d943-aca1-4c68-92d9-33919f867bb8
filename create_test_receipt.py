from database import صندوق_النقدية, حركة_نقدية, Receipt, Customer, Supplier
from db_session import session_scope
from datetime import datetime
import traceback
import sys

print("Script started")

def create_test_receipt():
    try:
        print("Creating a test receipt voucher...")
        sys.stdout.flush()
        
        with session_scope() as session:
            print("Session created")
            sys.stdout.flush()
            
            # إنشاء سند إيداع من عميل
            # Find a customer for the receipt
            customer = session.query(Customer).first()
            if not customer:
                print("No customers found in the database. Creating a test customer.")
                customer = Customer(name="Test Customer", phone="123456789")
                session.add(customer)
                session.flush()
            
            print(f"Using customer: {customer.name} (ID: {customer.id})")
            sys.stdout.flush()
            
            # Create a receipt voucher for deposit
            receipt = Receipt(
                customer_id=customer.id,
                receipt_type="CashIn",
                amount_usd=100.00,
                amount_sar=375.00,  # Assuming exchange rate of 3.75
                issue_date=datetime.now()
            )
            session.add(receipt)
            session.flush()  # To get the receipt ID
            
            print(f"Created deposit receipt voucher ID: {receipt.id}")
            sys.stdout.flush()
              # إنشاء سند صرف (سحب) باللغة العربية
            supplier = session.query(Supplier).first()
            supplier_id = supplier.id if supplier else None
            
            withdrawal_receipt_ar = Receipt(
                supplier_id=supplier_id,
                receipt_type="صرف",
                amount_usd=50.00,
                issue_date=datetime.now()
            )
            session.add(withdrawal_receipt_ar)
            session.flush()
            print(f"Created Arabic withdrawal receipt ID: {withdrawal_receipt_ar.id}")
            
            # إنشاء سند صرف (سحب) باللغة الإنجليزية
            withdrawal_receipt_en = Receipt(
                receipt_type="CashOut",
                amount_usd=25.00,
                issue_date=datetime.now()
            )
            session.add(withdrawal_receipt_en)
            session.flush()
            print(f"Created English withdrawal receipt ID: {withdrawal_receipt_en.id}")
            
            # Update cash box
            cash_box = session.query(صندوق_النقدية).first()
            if not cash_box:
                print("No cash box found. Creating a new cash box.")
                cash_box = صندوق_النقدية(balance=0.0, last_updated=datetime.now())
                session.add(cash_box)
                session.flush()
            
            # Update cash box balance
            new_balance = cash_box.balance + receipt.amount_usd
            cash_box.balance = new_balance
            cash_box.last_updated = datetime.now()
            
            print(f"Updated cash box balance: ${new_balance:.2f}")
            sys.stdout.flush()
            
            # Create cash transaction
            transaction = حركة_نقدية(
                cash_box_id=cash_box.id,
                transaction_type="deposit",
                amount=receipt.amount_usd,
                balance_after=new_balance,
                transaction_date=receipt.issue_date,
                reference=f"سند قبض رقم {receipt.id}",
                description=f"سند قبض من العميل - {customer.name}",
                created_by=None  # No user in this test
            )
            session.add(transaction)
            
            print(f"Created cash transaction for receipt")
            sys.stdout.flush()
            
            # Commit changes
            session.commit()
            print("Successfully committed all changes")
            sys.stdout.flush()
            
            # Display updated cash box status
            print("\nUpdated Cash Box Status:")
            cash_box = session.query(صندوق_النقدية).first()
            print(f"Cash Box ID: {cash_box.id}, Balance: ${cash_box.balance:.2f}")
            sys.stdout.flush()
            
            # Display transaction
            transaction = session.query(حركة_نقدية).order_by(حركة_نقدية.id.desc()).first()
            print("\nCreated Transaction:")
            print(f"ID: {transaction.id}, Type: {transaction.transaction_type}, Amount: ${transaction.amount:.2f}")
            print(f"Balance After: ${transaction.balance_after:.2f}")
            print(f"Reference: {transaction.reference}")
            print(f"Description: {transaction.description}")
            sys.stdout.flush()
            
    except Exception as e:
        print(f"Error occurred: {str(e)}")
        traceback.print_exc()
        sys.stdout.flush()

if __name__ == "__main__":
    print("Starting main function")
    sys.stdout.flush()
    create_test_receipt()
    print("\nTest completed")
    sys.stdout.flush()
