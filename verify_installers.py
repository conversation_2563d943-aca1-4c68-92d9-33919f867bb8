#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Diamond Sales V1.30 - Installers Verification
التحقق من برامج التثبيت المُنشأة
"""

import os
import subprocess
from pathlib import Path

def print_status(message, status="INFO"):
    """طباعة رسالة حالة بتنسيق منظم"""
    symbols = {
        "INFO": "ℹ️",
        "SUCCESS": "✅", 
        "ERROR": "❌",
        "WARNING": "⚠️"
    }
    print(f"{symbols.get(status, 'ℹ️')} {message}")

def verify_installer_files():
    """التحقق من ملفات برامج التثبيت"""
    print("\n" + "="*60)
    print("🔍 التحقق من برامج التثبيت المُنشأة")
    print("="*60)
    
    installer_output_dir = Path("installer_output")
    
    # قائمة برامج التثبيت المتوقعة
    expected_installers = [
        {
            "file": "DiamondSalesMainProgram_v1.30_Setup.exe",
            "description": "برنامج تثبيت البرنامج الرئيسي",
            "target_users": "العملاء العاديين",
            "min_size_mb": 30,
            "max_size_mb": 50
        },
        {
            "file": "DiamondSalesActivationGenerator_v1.30_Setup.exe", 
            "description": "برنامج تثبيت مولد أكواد التفعيل",
            "target_users": "الموزعين والوكلاء",
            "min_size_mb": 30,
            "max_size_mb": 50
        },
        {
            "file": "DiamondSalesPasswordReset_v1.30_Setup.exe",
            "description": "برنامج تثبيت أداة إعادة تعيين كلمة المرور", 
            "target_users": "فنيي الدعم الفني",
            "min_size_mb": 40,
            "max_size_mb": 50
        }
    ]
    
    # قائمة برامج التثبيت القديمة (يمكن إزالتها)
    legacy_installers = [
        "DiamondSalesSetup_v1.30.exe",
        "DiamondSalesSetup_1.30.exe",
        "DiamondSalesSetup1.30.exe"
    ]
    
    verified_installers = []
    total_size = 0
    
    print("📦 فحص برامج التثبيت الجديدة:")
    for installer in expected_installers:
        installer_path = installer_output_dir / installer["file"]
        
        if installer_path.exists():
            size = installer_path.stat().st_size
            size_mb = size / 1024 / 1024
            total_size += size
            
            # التحقق من الحجم
            if installer["min_size_mb"] <= size_mb <= installer["max_size_mb"]:
                size_status = "✅"
            else:
                size_status = "⚠️"
            
            print(f"   {size_status} {installer['file']}")
            print(f"      📄 الوصف: {installer['description']}")
            print(f"      👥 المستخدمون: {installer['target_users']}")
            print(f"      📏 الحجم: {size_mb:.1f} MB")
            print()
            
            verified_installers.append(installer)
        else:
            print_status(f"❌ مفقود: {installer['file']}", "ERROR")
    
    print("📂 برامج التثبيت القديمة (يمكن إزالتها):")
    for legacy_file in legacy_installers:
        legacy_path = installer_output_dir / legacy_file
        if legacy_path.exists():
            size_mb = legacy_path.stat().st_size / 1024 / 1024
            print(f"   🗂️ {legacy_file} ({size_mb:.1f} MB) - برنامج التثبيت الشامل القديم")
    
    return verified_installers, total_size

def verify_iss_files():
    """التحقق من ملفات .iss"""
    print("\n" + "="*60)
    print("📝 التحقق من ملفات Inno Setup")
    print("="*60)
    
    iss_files = [
        {
            "file": "diamond_sales_main_installer.iss",
            "description": "ملف إعداد البرنامج الرئيسي"
        },
        {
            "file": "activation_generator_installer.iss",
            "description": "ملف إعداد مولد التفعيل"
        },
        {
            "file": "reset_password_installer.iss",
            "description": "ملف إعداد أداة إعادة التعيين"
        }
    ]
    
    verified_iss = []
    
    for iss_info in iss_files:
        iss_path = Path(iss_info["file"])
        
        if iss_path.exists():
            size = iss_path.stat().st_size
            print_status(f"{iss_info['description']}: موجود ({size:,} بايت)", "SUCCESS")
            
            # فحص محتوى الملف
            try:
                with open(iss_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    if "[Setup]" in content and "AppName=" in content:
                        print_status(f"  المحتوى: صحيح", "SUCCESS")
                        verified_iss.append(iss_info)
                    else:
                        print_status(f"  المحتوى: غير مكتمل", "WARNING")
            except Exception as e:
                print_status(f"  خطأ في قراءة الملف: {str(e)}", "ERROR")
        else:
            print_status(f"{iss_info['description']}: مفقود", "ERROR")
    
    return verified_iss

def generate_installers_report():
    """إنشاء تقرير شامل لبرامج التثبيت"""
    print("\n" + "="*60)
    print("📊 تقرير برامج التثبيت النهائي")
    print("="*60)
    
    # فحص برامج التثبيت
    verified_installers, total_size = verify_installer_files()
    
    # فحص ملفات .iss
    verified_iss = verify_iss_files()
    
    # حساب النتائج
    expected_installers = 3
    expected_iss = 3
    
    print(f"\n📈 الإحصائيات:")
    print(f"   - برامج التثبيت المُنشأة: {len(verified_installers)}/{expected_installers}")
    print(f"   - ملفات .iss المُنشأة: {len(verified_iss)}/{expected_iss}")
    print(f"   - الحجم الإجمالي: {total_size/1024/1024:.1f} MB")
    
    # تقييم عام
    all_installers_ok = len(verified_installers) == expected_installers
    all_iss_ok = len(verified_iss) == expected_iss
    overall_success = all_installers_ok and all_iss_ok
    
    if overall_success:
        print_status("جميع برامج التثبيت جاهزة! 🎉", "SUCCESS")
        
        print("\n🎯 ملفات التوزيع الجاهزة:")
        for installer in verified_installers:
            print(f"   📦 {installer['file']}")
            print(f"      👥 للـ: {installer['target_users']}")
        
        print("\n💡 مميزات برامج التثبيت:")
        print("   ✅ تثبيت منفصل لكل برنامج")
        print("   ✅ واجهة تثبيت احترافية")
        print("   ✅ رسائل تأكيد وتعليمات")
        print("   ✅ اختصارات في قائمة ابدأ وسطح المكتب")
        print("   ✅ إمكانية إلغاء التثبيت")
        
        print("\n📋 تعليمات التوزيع:")
        print("   1. وزع البرنامج الرئيسي للعملاء العاديين")
        print("   2. وزع مولد التفعيل للموزعين المعتمدين فقط")
        print("   3. وزع أداة إعادة التعيين لفرق الدعم الفني")
        print("   4. تأكد من قراءة التعليمات قبل التثبيت")
        
    else:
        print_status("يوجد مشاكل في بعض برامج التثبيت", "WARNING")
        
        if not all_installers_ok:
            print_status(f"برامج التثبيت: {len(verified_installers)}/{expected_installers} فقط", "WARNING")
        
        if not all_iss_ok:
            print_status(f"ملفات .iss: {len(verified_iss)}/{expected_iss} فقط", "WARNING")
    
    return overall_success

def create_distribution_guide():
    """إنشاء دليل التوزيع"""
    print("\n" + "="*60)
    print("📚 إنشاء دليل التوزيع")
    print("="*60)
    
    guide_content = """# دليل توزيع برامج تثبيت مبيعات الألماس - الإصدار 1.30

## نظرة عامة
تم إنشاء ثلاثة برامج تثبيت منفصلة لنظام مبيعات الألماس، كل برنامج مخصص لفئة معينة من المستخدمين.

## برامج التثبيت المتاحة

### 1. البرنامج الرئيسي 💎
**الملف:** `DiamondSalesMainProgram_v1.30_Setup.exe`
**الحجم:** ~33 MB
**المستخدمون المستهدفون:** العملاء العاديين

**المحتويات:**
- البرنامج الأساسي لإدارة المبيعات
- قاعدة البيانات الأساسية
- جميع الأصول والترجمات
- واجهة المستخدم الكاملة

**التثبيت:**
- تشغيل البرنامج كمسؤول
- اتباع تعليمات معالج التثبيت
- كلمة المرور الافتراضية: admin/admin

### 2. مولد أكواد التفعيل 🔑
**الملف:** `DiamondSalesActivationGenerator_v1.30_Setup.exe`
**الحجم:** ~32 MB
**المستخدمون المستهدفون:** الموزعين والوكلاء المعتمدين

**المحتويات:**
- أداة إنشاء أكواد التفعيل
- واجهة إدارة التراخيص
- نظام ربط التراخيص بالعملاء

**تحذيرات أمنية:**
⚠️ للموظفين المعتمدين فقط
⚠️ لا تشارك أكواد التفعيل مع أطراف غير مصرح لها
⚠️ احتفظ بسجل للتراخيص المُصدرة

### 3. أداة إعادة تعيين كلمة المرور 🔓
**الملف:** `DiamondSalesPasswordReset_v1.30_Setup.exe`
**الحجم:** ~44 MB
**المستخدمون المستهدفون:** فنيي الدعم الفني

**المحتويات:**
- أداة إعادة تعيين كلمة المرور
- أداة استرداد الوصول للنظام

**طريقة الاستخدام:**
1. إغلاق البرنامج الرئيسي
2. نسخ الأداة لمجلد البرنامج الرئيسي
3. تشغيل الأداة لإعادة تعيين كلمة المرور
4. كلمة المرور الجديدة ستكون: admin

## متطلبات النظام

### لجميع البرامج
- **نظام التشغيل:** Windows 10 أو أحدث
- **الذاكرة:** 4 جيجابايت رام (2 جيجابايت للأدوات المساعدة)
- **المساحة:** 200 ميجابايت للبرنامج الرئيسي، 100 ميجابايت للأدوات
- **الصلاحيات:** صلاحيات المسؤول للتثبيت

## استراتيجيات التوزيع

### التوزيع العادي
**للعملاء الأفراد والشركات الصغيرة:**
- وزع `DiamondSalesMainProgram_v1.30_Setup.exe` فقط
- قدم الدعم الفني المباشر
- أعط كود تفعيل مُسبق

### التوزيع للموزعين
**للوكلاء والموزعين المعتمدين:**
- وزع البرنامج الرئيسي + مولد التفعيل
- قدم تدريب على استخدام مولد التفعيل
- وضع سياسات واضحة لإصدار التراخيص

### التوزيع لفرق الدعم
**لشركات الدعم الفني:**
- وزع الثلاث برامج كاملة
- قدم تدريب متقدم على استكشاف الأخطاء
- وضع بروتوكولات للطوارئ

## تعليمات التثبيت التفصيلية

### قبل التثبيت
1. تأكد من إغلاق أي نسخة قديمة من البرنامج
2. عمل نسخة احتياطية من البيانات الموجودة
3. تشغيل برنامج التثبيت كمسؤول
4. التأكد من اتصال الإنترنت لتحديث المكونات

### أثناء التثبيت
1. اختيار مجلد التثبيت المناسب
2. قراءة رسائل التأكيد بعناية
3. اختيار إنشاء اختصارات سطح المكتب حسب الحاجة
4. انتظار اكتمال التثبيت بالكامل

### بعد التثبيت
1. تشغيل البرنامج للتأكد من عمله
2. تغيير كلمة المرور الافتراضية
3. استيراد البيانات المحفوظة إن وجدت
4. تفعيل البرنامج بالكود المناسب

## استكشاف الأخطاء الشائعة

### مشكلة: "فشل في التثبيت"
**الحلول:**
- تشغيل البرنامج كمسؤول
- إيقاف مكافح الفيروسات مؤقتاً
- تنظيف ملفات النظام المؤقتة
- إعادة تحميل برنامج التثبيت

### مشكلة: "البرنامج لا يبدأ بعد التثبيت"
**الحلول:**
- التحقق من تثبيت Visual C++ Redistributable
- فحص ملفات السجلات للأخطاء
- إعادة تثبيت البرنامج
- تحديث تعريفات النظام

### مشكلة: "خطأ في قاعدة البيانات"
**الحلول:**
- التأكد من صلاحيات الكتابة في المجلد
- فحص توفر مساحة القرص الصلب
- استخدام أداة إعادة تعيين كلمة المرور
- استعادة النسخة الاحتياطية

## الأمان والنسخ الاحتياطية

### أفضل الممارسات
- ✅ عمل نسخة احتياطية أسبوعية من البيانات
- ✅ استخدام كلمات مرور قوية
- ✅ تحديث البرنامج بانتظام
- ✅ تدريب المستخدمين على الاستخدام الآمن

### النسخ الاحتياطية
- موقع النسخ: مجلد البرنامج\\backups
- تكرار النسخ: تلقائي يومياً
- أنواع النسخ: قاعدة البيانات + الإعدادات
- استعادة النسخ: من قائمة الأدوات

## الدعم الفني

### مستويات الدعم
1. **الدعم الأساسي:** للعملاء العاديين
2. **الدعم المتقدم:** للموزعين
3. **الدعم الفني:** لفرق الدعم

### معلومات الاتصال
- البريد الإلكتروني: <EMAIL>
- الهاتف: +1-234-567-8900
- الموقع: www.diamondsales.com/support
- ساعات العمل: 24/7 للطوارئ

---
**تاريخ الإصدار:** مايو 2025
**رقم الإصدار:** 1.30
**نوع التوزيع:** برامج تثبيت منفصلة
"""
    
    with open("installer_output/DISTRIBUTION_GUIDE.txt", "w", encoding="utf-8") as f:
        f.write(guide_content)
    
    print_status("تم إنشاء دليل التوزيع: DISTRIBUTION_GUIDE.txt", "SUCCESS")

def main():
    """الدالة الرئيسية"""
    print("🚀 التحقق من برامج التثبيت المُنشأة")
    print("="*70)
    
    # التأكد من وجود مجلد المخرجات
    if not os.path.exists("installer_output"):
        print_status("لم يتم العثور على مجلد installer_output", "ERROR")
        return False
    
    # التحقق من برامج التثبيت
    success = generate_installers_report()
    
    # إنشاء دليل التوزيع
    create_distribution_guide()
    
    if success:
        print("\n🎉 جميع برامج التثبيت جاهزة للتوزيع!")
        print("\n📁 الملفات النهائية:")
        print("   📦 DiamondSalesMainProgram_v1.30_Setup.exe")
        print("   🔑 DiamondSalesActivationGenerator_v1.30_Setup.exe")
        print("   🔓 DiamondSalesPasswordReset_v1.30_Setup.exe")
        print("   📚 DISTRIBUTION_GUIDE.txt")
    
    return success

if __name__ == "__main__":
    main()
