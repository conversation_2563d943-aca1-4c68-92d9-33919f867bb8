import sqlite3
import os
import datetime
import shutil

# Crear una copia de seguridad de la base de datos actual
def backup_database():
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_dir = "backups"
    
    # Crear directorio de copias de seguridad si no existe
    if not os.path.exists(backup_dir):
        os.makedirs(backup_dir)
    
    backup_filename = os.path.join(backup_dir, f"diamond_sales_backup_{timestamp}.db")
    
    # Verificar si el archivo original existe
    if not os.path.exists('diamond_sales.db'):
        print("Error: No se encontró la base de datos original.")
        return False
    
    # Copiar el archivo
    try:
        shutil.copy2('diamond_sales.db', backup_filename)
        print(f"Copia de seguridad creada: {backup_filename}")
        return True
    except Exception as e:
        print(f"Error al crear la copia de seguridad: {str(e)}")
        return False

# Vaciar las tablas de la base de datos
def reset_database():
    # Tablas que se mantendrán con datos
    keep_tables = ['users', 'company_info']
    
    # Tablas que se vaciarán pero se mantendrán algunas configuraciones
    partial_reset = ['settings']
    
    # Conectar a la base de datos
    conn = sqlite3.connect('diamond_sales.db')
    cursor = conn.cursor()
    
    # Obtener la lista de tablas
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
    tables = cursor.fetchall()
    
    # Desactivar las restricciones de clave foránea temporalmente
    cursor.execute("PRAGMA foreign_keys = OFF")
    
    # Iniciar una transacción
    conn.execute("BEGIN TRANSACTION")
    
    try:
        # Vaciar cada tabla excepto las que están en keep_tables
        for table in tables:
            table_name = table[0]
            
            if table_name in keep_tables or table_name.startswith('sqlite_'):
                print(f"Manteniendo tabla: {table_name}")
                continue
                
            elif table_name in partial_reset:
                print(f"Reiniciando configuraciones en tabla: {table_name}")
                # Mantener solo configuraciones básicas
                if table_name == 'settings':
                    cursor.execute("UPDATE settings SET value = '1' WHERE key = 'exchange_rate'")
                    # Otras configuraciones que quieras mantener...
                    continue
            
            # Vaciar completamente la tabla
            print(f"Vaciando tabla: {table_name}")
            cursor.execute(f"DELETE FROM {table_name}")
        
        # Confirmar los cambios
        conn.commit()
        print("Base de datos reiniciada correctamente.")
    except Exception as e:
        # Revertir los cambios en caso de error
        conn.rollback()
        print(f"Error al reiniciar la base de datos: {str(e)}")
    finally:
        # Reactivar las restricciones de clave foránea
        cursor.execute("PRAGMA foreign_keys = ON")
        # Cerrar la conexión
        conn.close()

# Limpiar archivos temporales y logs
def clean_temp_files():
    try:
        # Limpiar logs de errores pero mantener el archivo
        if os.path.exists('error_log.txt'):
            with open('error_log.txt', 'w') as f:
                f.write(f"# Log reiniciado el {datetime.datetime.now()}\n")
            print("Archivo de log de errores limpiado.")
        
        # Otros archivos temporales que quieras limpiar
        temp_files = []
        for file in temp_files:
            if os.path.exists(file):
                os.remove(file)
                print(f"Archivo temporal eliminado: {file}")
        
    except Exception as e:
        print(f"Error al limpiar archivos temporales: {str(e)}")

# Función principal
def main():
    print("=== Utilidad para reiniciar el sistema ===")
    print("ADVERTENCIA: Esta operación eliminará todos los datos excepto usuarios e información de la empresa.")
    print("Se creará una copia de seguridad antes de proceder.")
    
    confirmation = input("¿Está seguro de que desea continuar? (s/n): ")
    
    if confirmation.lower() != 's':
        print("Operación cancelada.")
        return
    
    # Crear copia de seguridad
    if backup_database():
        # Reiniciar la base de datos
        reset_database()
        # Limpiar archivos temporales
        clean_temp_files()
        print("\nEl sistema ha sido reiniciado correctamente.")
        print("Puede iniciar el programa con datos nuevos.")
    else:
        print("No se pudo crear la copia de seguridad. Operación cancelada.")

if __name__ == "__main__":
    main()
