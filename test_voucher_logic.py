#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
اختبار مبسط لتقارير السندات مع الإصلاحات الجديدة
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from database import Receipt, Customer, Supplier, Sale, Purchase
from datetime import datetime, timedelta

def test_voucher_report_logic():
    """اختبار منطق تقرير السندات"""
    print("بدء اختبار منطق تقرير السندات...")
    
    # إنشاء الجلسة
    engine = create_engine('sqlite:///diamond_sales.db')
    Session = sessionmaker(bind=engine)
    session = Session()
    
    try:
        # تحديد نطاق التواريخ
        start_date = datetime(2024, 1, 1)
        end_date = datetime(2024, 12, 31)
        end_date_time = datetime.combine(end_date.date(), datetime.max.time())
        
        print(f"البحث عن السندات من {start_date.date()} إلى {end_date.date()}")
        
        # الحصول على السندات
        voucher_query = session.query(Receipt).filter(
            Receipt.issue_date.between(start_date, end_date_time)
        )
        
        vouchers = voucher_query.all()
        print(f"تم العثور على {len(vouchers)} سند")
        
        # معالجة كل سند
        voucher_data = []
        
        for voucher in vouchers:
            print(f"\nمعالجة السند {voucher.id}:")
            print(f"  - النوع: {voucher.receipt_type}")
            print(f"  - المبلغ USD: {voucher.amount_usd}")
            print(f"  - المبلغ SAR: {voucher.amount_sar}")
            print(f"  - sale_id: {voucher.sale_id}")
            print(f"  - purchase_id: {voucher.purchase_id}")
            print(f"  - customer_id: {voucher.customer_id}")
            print(f"  - supplier_id: {voucher.supplier_id}")
            
            # تطبيق منطق الإصلاح الجديد
            entity_name = "غير محدد"
            reference = "غير محدد"
            
            # فحص المبيعات أولاً
            if voucher.sale_id:
                sale = session.query(Sale).get(voucher.sale_id)
                if sale and sale.customer_id:
                    customer = session.query(Customer).get(sale.customer_id)
                    if customer:
                        entity_name = customer.name
                        reference = f"فاتورة رقم {sale.id}"
                        print(f"  ✓ مرتبط بمبيعة: العميل = {entity_name}")
            
            # فحص المشتريات
            elif voucher.purchase_id:
                purchase = session.query(Purchase).get(voucher.purchase_id)
                if purchase and purchase.supplier_id:
                    supplier = session.query(Supplier).get(purchase.supplier_id)
                    if supplier:
                        entity_name = supplier.name
                        reference = f"فاتورة شراء رقم {purchase.id}"
                        print(f"  ✓ مرتبط بمشترى: المورد = {entity_name}")
            
            # فحص الارتباط المباشر بالعملاء (الإصلاح الجديد)
            elif voucher.customer_id:
                customer = session.query(Customer).get(voucher.customer_id)
                if customer:
                    entity_name = customer.name
                    reference = "بدون مرجع"
                    print(f"  ✓ مرتبط مباشرة بعميل: {entity_name}")
            
            # فحص الارتباط المباشر بالموردين (الإصلاح الجديد)
            elif voucher.supplier_id:
                supplier = session.query(Supplier).get(voucher.supplier_id)
                if supplier:
                    entity_name = supplier.name
                    reference = "بدون مرجع"
                    print(f"  ✓ مرتبط مباشرة بمورد: {entity_name}")
            
            else:
                print(f"  ⚠ سند غير مرتبط بأي كيان")
            
            # إضافة بيانات السند
            voucher_info = {
                'id': voucher.id,
                'type': voucher.receipt_type or 'غير محدد',
                'entity_name': entity_name,
                'reference': reference,
                'amount_usd': voucher.amount_usd or 0,
                'amount_sar': voucher.amount_sar or 0,
                'date': voucher.issue_date
            }
            
            voucher_data.append(voucher_info)
        
        # عرض النتائج النهائية
        print(f"\n{'='*50}")
        print("ملخص السندات المعالجة:")
        print(f"{'='*50}")
        
        for i, voucher in enumerate(voucher_data, 1):
            print(f"{i}. السند {voucher['id']}")
            print(f"   النوع: {voucher['type']}")
            print(f"   العميل/المورد: {voucher['entity_name']}")
            print(f"   المرجع: {voucher['reference']}")
            print(f"   المبلغ: {voucher['amount_usd']} دولار / {voucher['amount_sar']} ريال")
            print()
        
        # اختبار فلتر الأسماء
        print("اختبار فلتر الأسماء...")
        customers = session.query(Customer).all()
        for customer in customers:
            filtered_vouchers = [v for v in voucher_data 
                               if customer.name.lower() in v['entity_name'].lower()]
            print(f"سندات العميل '{customer.name}': {len(filtered_vouchers)}")
        
    except Exception as e:
        print(f"خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        session.close()

if __name__ == "__main__":
    test_voucher_report_logic()
