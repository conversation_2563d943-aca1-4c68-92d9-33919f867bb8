#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
برنامج لاختبار وظائف صندوق النقدية
"""

import sys
from PyQt6.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, 
                             QPushButton, QLabel, QWidget, QTextEdit, QMessageBox)
from PyQt6.QtCore import Qt
from sqlalchemy import func, desc
from db_session import session_scope
from database import صندوق_النقدية, حركة_نقدية, Receipt
from datetime import datetime
import json

class CashBoxTester(QMainWindow):
    """نافذة اختبار وظائف صندوق النقدية"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("اختبار صندوق النقدية")
        self.setGeometry(100, 100, 800, 600)
        
        # إنشاء التخطيط الرئيسي
        main_widget = QWidget()
        self.setCentralWidget(main_widget)
        
        main_layout = QVBoxLayout(main_widget)
        
        # إنشاء أزرار الاختبار
        buttons_layout = QHBoxLayout()
        
        self.test_cashbox_btn = QPushButton("اختبار صندوق النقدية")
        self.test_cashbox_btn.clicked.connect(self.test_cashbox)
        self.test_cashbox_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                border-radius: 4px;
                font-size: 12pt;
                font-weight: bold;
                padding: 5px 15px;
            }
            QPushButton:hover {
                background-color: #2ecc71;
            }
            QPushButton:pressed {
                background-color: #219653;
            }
        """)
        self.test_withdrawals_btn = QPushButton("اختبار سندات الصرف")
        self.test_withdrawals_btn.clicked.connect(self.test_withdrawals)
        self.test_withdrawals_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                border-radius: 4px;
                font-size: 12pt;
                font-weight: bold;
                padding: 5px 15px;
            }
            QPushButton:hover {
                background-color: #ff7675;
            }
            QPushButton:pressed {
                background-color: #c0392b;
            }
        """)
        self.test_deposits_btn = QPushButton("اختبار سندات القبض")
        self.test_deposits_btn.clicked.connect(self.test_deposits)
        self.test_deposits_btn.setStyleSheet("""
            QPushButton {
                background-color: #f39c12;
                color: white;
                border: none;
                border-radius: 4px;
                font-size: 12pt;
                font-weight: bold;
                padding: 5px 15px;
            }
            QPushButton:hover {
                background-color: #f7b731;
            }
            QPushButton:pressed {
                background-color: #e67e22;
            }
        """)
        self.fix_all_btn = QPushButton("إصلاح جميع المشكلات")
        self.fix_all_btn.clicked.connect(self.fix_all_issues)
        self.fix_all_btn.setStyleSheet("""
            QPushButton {
                background-color: #9b59b6;
                color: white;
                border: none;
                border-radius: 4px;
                font-size: 12pt;
                font-weight: bold;
                padding: 5px 15px;
            }
            QPushButton:hover {
                background-color: #b084cc;
            }
            QPushButton:pressed {
                background-color: #8e44ad;
            }
        """)
        buttons_layout.addWidget(self.test_cashbox_btn)
        buttons_layout.addWidget(self.test_withdrawals_btn)
        buttons_layout.addWidget(self.test_deposits_btn)
        buttons_layout.addWidget(self.fix_all_btn)
        
        main_layout.addLayout(buttons_layout)
        
        # إنشاء ملصق لإظهار الرصيد الحالي
        self.balance_label = QLabel("الرصيد الحالي: 0.00 $")
        self.balance_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.balance_label.setStyleSheet("font-size: 14pt; font-weight: bold; margin: 10px;")
        main_layout.addWidget(self.balance_label)
        
        # إنشاء مربع النص لعرض النتائج
        self.results_text = QTextEdit()
        self.results_text.setReadOnly(True)
        self.results_text.setStyleSheet("direction: rtl; font-size: 12pt;")
        main_layout.addWidget(self.results_text)
        
        # تحديث الرصيد الحالي
        self.update_balance()
    
    def update_balance(self):
        """تحديث عرض الرصيد الحالي"""
        try:
            with session_scope() as session:
                cash_box = session.query(صندوق_النقدية).first()
                if cash_box:
                    self.balance_label.setText(f"الرصيد الحالي: {cash_box.balance:.2f} $")
                else:
                    self.balance_label.setText("الرصيد الحالي: لا يوجد صندوق")
        except Exception as e:
            self.balance_label.setText(f"خطأ: {str(e)}")
    
    def test_cashbox(self):
        """اختبار صندوق النقدية"""
        try:
            with session_scope() as session:
                cash_box = session.query(صندوق_النقدية).first()
                
                if not cash_box:
                    self.results_text.setText("لا يوجد صندوق نقدية في قاعدة البيانات!")
                    return
                
                # الحصول على إحصائيات
                total_transactions = session.query(حركة_نقدية).count()
                deposit_transactions = session.query(حركة_نقدية).filter(
                    حركة_نقدية.transaction_type == "deposit"
                ).count()
                withdraw_transactions = session.query(حركة_نقدية).filter(
                    حركة_نقدية.transaction_type == "withdraw"
                ).count()
                
                total_deposits = session.query(func.sum(حركة_نقدية.amount)).filter(
                    حركة_نقدية.transaction_type == "deposit"
                ).scalar() or 0
                
                total_withdrawals = session.query(func.sum(حركة_نقدية.amount)).filter(
                    حركة_نقدية.transaction_type == "withdraw"
                ).scalar() or 0
                
                calculated_balance = total_deposits - total_withdrawals
                
                # التحقق من صحة رصيد الصندوق
                is_balance_correct = abs(calculated_balance - cash_box.balance) < 0.001
                
                # إعداد التقرير
                report = f"""
                معلومات صندوق النقدية:
                
                - معرّف الصندوق: {cash_box.id}
                - الرصيد الحالي: {cash_box.balance:.2f} $
                - آخر تحديث: {cash_box.last_updated.strftime('%Y-%m-%d %H:%M:%S')}
                
                إحصائيات الحركات:
                
                - إجمالي عدد الحركات: {total_transactions}
                - عدد الإيداعات: {deposit_transactions}
                - عدد السحوبات: {withdraw_transactions}
                
                المبالغ:
                
                - إجمالي الإيداعات: {total_deposits:.2f} $
                - إجمالي السحوبات: {total_withdrawals:.2f} $
                - الرصيد المحسوب: {calculated_balance:.2f} $
                
                التحقق من الرصيد: {"✓ صحيح" if is_balance_correct else "✗ غير صحيح"}
                """
                
                self.results_text.setText(report)
                
                # تحديث الرصيد
                self.update_balance()
                
        except Exception as e:
            self.results_text.setText(f"حدث خطأ أثناء اختبار صندوق النقدية:\n{str(e)}")
    
    def test_withdrawals(self):
        """اختبار سندات الصرف وعلاقتها بحركات النقدية"""
        try:
            with session_scope() as session:
                withdrawal_receipts = session.query(Receipt).filter(
                    Receipt.receipt_type == "صرف"
                ).all()
                
                withdraw_transactions = session.query(حركة_نقدية).filter(
                    حركة_نقدية.transaction_type == "withdraw"
                ).all()
                
                # التحقق من كل سند صرف
                matched_receipts = []
                unmatched_receipts = []
                
                for receipt in withdrawal_receipts:
                    transaction = session.query(حركة_نقدية).filter(
                        حركة_نقدية.reference == f"سند صرف رقم {receipt.id}"
                    ).first()
                    
                    if transaction:
                        matched_receipts.append({
                            "receipt_id": receipt.id,
                            "transaction_id": transaction.id,
                            "amount": receipt.amount_usd,
                            "date": receipt.issue_date.strftime('%Y-%m-%d')
                        })
                    else:
                        unmatched_receipts.append({
                            "receipt_id": receipt.id,
                            "amount": receipt.amount_usd,
                            "date": receipt.issue_date.strftime('%Y-%m-%d')
                        })
                
                # إعداد التقرير
                report = f"""
                تقرير سندات الصرف:
                
                - عدد سندات الصرف: {len(withdrawal_receipts)}
                - عدد حركات السحب: {len(withdraw_transactions)}
                - عدد السندات المطابقة: {len(matched_receipts)}
                - عدد السندات غير المطابقة: {len(unmatched_receipts)}
                
                """
                
                if unmatched_receipts:
                    report += "سندات الصرف غير المطابقة:\n\n"
                    for receipt in unmatched_receipts:
                        report += f"- سند رقم {receipt['receipt_id']}: {receipt['amount']:.2f} $ ({receipt['date']})\n"
                
                self.results_text.setText(report)
                
        except Exception as e:
            self.results_text.setText(f"حدث خطأ أثناء اختبار سندات الصرف:\n{str(e)}")
    
    def test_deposits(self):
        """اختبار سندات القبض وعلاقتها بحركات النقدية"""
        try:
            with session_scope() as session:
                deposit_receipts = session.query(Receipt).filter(
                    Receipt.receipt_type == "قبض"
                ).all()
                
                deposit_transactions = session.query(حركة_نقدية).filter(
                    حركة_نقدية.transaction_type == "deposit"
                ).all()
                
                # التحقق من كل سند قبض
                matched_receipts = []
                unmatched_receipts = []
                
                for receipt in deposit_receipts:
                    transaction = session.query(حركة_نقدية).filter(
                        حركة_نقدية.reference == f"سند قبض رقم {receipt.id}"
                    ).first()
                    
                    if transaction:
                        matched_receipts.append({
                            "receipt_id": receipt.id,
                            "transaction_id": transaction.id,
                            "amount": receipt.amount_usd,
                            "date": receipt.issue_date.strftime('%Y-%m-%d')
                        })
                    else:
                        unmatched_receipts.append({
                            "receipt_id": receipt.id,
                            "amount": receipt.amount_usd,
                            "date": receipt.issue_date.strftime('%Y-%m-%d')
                        })
                
                # إعداد التقرير
                report = f"""
                تقرير سندات القبض:
                
                - عدد سندات القبض: {len(deposit_receipts)}
                - عدد حركات الإيداع: {len(deposit_transactions)}
                - عدد السندات المطابقة: {len(matched_receipts)}
                - عدد السندات غير المطابقة: {len(unmatched_receipts)}
                
                """
                
                if unmatched_receipts:
                    report += "سندات القبض غير المطابقة:\n\n"
                    for receipt in unmatched_receipts:
                        report += f"- سند رقم {receipt['receipt_id']}: {receipt['amount']:.2f} $ ({receipt['date']})\n"
                
                self.results_text.setText(report)
                
        except Exception as e:
            self.results_text.setText(f"حدث خطأ أثناء اختبار سندات القبض:\n{str(e)}")
    
    def fix_all_issues(self):
        """إصلاح جميع المشكلات المتعلقة بصندوق النقدية"""
        try:
            fixed_issues = []
            
            with session_scope() as session:
                # 1. التأكد من وجود صندوق النقدية
                cash_box = session.query(صندوق_النقدية).first()
                if not cash_box:
                    cash_box = صندوق_النقدية(balance=0.0, last_updated=datetime.now())
                    session.add(cash_box)
                    session.commit()
                    fixed_issues.append("تم إنشاء صندوق النقدية")
                
                # 2. إصلاح الحركات غير المرتبطة بصندوق
                orphaned_transactions = session.query(حركة_نقدية).filter(
                    حركة_نقدية.cash_box_id != cash_box.id
                ).all()
                
                if orphaned_transactions:
                    for transaction in orphaned_transactions:
                        transaction.cash_box_id = cash_box.id
                    session.commit()
                    fixed_issues.append(f"تم إصلاح {len(orphaned_transactions)} حركة غير مرتبطة بصندوق")
                
                # 3. إصلاح سندات الصرف غير المطابقة
                withdrawal_receipts = session.query(Receipt).filter(
                    Receipt.receipt_type == "صرف"
                ).all()
                
                fixed_withdrawal_count = 0
                for receipt in withdrawal_receipts:
                    transaction = session.query(حركة_نقدية).filter(
                        حركة_نقدية.reference == f"سند صرف رقم {receipt.id}"
                    ).first()
                    
                    if not transaction:
                        # إنشاء حركة نقدية للسند
                        new_balance = cash_box.balance - receipt.amount_usd
                        new_transaction = حركة_نقدية(
                            cash_box_id=cash_box.id,
                            transaction_type="withdraw",
                            amount=receipt.amount_usd,
                            balance_after=new_balance,
                            transaction_date=receipt.issue_date,
                            reference=f"سند صرف رقم {receipt.id}",
                            description=f"سند صرف للمورد - (تم إصلاحه)",
                            created_by=1  # Admin user
                        )
                        session.add(new_transaction)
                        fixed_withdrawal_count += 1
                
                if fixed_withdrawal_count > 0:
                    session.commit()
                    fixed_issues.append(f"تم إصلاح {fixed_withdrawal_count} سند صرف غير مطابق")
                
                # 4. إصلاح سندات القبض غير المطابقة
                deposit_receipts = session.query(Receipt).filter(
                    Receipt.receipt_type == "قبض"
                ).all()
                
                fixed_deposit_count = 0
                for receipt in deposit_receipts:
                    transaction = session.query(حركة_نقدية).filter(
                        حركة_نقدية.reference == f"سند قبض رقم {receipt.id}"
                    ).first()
                    
                    if not transaction:
                        # إنشاء حركة نقدية للسند
                        new_balance = cash_box.balance + receipt.amount_usd
                        new_transaction = حركة_نقدية(
                            cash_box_id=cash_box.id,
                            transaction_type="deposit",
                            amount=receipt.amount_usd,
                            balance_after=new_balance,
                            transaction_date=receipt.issue_date,
                            reference=f"سند قبض رقم {receipt.id}",
                            description=f"سند قبض من العميل - (تم إصلاحه)",
                            created_by=1  # Admin user
                        )
                        session.add(new_transaction)
                        fixed_deposit_count += 1
                
                if fixed_deposit_count > 0:
                    session.commit()
                    fixed_issues.append(f"تم إصلاح {fixed_deposit_count} سند قبض غير مطابق")
                
                # 5. إعادة حساب رصيد الصندوق
                total_deposits = session.query(func.sum(حركة_نقدية.amount)).filter(
                    حركة_نقدية.transaction_type == "deposit"
                ).scalar() or 0
                
                total_withdrawals = session.query(func.sum(حركة_نقدية.amount)).filter(
                    حركة_نقدية.transaction_type == "withdraw"
                ).scalar() or 0
                
                calculated_balance = total_deposits - total_withdrawals
                
                if abs(calculated_balance - cash_box.balance) > 0.001:
                    old_balance = cash_box.balance
                    cash_box.balance = calculated_balance
                    cash_box.last_updated = datetime.now()
                    session.commit()
                    fixed_issues.append(f"تم تصحيح رصيد الصندوق من {old_balance:.2f} $ إلى {calculated_balance:.2f} $")
            
            # عرض النتائج
            if fixed_issues:
                result = "تم إصلاح المشكلات التالية:\n\n" + "\n".join([f"- {issue}" for issue in fixed_issues])
            else:
                result = "لم يتم العثور على أي مشكلات للإصلاح"
            
            self.results_text.setText(result)
            self.update_balance()
            
            # اختبار الصندوق بعد الإصلاح
            QMessageBox.information(self, "تم الإصلاح", "تم إصلاح المشكلات بنجاح. سيتم الآن اختبار صندوق النقدية للتأكد من الإصلاح.")
            self.test_cashbox()
            
        except Exception as e:
            self.results_text.setText(f"حدث خطأ أثناء محاولة إصلاح المشكلات:\n{str(e)}")


if __name__ == "__main__":
    app = QApplication.instance() or QApplication(sys.argv)
    window = CashBoxTester()
    window.show()
    sys.exit(app.exec())
