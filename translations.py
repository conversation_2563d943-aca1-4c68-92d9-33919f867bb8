"""
Módulo de traducciones para la aplicación de ventas de diamantes.
"""

# Diccionario de traducciones
translations = {
    "ar": {  # Árabe
        "settings_title": "إعدادات النظام",
        "company_info": "معلومات الشركة",
        "system_settings": "إعدادات النظام",
        "backup": "النسخ الاحتياطي",
        "language_changed": "تم تغيير اللغة",
        "settings_changed": "تم تغيير الإعدادات",
        "restart_required": "يرجى إعادة تشغيل البرنامج لتطبيق التغييرات",
        "success": "تم بنجاح",
        "error": "خطأ",
        "operation_success": "تمت العملية بنجاح",
        "operation_error": "حدث خطأ أثناء تنفيذ العملية",
        "cash_box": "صندوق النقدية"
    },
    "en": {  # Inglés
        "settings_title": "System Settings",
        "company_info": "Company Information",
        "system_settings": "System Settings",
        "backup": "Backup",
        "language_changed": "Language Changed",
        "settings_changed": "Settings Changed",
        "restart_required": "Please restart the application to apply changes",
        "success": "Success",
        "error": "Error",
        "operation_success": "Operation completed successfully",
        "operation_error": "An error occurred during the operation",
        "cash_box": "Cash Box"
    }
}

# Idioma actual
current_language = "ar"  # Árabe por defecto

def load_translations(language_code):
    """
    Carga las traducciones para el idioma especificado.
    
    Args:
        language_code (str): Código del idioma (ar, en)
        
    Returns:
        dict: Diccionario de traducciones para el idioma especificado
    """
    global current_language
    current_language = language_code
    return translations.get(language_code, translations["ar"])

def change_language(language_code):
    """
    Cambia el idioma actual.
    
    Args:
        language_code (str): Código del idioma (ar, en)
    """
    global current_language
    current_language = language_code

def get_translation(key):
    """
    Obtiene la traducción para una clave específica.
    
    Args:
        key (str): Clave de traducción
        
    Returns:
        str: Texto traducido
    """
    lang_dict = translations.get(current_language, translations["ar"])
    return lang_dict.get(key, key)
