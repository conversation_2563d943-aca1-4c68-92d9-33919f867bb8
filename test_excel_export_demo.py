#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف اختبار لإظهار كيفية تصدير التقارير إلى Excel
مع ملخص الإجماليات المحسن
"""

import xlsxwriter
from datetime import datetime

def create_sample_customer_excel():
    """إنشاء ملف Excel نموذجي لتقرير العميل"""
    
    # إنشاء ملف Excel
    workbook = xlsxwriter.Workbook('تقرير_العميل_نموذج.xlsx')
    worksheet = workbook.add_worksheet("تقرير العميل")
    
    # تنسيقات مختلفة
    title_format = workbook.add_format({
        'bold': True,
        'font_size': 16,
        'align': 'center',
        'valign': 'vcenter',
        'font_color': '#1F497D',
        'bg_color': '#E6EFF9',
        'border': 1,
        'text_wrap': True
    })
    
    header_format = workbook.add_format({
        'bold': True,
        'font_size': 12,
        'align': 'center',
        'valign': 'vcenter',
        'font_color': 'white',
        'bg_color': '#3498db',
        'border': 1,
        'text_wrap': True
    })
    
    cell_format = workbook.add_format({
        'align': 'center',
        'valign': 'vcenter',
        'border': 1,
        'text_wrap': True
    })
    
    total_format = workbook.add_format({
        'bold': True,
        'align': 'center',
        'valign': 'vcenter',
        'font_color': 'white',
        'bg_color': '#28a745',
        'border': 2,
        'num_format': '#,##0.00'
    })
    
    # عنوان التقرير
    worksheet.merge_range('A1:J1', 'شركة الماس للمجوهرات', title_format)
    worksheet.merge_range('A2:J2', 'تقرير حساب العميل: أحمد محمد', title_format)
    worksheet.merge_range('A3:J3', 'للفترة من 2025-01-01 إلى 2025-06-29', title_format)
    
    # رؤوس الأعمدة
    headers = [
        'الوزن', 'نوع الألماس', 'سعر القيراط ($)', 'السعر بالريال للكمية',
        'المبلغ المسدد ($)', 'المبلغ المسدد (ريال)', 'الرصيد ($)', 
        'الرصيد التراكمي (ريال)', 'التاريخ', 'الملاحظات'
    ]
    
    for col, header in enumerate(headers):
        worksheet.write(4, col, header, header_format)
    
    # بيانات نموذجية
    data = [
        [1.921, 'فاتورة مبيعات - 22335', 220.000, 1584.825, 0.000, 0.000, 422.52, 1584.825, '2025-06-29', 'بيع ألماس'],
        [32.129, 'فاتورة مبيعات - 22334', 195.000, 21258.262, 0.000, 0.000, 6268.87, 23509.087, '2025-06-28', 'بيع ألماس'],
        ['-', 'سند قبض - 1001', '-', '-', 1000.000, 3750.000, -1000.00, 19759.087, '2025-06-27', 'دفعة جزئية']
    ]
    
    # كتابة البيانات
    for row, row_data in enumerate(data, start=5):
        for col, value in enumerate(row_data):
            if isinstance(value, (int, float)) and value != '-':
                worksheet.write(row, col, value, cell_format)
            else:
                worksheet.write(row, col, str(value), cell_format)
    
    # صف الإجماليات
    row = 8
    totals = ['الإجمالي', '', 34.05, 207.500, 22843.087, 3750.000, 5691.39, 21343.087, '', '']
    for col, total in enumerate(totals):
        if isinstance(total, (int, float)) and total != '':
            worksheet.write(row, col, total, total_format)
        else:
            worksheet.write(row, col, str(total), total_format)
    
    # ملخص الإجماليات المحسن
    row += 3
    
    summary_header_format = workbook.add_format({
        'bold': True,
        'font_size': 14,
        'align': 'center',
        'valign': 'vcenter',
        'font_color': 'white',
        'bg_color': '#2E86AB',
        'border': 2,
        'text_wrap': True
    })
    
    summary_label_format = workbook.add_format({
        'bold': True,
        'font_size': 12,
        'align': 'right',
        'valign': 'vcenter',
        'font_color': '#2E86AB',
        'bg_color': '#F0F8FF',
        'border': 1,
        'text_wrap': True
    })
    
    summary_value_format = workbook.add_format({
        'bold': True,
        'font_size': 12,
        'align': 'center',
        'valign': 'vcenter',
        'font_color': '#2E86AB',
        'bg_color': '#F0F8FF',
        'border': 1,
        'num_format': '#,##0.00'
    })
    
    # عنوان ملخص الإجماليات
    worksheet.merge_range(f'A{row}:J{row}', "ملخص الإجماليات", summary_header_format)
    row += 1
    
    # الصف الأول: الوزن والمستحق
    worksheet.merge_range(f'A{row}:B{row}', "إجمالي الوزن: 34.05 قيراط", summary_label_format)
    worksheet.merge_range(f'C{row}:E{row}', "إجمالي المستحق: $5691.49 | 21343.09 ريال", summary_value_format)
    row += 1
    
    # الصف الثاني: المسدد والرصيد
    worksheet.merge_range(f'A{row}:B{row}', "إجمالي المسدد: $1000.00 | 3750.00 ريال", summary_label_format)
    worksheet.merge_range(f'C{row}:E{row}', "إجمالي الرصيد: $4691.49 | 17593.09 ريال", summary_value_format)
    row += 2
    
    # تذييل التقرير
    worksheet.merge_range(f'A{row+1}:J{row+1}', f"تم إنشاء هذا التقرير بواسطة: admin", title_format)
    worksheet.merge_range(f'A{row+2}:J{row+2}', f"تاريخ الطباعة: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}", title_format)
    
    # تعديل عرض الأعمدة
    worksheet.set_column('A:J', 15)
    worksheet.set_row(0, 25)
    worksheet.set_row(1, 25)
    worksheet.set_row(2, 25)
    
    workbook.close()
    print("تم إنشاء ملف تقرير العميل النموذجي: تقرير_العميل_نموذج.xlsx")

def create_sample_supplier_excel():
    """إنشاء ملف Excel نموذجي لتقرير المورد"""
    
    # إنشاء ملف Excel
    workbook = xlsxwriter.Workbook('تقرير_المورد_نموذج.xlsx')
    worksheet = workbook.add_worksheet("تقرير المورد")
    
    # تنسيقات مختلفة (نفس التنسيقات مع ألوان مختلفة للمورد)
    title_format = workbook.add_format({
        'bold': True,
        'font_size': 16,
        'align': 'center',
        'valign': 'vcenter',
        'font_color': '#1F497D',
        'bg_color': '#E6EFF9',
        'border': 1,
        'text_wrap': True
    })
    
    summary_header_format = workbook.add_format({
        'bold': True,
        'font_size': 14,
        'align': 'center',
        'valign': 'vcenter',
        'font_color': 'white',
        'bg_color': '#E74C3C',
        'border': 2,
        'text_wrap': True
    })
    
    summary_label_format = workbook.add_format({
        'bold': True,
        'font_size': 12,
        'align': 'right',
        'valign': 'vcenter',
        'font_color': '#E74C3C',
        'bg_color': '#FFF5F5',
        'border': 1,
        'text_wrap': True
    })
    
    summary_value_format = workbook.add_format({
        'bold': True,
        'font_size': 12,
        'align': 'center',
        'valign': 'vcenter',
        'font_color': '#E74C3C',
        'bg_color': '#FFF5F5',
        'border': 1,
        'num_format': '#,##0.00'
    })
    
    # عنوان التقرير
    worksheet.merge_range('A1:J1', 'شركة الماس للمجوهرات', title_format)
    worksheet.merge_range('A2:J2', 'تقرير حساب المورد: مورد الماس الذهبي', title_format)
    worksheet.merge_range('A3:J3', 'للفترة من 2025-01-01 إلى 2025-06-29', title_format)
    
    # ملخص الإجماليات المحسن
    row = 5
    
    # عنوان ملخص الإجماليات
    worksheet.merge_range(f'A{row}:J{row}', "ملخص الإجماليات", summary_header_format)
    row += 1
    
    # الصف الأول: الوزن والمستحق
    worksheet.merge_range(f'A{row}:B{row}', "إجمالي الوزن: 34.05 قيراط", summary_label_format)
    worksheet.merge_range(f'C{row}:E{row}', "إجمالي المستحق: $5691.49 | 21343.09 ريال", summary_value_format)
    row += 1
    
    # الصف الثاني: المسدد والرصيد
    worksheet.merge_range(f'A{row}:B{row}', "إجمالي المسدد: $1000.00 | 3750.00 ريال", summary_label_format)
    worksheet.merge_range(f'C{row}:E{row}', "إجمالي الرصيد: $4691.49 | 17593.09 ريال", summary_value_format)
    row += 2
    
    # تذييل التقرير
    worksheet.merge_range(f'A{row+1}:J{row+1}', f"تم إنشاء هذا التقرير بواسطة: admin", title_format)
    worksheet.merge_range(f'A{row+2}:J{row+2}', f"تاريخ الطباعة: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}", title_format)
    
    # تعديل عرض الأعمدة
    worksheet.set_column('A:J', 15)
    worksheet.set_row(0, 25)
    worksheet.set_row(1, 25)
    worksheet.set_row(2, 25)
    
    workbook.close()
    print("تم إنشاء ملف تقرير المورد النموذجي: تقرير_المورد_نموذج.xlsx")

if __name__ == "__main__":
    print("إنشاء ملفات Excel النموذجية...")
    create_sample_customer_excel()
    create_sample_supplier_excel()
    print("تم الانتهاء من إنشاء الملفات النموذجية!")
