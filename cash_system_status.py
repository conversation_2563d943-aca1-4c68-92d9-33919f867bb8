"""
عرض تفاصيل شاملة عن سندات الصرف وحركات النقدية
هذا البرنامج يعرض تقريرًا مفصلاً عن حالة سندات الصرف في النظام
"""

from database import Receipt, حركة_نقدية, صندوق_النقدية, Purchase, Supplier
from db_session import session_scope
from sqlalchemy import or_, func, desc
from datetime import datetime

def display_cash_system_status():
    """
    عرض حالة شاملة لنظام صندوق النقدية وسندات الصرف
    """
    print("=" * 60)
    print("تقرير شامل عن حالة نظام صندوق النقدية")
    print("=" * 60)
    
    try:
        with session_scope() as session:
            # 1. حالة صندوق النقدية
            print("\n1. حالة صندوق النقدية:")
            print("-" * 30)
            
            cash_box = session.query(صندوق_النقدية).first()
            if cash_box:
                print(f"   رقم الصندوق: {cash_box.id}")
                print(f"   الرصيد الحالي: {cash_box.balance:.2f} دولار")
                print(f"   آخر تحديث: {cash_box.last_updated}")
            else:
                print("   لا يوجد صندوق نقدية في النظام!")
            
            # 2. إحصائيات حركات النقدية
            print("\n2. إحصائيات حركات النقدية:")
            print("-" * 35)
            
            total_transactions = session.query(حركة_نقدية).count()
            deposit_count = session.query(حركة_نقدية).filter_by(transaction_type="deposit").count()
            withdraw_count = session.query(حركة_نقدية).filter_by(transaction_type="withdraw").count()
            
            print(f"   إجمالي الحركات: {total_transactions}")
            print(f"   حركات الإيداع: {deposit_count}")
            print(f"   حركات السحب: {withdraw_count}")
            
            # 3. إحصائيات سندات الصرف
            print("\n3. إحصائيات سندات الصرف:")
            print("-" * 35)
            
            total_receipts = session.query(Receipt).count()
            cash_out_ar = session.query(Receipt).filter_by(receipt_type="صرف").count()
            cash_out_en = session.query(Receipt).filter_by(receipt_type="CashOut").count()
            cash_in = session.query(Receipt).filter_by(receipt_type="CashIn").count()
            
            print(f"   إجمالي السندات: {total_receipts}")
            print(f"   سندات الصرف (عربي): {cash_out_ar}")
            print(f"   سندات الصرف (إنجليزي): {cash_out_en}")
            print(f"   سندات القبض: {cash_in}")
            
            # 4. تفاصيل سندات الصرف
            print("\n4. تفاصيل سندات الصرف:")
            print("-" * 30)
            
            withdrawal_receipts = session.query(Receipt).filter(
                or_(Receipt.receipt_type == "صرف", Receipt.receipt_type == "CashOut")
            ).order_by(desc(Receipt.issue_date)).all()
            
            if withdrawal_receipts:
                print(f"   تم العثور على {len(withdrawal_receipts)} سند صرف:")
                
                for i, receipt in enumerate(withdrawal_receipts, 1):
                    print(f"\n   {i}. سند صرف رقم {receipt.id}:")
                    print(f"      النوع: {receipt.receipt_type}")
                    print(f"      المبلغ: {receipt.amount_usd:.2f} دولار")
                    print(f"      التاريخ: {receipt.issue_date}")
                    
                    # البحث عن المورد المرتبط
                    if receipt.supplier_id:
                        supplier = session.query(Supplier).filter_by(id=receipt.supplier_id).first()
                        if supplier:
                            print(f"      المورد: {supplier.name}")
                    
                    # البحث عن الحركة النقدية المرتبطة
                    related_transaction = session.query(حركة_نقدية).filter(
                        or_(
                            حركة_نقدية.reference == f"سند صرف #{receipt.id}",
                            حركة_نقدية.reference == f"سند صرف رقم {receipt.id}"
                        )
                    ).first()
                    
                    if related_transaction:
                        print(f"      الحركة النقدية: رقم {related_transaction.id}")
                        print(f"      الحالة: ✓ مرتبط بحركة نقدية")
                    else:
                        print(f"      الحالة: ✗ غير مرتبط بحركة نقدية")
            else:
                print("   لا توجد سندات صرف في النظام")
            
            # 5. آخر الحركات النقدية
            print("\n5. آخر 5 حركات نقدية:")
            print("-" * 30)
            
            recent_transactions = session.query(حركة_نقدية).order_by(
                desc(حركة_نقدية.transaction_date)
            ).limit(5).all()
            
            if recent_transactions:
                for i, transaction in enumerate(recent_transactions, 1):
                    transaction_type_ar = "إيداع" if transaction.transaction_type == "deposit" else "سحب"
                    print(f"\n   {i}. حركة رقم {transaction.id}:")
                    print(f"      النوع: {transaction_type_ar}")
                    print(f"      المبلغ: {transaction.amount:.2f} دولار")
                    print(f"      التاريخ: {transaction.transaction_date}")
                    print(f"      المرجع: {transaction.reference or 'لا يوجد'}")
                    print(f"      الوصف: {transaction.description or 'لا يوجد'}")
                    print(f"      الرصيد بعد العملية: {transaction.balance_after:.2f} دولار")
            else:
                print("   لا توجد حركات نقدية في النظام")
            
            # 6. تحقق من التطابق
            print("\n6. تحقق من تطابق البيانات:")
            print("-" * 35)
            
            # حساب الرصيد المتوقع من مجموع الحركات
            deposit_total = session.query(func.sum(حركة_نقدية.amount)).filter_by(transaction_type="deposit").scalar() or 0
            withdraw_total = session.query(func.sum(حركة_نقدية.amount)).filter_by(transaction_type="withdraw").scalar() or 0
            calculated_balance = deposit_total - withdraw_total
            
            print(f"   إجمالي الإيداعات: {deposit_total:.2f} دولار")
            print(f"   إجمالي المسحوبات: {withdraw_total:.2f} دولار")
            print(f"   الرصيد المحسوب: {calculated_balance:.2f} دولار")
            
            if cash_box:
                print(f"   الرصيد المسجل: {cash_box.balance:.2f} دولار")
                if abs(calculated_balance - cash_box.balance) < 0.01:
                    print("   الحالة: ✓ البيانات متطابقة")
                else:
                    print("   الحالة: ✗ عدم تطابق في البيانات!")
                    print(f"   الفرق: {abs(calculated_balance - cash_box.balance):.2f} دولار")
            
            print("\n" + "=" * 60)
            print("انتهى التقرير")
            print("=" * 60)
            
    except Exception as e:
        print(f"خطأ أثناء عرض التقرير: {str(e)}")

if __name__ == "__main__":
    display_cash_system_status()
