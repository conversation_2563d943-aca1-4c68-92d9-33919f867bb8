.. This data file has been placed in the public domain.
.. Derived from the Unicode character mappings available from
   <http://www.w3.org/2003/entities/xml/>.
   Processed by unicode2rstsubs.py, part of Docutils:
   <https://docutils.sourceforge.io>.

.. |ang|      unicode:: U+02220 .. ANGLE
.. |ange|     unicode:: U+029A4 .. ANGLE WITH UNDERBAR
.. |angmsd|   unicode:: U+02221 .. MEASURED ANGLE
.. |angmsdaa| unicode:: U+029A8 .. MEASURED ANGLE WITH OPEN ARM ENDING IN ARROW POINTING UP AND RIGHT
.. |angmsdab| unicode:: U+029A9 .. MEASURED ANGLE WITH OPEN ARM ENDING IN ARROW POINTING UP AND LEFT
.. |angmsdac| unicode:: U+029AA .. MEASURED ANGLE WITH OPEN ARM ENDING IN ARROW POINTING DOWN AND RIGHT
.. |angmsdad| unicode:: U+029AB .. MEASURED ANGLE WITH OPEN ARM ENDING IN ARROW POINTING DOWN AND LEFT
.. |angmsdae| unicode:: U+029AC .. MEASURED ANGLE WITH OPEN ARM ENDING IN ARROW POINTING RIGHT AND UP
.. |angmsdaf| unicode:: U+029AD .. MEASURED ANGLE WITH OPEN ARM ENDING IN ARROW POINTING LEFT AND UP
.. |angmsdag| unicode:: U+029AE .. MEASURED ANGLE WITH OPEN ARM ENDING IN ARROW POINTING RIGHT AND DOWN
.. |angmsdah| unicode:: U+029AF .. MEASURED ANGLE WITH OPEN ARM ENDING IN ARROW POINTING LEFT AND DOWN
.. |angrtvb|  unicode:: U+022BE .. RIGHT ANGLE WITH ARC
.. |angrtvbd| unicode:: U+0299D .. MEASURED RIGHT ANGLE WITH DOT
.. |bbrk|     unicode:: U+023B5 .. BOTTOM SQUARE BRACKET
.. |bbrktbrk| unicode:: U+023B6 .. BOTTOM SQUARE BRACKET OVER TOP SQUARE BRACKET
.. |bemptyv|  unicode:: U+029B0 .. REVERSED EMPTY SET
.. |beth|     unicode:: U+02136 .. BET SYMBOL
.. |boxbox|   unicode:: U+029C9 .. TWO JOINED SQUARES
.. |bprime|   unicode:: U+02035 .. REVERSED PRIME
.. |bsemi|    unicode:: U+0204F .. REVERSED SEMICOLON
.. |cemptyv|  unicode:: U+029B2 .. EMPTY SET WITH SMALL CIRCLE ABOVE
.. |cirE|     unicode:: U+029C3 .. CIRCLE WITH TWO HORIZONTAL STROKES TO THE RIGHT
.. |cirscir|  unicode:: U+029C2 .. CIRCLE WITH SMALL CIRCLE TO THE RIGHT
.. |comp|     unicode:: U+02201 .. COMPLEMENT
.. |daleth|   unicode:: U+02138 .. DALET SYMBOL
.. |demptyv|  unicode:: U+029B1 .. EMPTY SET WITH OVERBAR
.. |ell|      unicode:: U+02113 .. SCRIPT SMALL L
.. |empty|    unicode:: U+02205 .. EMPTY SET
.. |emptyv|   unicode:: U+02205 .. EMPTY SET
.. |gimel|    unicode:: U+02137 .. GIMEL SYMBOL
.. |iiota|    unicode:: U+02129 .. TURNED GREEK SMALL LETTER IOTA
.. |image|    unicode:: U+02111 .. BLACK-LETTER CAPITAL I
.. |imath|    unicode:: U+00131 .. LATIN SMALL LETTER DOTLESS I
.. |inodot|   unicode:: U+00131 .. LATIN SMALL LETTER DOTLESS I
.. |jmath|    unicode:: U+0006A .. LATIN SMALL LETTER J
.. |jnodot|   unicode:: U+0006A .. LATIN SMALL LETTER J
.. |laemptyv| unicode:: U+029B4 .. EMPTY SET WITH LEFT ARROW ABOVE
.. |lltri|    unicode:: U+025FA .. LOWER LEFT TRIANGLE
.. |lrtri|    unicode:: U+022BF .. RIGHT TRIANGLE
.. |mho|      unicode:: U+02127 .. INVERTED OHM SIGN
.. |nang|     unicode:: U+02220 U+020D2 .. ANGLE with vertical line
.. |nexist|   unicode:: U+02204 .. THERE DOES NOT EXIST
.. |oS|       unicode:: U+024C8 .. CIRCLED LATIN CAPITAL LETTER S
.. |planck|   unicode:: U+0210F .. PLANCK CONSTANT OVER TWO PI
.. |plankv|   unicode:: U+0210F .. PLANCK CONSTANT OVER TWO PI
.. |raemptyv| unicode:: U+029B3 .. EMPTY SET WITH RIGHT ARROW ABOVE
.. |range|    unicode:: U+029A5 .. REVERSED ANGLE WITH UNDERBAR
.. |real|     unicode:: U+0211C .. BLACK-LETTER CAPITAL R
.. |sbsol|    unicode:: U+0FE68 .. SMALL REVERSE SOLIDUS
.. |tbrk|     unicode:: U+023B4 .. TOP SQUARE BRACKET
.. |trpezium| unicode:: U+0FFFD .. REPLACEMENT CHARACTER
.. |ultri|    unicode:: U+025F8 .. UPPER LEFT TRIANGLE
.. |urtri|    unicode:: U+025F9 .. UPPER RIGHT TRIANGLE
.. |vprime|   unicode:: U+02032 .. PRIME
.. |vzigzag|  unicode:: U+0299A .. VERTICAL ZIGZAG LINE
.. |weierp|   unicode:: U+02118 .. SCRIPT CAPITAL P
