===============================================
تعليمات إنشاء مثبت Diamond Sales v1.30
===============================================

📁 المجلد المطلوب:
DiamondSalesV1.20/installer_package/

📋 الملفات الموجودة:
✅ DiamondSales_Installer.iss (سكريبت المثبت)
✅ DiamondSales/ (مجلد البرنامج الكامل)
✅ assets/ (الأيقونات)
✅ تعليمات_المثبت.txt

🔧 خطوات إنشاء المثبت:

1️⃣ تحميل وتثبيت Inno Setup:
   - اذهب إلى: https://jrsoftware.org/isinfo.php
   - حمل النسخة الأحدث
   - ثبت البرنامج على جهازك

2️⃣ إنشاء المثبت:
   - افتح مجلد: DiamondSalesV1.20/installer_package/
   - انقر نقراً مزدوجاً على: DiamondSales_Installer.iss
   - سيفتح Inno Setup Compiler
   - اضغط F9 أو Build > Compile
   - انتظر حتى انتهاء العملية

3️⃣ النتيجة:
   - ستجد ملف: DiamondSalesSetup_v1.30.exe
   - في نفس المجلد (installer_package)
   - حجم الملف: حوالي 50-100 MB

⚠️ ملاحظات مهمة:
- تأكد من وجود جميع الملفات في مجلد installer_package
- لا تغير أسماء المجلدات أو الملفات
- تأكد من أن Inno Setup يعمل من المجلد الصحيح

🎯 بعد إنشاء المثبت:
- اختبر المثبت على جهاز آخر
- تأكد من عمل البرنامج بشكل صحيح
- وزع المثبت على المستخدمين

🔑 بيانات الدخول للبرنامج:
- اسم المستخدم: admin
- كلمة المرور: 1

===============================================
للدعم الفني: <EMAIL>
===============================================
