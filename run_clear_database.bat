@echo off
chcp 65001
echo Starting database clearing process...

REM Try different Python commands
if exist ".venv\Scripts\python.exe" (
    echo Using virtual environment Python...
    ".venv\Scripts\python.exe" auto_clear_database.py
    goto :end
)

if exist "C:\Python313\python.exe" (
    echo Using system Python 3.13...
    "C:\Python313\python.exe" auto_clear_database.py
    goto :end
)

if exist "C:\Python312\python.exe" (
    echo Using system Python 3.12...
    "C:\Python312\python.exe" auto_clear_database.py
    goto :end
)

if exist "C:\Python311\python.exe" (
    echo Using system Python 3.11...
    "C:\Python311\python.exe" auto_clear_database.py
    goto :end
)

echo Trying py launcher...
py auto_clear_database.py
if %errorlevel% neq 0 (
    echo Trying python command...
    python auto_clear_database.py
)

:end
echo.
echo Process completed. Press any key to exit...
pause > nul