import sqlite3
import os
from datetime import datetime
import shutil

def main():
    print("Database clearing script started...")
    
    # Check if database exists
    if not os.path.exists('diamond_sales.db'):
        print("ERROR: diamond_sales.db not found")
        return
    
    # Create backup
    try:
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_name = f'diamond_sales_backup_before_clear_{timestamp}.db'
        shutil.copy2('diamond_sales.db', backup_name)
        print(f"Backup created: {backup_name}")
    except Exception as e:
        print(f"Backup failed: {e}")
        return
    
    # Clear database
    try:
        conn = sqlite3.connect('diamond_sales.db')
        cursor = conn.cursor()
        
        print("Starting database clearing...")
        
        cursor.execute('PRAGMA foreign_keys = OFF')
        
        tables_to_clear = [
            'customers', 'suppliers', 'sales', 'purchases',
            'sale_items', 'purchase_items', 'receipts',
            'payments', 'vouchers', 'cash_transactions',
            'journal_entries', 'data_locks'
        ]
        
        cleared_count = 0
        for table in tables_to_clear:
            try:
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name=?", (table,))
                if cursor.fetchone():
                    cursor.execute(f'DELETE FROM {table}')
                    rows_deleted = cursor.rowcount
                    print(f"Cleared table {table}: {rows_deleted} rows")
                    cleared_count += 1
                else:
                    print(f"Table {table} not found")
            except Exception as e:
                print(f"Error clearing table {table}: {e}")
        
        # Reset auto-increment IDs
        try:
            cursor.execute('DELETE FROM sqlite_sequence WHERE name IN ({})'.format(
                ','.join(['?' for _ in tables_to_clear])
            ), tables_to_clear)
            print("Auto-increment IDs reset")
        except Exception as e:
            print(f"Warning resetting sequences: {e}")
        
        cursor.execute('PRAGMA foreign_keys = ON')
        conn.commit()
        conn.close()
        
        print(f"SUCCESS: Database cleared! {cleared_count} tables processed")
        print(f"Backup saved as: {backup_name}")
        
    except Exception as e:
        print(f"ERROR: Database clearing failed: {e}")
        if 'conn' in locals():
            conn.rollback()
            conn.close()

if __name__ == '__main__':
    main()