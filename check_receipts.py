"""
التحقق من سندات الصرف وعرض عددها
"""

from database import Receipt, حركة_نقدية, صندوق_النقدية
from db_session import session_scope
from sqlalchemy import or_

def check_receipts():
    """
    التحقق من سندات الصرف وحركات النقدية المرتبطة بها
    """
    with session_scope() as session:
        # عدد سندات الصرف
        cash_out_count = session.query(Receipt).filter(
            or_(Receipt.receipt_type == "صرف", Receipt.receipt_type == "CashOut")
        ).count()
        print(f"عدد سندات الصرف: {cash_out_count}")
        
        # استعلام عن سندات الصرف
        receipts = session.query(Receipt).filter(
            or_(Receipt.receipt_type == "صرف", Receipt.receipt_type == "CashOut")
        ).all()
        
        if receipts:
            print("\nبيانات سندات الصرف:")
            for receipt in receipts:
                print(f"رقم السند: {receipt.id} | النوع: {receipt.receipt_type} | المبلغ: {receipt.amount_usd} | التاريخ: {receipt.issue_date}")
                
                # التحقق من وجود حركة نقدية مرتبطة بهذا السند
                cash_transaction = session.query(حركة_نقدية).filter(
                    or_(
                        حركة_نقدية.reference == f"سند صرف #{receipt.id}",
                        حركة_نقدية.reference == f"سند صرف رقم {receipt.id}"
                    )
                ).first()
                
                if cash_transaction:
                    print(f"  - مرتبط بحركة نقدية: {cash_transaction.id} | المبلغ: {cash_transaction.amount} | النوع: {cash_transaction.transaction_type}")
                else:
                    print(f"  - !!! غير مرتبط بأي حركة نقدية !!!")
        else:
            print("لا توجد سندات صرف في قاعدة البيانات")

        # استعلام عن صندوق النقدية
        cash_box = session.query(صندوق_النقدية).first()
        if cash_box:
            print(f"\nرصيد صندوق النقدية الحالي: {cash_box.balance}")
        else:
            print("\nلا يوجد سجل لصندوق النقدية")

if __name__ == "__main__":
    check_receipts()
