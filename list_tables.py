import sqlite3
import os

# Get the current directory
current_dir = os.path.dirname(os.path.abspath(__file__))
# Build the path to the database
db_path = os.path.join(current_dir, 'diamond_sales.db')

print(f"Looking for database at: {db_path}")

# Connect to the database
if os.path.exists(db_path):
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()

    # Get the list of tables
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
    tables = cursor.fetchall()

    print("Tables in the database:")
    for table in tables:
        print(table[0])

    # Close the connection
    conn.close()
else:
    print(f"Database file not found at {db_path}")
    
    # Try to look in parent directory
    parent_dir = os.path.dirname(current_dir)
    parent_db_path = os.path.join(parent_dir, 'diamond_sales.db')
    
    if os.path.exists(parent_db_path):
        print(f"Found database at parent directory: {parent_db_path}")
        conn = sqlite3.connect(parent_db_path)
        cursor = conn.cursor()

        # Get the list of tables
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()

        print("Tables in the database:")
        for table in tables:
            print(table[0])

        # Close the connection
        conn.close()
    else:
        print(f"Database file also not found at {parent_db_path}")
