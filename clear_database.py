#!/usr/bin/env python
# -*- coding: utf-8 -*-

import sqlite3
import os
from datetime import datetime
import shutil

def backup_current_database():
    try:
        if os.path.exists('diamond_sales.db'):
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_name = f'diamond_sales_backup_before_clear_{timestamp}.db'
            shutil.copy2('diamond_sales.db', backup_name)
            print(f'✅ تم إنشاء نسخة احتياطية: {backup_name}')
            return backup_name
        else:
            print('❌ لا توجد قاعدة بيانات للنسخ الاحتياطي')
            return None
    except Exception as e:
        print(f'❌ خطأ في إنشاء النسخة الاحتياطية: {str(e)}')
        return None

def clear_database():
    try:
        conn = sqlite3.connect('diamond_sales.db')
        cursor = conn.cursor()

        print('🔄 بدء تفريغ قاعدة البيانات...')

        cursor.execute('PRAGMA foreign_keys = OFF')

        tables_to_clear = [
            'customers', 'suppliers', 'sales', 'purchases',
            'sale_items', 'purchase_items', 'receipts',
            'payments', 'vouchers', 'cash_transactions',
            'journal_entries', 'data_locks'
        ]

        cleared_count = 0
        for table in tables_to_clear:
            try:
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name=?", (table,))
                if cursor.fetchone():
                    cursor.execute(f'DELETE FROM {table}')
                    rows_deleted = cursor.rowcount
                    print(f'  ✅ تم تفريغ جدول {table}: {rows_deleted} صف')
                    cleared_count += 1
                else:
                    print(f'  ⚠️ الجدول {table} غير موجود')
            except Exception as e:
                print(f'  ❌ خطأ في تفريغ جدول {table}: {str(e)}')

        print('\\n🔄 إعادة تعيين معرفات التسلسل التلقائي...')
        try:
            cursor.execute('DELETE FROM sqlite_sequence WHERE name IN ({})'.format(
                ','.join(['?' for _ in tables_to_clear])
            ), tables_to_clear)
            print('  ✅ تم إعادة تعيين معرفات التسلسل التلقائي')
        except Exception as e:
            print(f'  ⚠️ تحذير في إعادة تعيين التسلسل: {str(e)}')

        cursor.execute('PRAGMA foreign_keys = ON')
        conn.commit()

        print(f'\\n✅ تم تفريغ قاعدة البيانات بنجاح!')
        print(f'📊 تم تفريغ {cleared_count} جدول')

        return True

    except Exception as e:
        print(f'❌ خطأ في تفريغ قاعدة البيانات: {str(e)}')
        if 'conn' in locals():
            conn.rollback()
        return False
    finally:
        if 'conn' in locals():
            conn.close()

def main():
    print('=' * 60)
    print('🗃️  سكريبت تفريغ قاعدة البيانات')
    print('=' * 60)

    if not os.path.exists('diamond_sales.db'):
        print('❌ لا توجد قاعدة بيانات للتفريغ')
        return

    backup_file = backup_current_database()

    print('\\n⚠️  تحذير: سيتم حذف جميع البيانات التشغيلية من قاعدة البيانات')

    confirm = input('\\n❓ هل تريد المتابعة؟ (y/N): ').strip().lower()

    if confirm in ['y', 'yes']:
        if clear_database():
            print('\\n🎉 تم تفريغ قاعدة البيانات بنجاح!')
            print('📦 قاعدة البيانات جاهزة لإنشاء ملف exe')
            if backup_file:
                print(f'💾 النسخة الاحتياطية محفوظة في: {backup_file}')
        else:
            print('\\n❌ فشل في تفريغ قاعدة البيانات')
    else:
        print('\\n🚫 تم إلغاء العملية')

if __name__ == '__main__':
    main()

