#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Diamond Sales V1.30 - Create Separate Standalone Packages
إنشاء حزم منفصلة لكل برنامج من برامج مبيعات الألماس
"""

import os
import shutil
import zipfile
from pathlib import Path

def print_status(message, status="INFO"):
    """طباعة رسالة حالة بتنسيق منظم"""
    symbols = {
        "INFO": "ℹ️",
        "SUCCESS": "✅", 
        "ERROR": "❌",
        "WARNING": "⚠️"
    }
    print(f"{symbols.get(status, 'ℹ️')} {message}")

def create_directory_if_not_exists(path):
    """إنشاء مجلد إذا لم يكن موجوداً"""
    Path(path).mkdir(parents=True, exist_ok=True)
    print_status(f"تم إنشاء المجلد: {path}", "SUCCESS")

def copy_file_or_directory(src, dst):
    """نسخ ملف أو مجلد"""
    try:
        if os.path.isdir(src):
            if os.path.exists(dst):
                shutil.rmtree(dst)
            shutil.copytree(src, dst)
            print_status(f"تم نسخ المجلد: {os.path.basename(src)}", "SUCCESS")
        else:
            shutil.copy2(src, dst)
            print_status(f"تم نسخ الملف: {os.path.basename(src)}", "SUCCESS")
        return True
    except Exception as e:
        print_status(f"خطأ في نسخ {src}: {str(e)}", "ERROR")
        return False

def create_diamond_sales_package():
    """إنشاء حزمة البرنامج الرئيسي"""
    print("\n" + "="*60)
    print("💎 إنشاء حزمة البرنامج الرئيسي")
    print("="*60)
    
    package_dir = "standalone_packages/DiamondSales_v1.30_Standalone"
    create_directory_if_not_exists(package_dir)
    
    # نسخ البرنامج الرئيسي مع جميع ملفاته
    source_dir = "installer_package/DiamondSales"
    if os.path.exists(source_dir):
        # نسخ جميع محتويات البرنامج الرئيسي
        for item in os.listdir(source_dir):
            src_path = os.path.join(source_dir, item)
            dst_path = os.path.join(package_dir, item)
            copy_file_or_directory(src_path, dst_path)
    
    # إنشاء ملف تعليمات خاص بالبرنامج الرئيسي
    readme_content = """# برنامج مبيعات الألماس - الإصدار 1.30
## النسخة المستقلة

### نظرة عامة
هذه النسخة المستقلة من برنامج مبيعات الألماس تعمل بدون الحاجة لتثبيت.

### متطلبات النظام
- Windows 10 أو أحدث
- 4 جيجابايت رام على الأقل
- 200 ميجابايت مساحة متاحة

### طريقة التشغيل
1. استخرج جميع الملفات إلى مجلد على جهازك
2. انقر نقراً مزدوجاً على DiamondSales.exe
3. ستبدأ شاشة تسجيل الدخول

### كلمة المرور الافتراضية
- اسم المستخدم: admin
- كلمة المرور: admin

### المميزات
✅ إدارة المبيعات والمخزون
✅ نظام العملاء
✅ التقارير المالية
✅ نظام الصندوق والخزينة
✅ واجهة عربية كاملة

### ملاحظات مهمة
- احرص على عدم حذف أي ملفات من المجلد
- يتم حفظ البيانات في ملف diamond_sales.db
- لا تقم بنقل الملفات منفردة، انقل المجلد كاملاً

### الدعم الفني
في حالة واجهت مشاكل:
- تأكد من تشغيل البرنامج كمسؤول
- تحقق من عدم حجب البرنامج بواسطة مكافح الفيروسات
- راجع ملف error_log.txt للأخطاء التفصيلية

تاريخ الإصدار: مايو 2025
"""
    
    with open(os.path.join(package_dir, "README.txt"), "w", encoding="utf-8") as f:
        f.write(readme_content)
    
    print_status("تم إنشاء حزمة البرنامج الرئيسي", "SUCCESS")
    return package_dir

def create_activation_generator_package():
    """إنشاء حزمة مولد أكواد التفعيل"""
    print("\n" + "="*60)
    print("🔑 إنشاء حزمة مولد أكواد التفعيل")
    print("="*60)
    
    package_dir = "standalone_packages/ActivationGenerator_v1.30_Standalone"
    create_directory_if_not_exists(package_dir)
    
    # نسخ مولد أكواد التفعيل
    source_dir = "installer_package/ActivationGenerator"
    if os.path.exists(source_dir):
        for item in os.listdir(source_dir):
            src_path = os.path.join(source_dir, item)
            dst_path = os.path.join(package_dir, item)
            copy_file_or_directory(src_path, dst_path)
    
    # إنشاء ملف تعليمات خاص بمولد التفعيل
    readme_content = """# مولد أكواد التفعيل - الإصدار 1.30
## النسخة المستقلة

### نظرة عامة
هذا البرنامج مخصص لإنشاء أكواد تفعيل لبرنامج مبيعات الألماس.

### متطلبات النظام
- Windows 10 أو أحدث
- 2 جيجابايت رام على الأقل
- 50 ميجابايت مساحة متاحة

### طريقة الاستخدام
1. استخرج جميع الملفات إلى مجلد
2. انقر نقراً مزدوجاً على ActivationGenerator.exe
3. أدخل معلومات العميل المطلوبة
4. اضغط "إنشاء كود التفعيل"
5. انسخ الكود المُنشأ وأرسله للعميل

### الوظائف الأساسية
✅ إنشاء أكواد تفعيل فريدة
✅ تحديد فترة صلاحية الترخيص
✅ ربط الترخيص بمعلومات العميل
✅ حفظ سجل بالتراخيص المُصدرة

### ملاحظات أمنية
⚠️ هذا البرنامج مخصص للاستخدام الداخلي فقط
⚠️ لا تشارك أكواد التفعيل مع أطراف غير مصرح لها
⚠️ احتفظ بنسخة احتياطية من قاعدة بيانات التراخيص

### استكشاف الأخطاء
- تأكد من وجود قاعدة بيانات البرنامج الرئيسي
- تحقق من صلاحيات الكتابة في المجلد
- شغل البرنامج كمسؤول إذا لزم الأمر

تاريخ الإصدار: مايو 2025
"""
    
    with open(os.path.join(package_dir, "README.txt"), "w", encoding="utf-8") as f:
        f.write(readme_content)
    
    print_status("تم إنشاء حزمة مولد أكواد التفعيل", "SUCCESS")
    return package_dir

def create_reset_password_package():
    """إنشاء حزمة أداة إعادة تعيين كلمة المرور"""
    print("\n" + "="*60)
    print("🔓 إنشاء حزمة أداة إعادة تعيين كلمة المرور")
    print("="*60)
    
    package_dir = "standalone_packages/ResetPassword_v1.30_Standalone"
    create_directory_if_not_exists(package_dir)
    
    # نسخ أداة إعادة تعيين كلمة المرور
    source_file = "installer_package/reset_password.exe"
    if os.path.exists(source_file):
        copy_file_or_directory(source_file, os.path.join(package_dir, "reset_password.exe"))
    
    # إنشاء ملف تعليمات خاص بأداة إعادة تعيين كلمة المرور
    readme_content = """# أداة إعادة تعيين كلمة المرور - الإصدار 1.30
## النسخة المستقلة

### نظرة عامة
هذه الأداة مخصصة لإعادة تعيين كلمة مرور المدير في برنامج مبيعات الألماس.

### متطلبات النظام
- Windows 10 أو أحدث
- 2 جيجابايت رام على الأقل
- 100 ميجابايت مساحة متاحة

### متى تستخدم هذه الأداة؟
- نسيان كلمة مرور المدير
- عدم القدرة على تسجيل الدخول للبرنامج
- الحاجة لإعادة تعيين صلاحيات المدير

### طريقة الاستخدام
1. أغلق برنامج مبيعات الألماس إذا كان يعمل
2. انسخ ملف reset_password.exe إلى مجلد البرنامج الرئيسي
3. تأكد من وجود ملف diamond_sales.db في نفس المجلد
4. انقر نقراً مزدوجاً على reset_password.exe
5. ستظهر رسالة تأكيد إعادة التعيين
6. كلمة المرور الجديدة ستكون: admin

### خطوات مهمة
⚠️ تأكد من إغلاق البرنامج الرئيسي قبل الاستخدام
⚠️ تأكد من وجود ملف قاعدة البيانات diamond_sales.db
⚠️ قم بعمل نسخة احتياطية من قاعدة البيانات قبل الاستخدام

### بعد إعادة التعيين
1. شغل برنامج مبيعات الألماس
2. استخدم:
   - اسم المستخدم: admin
   - كلمة المرور: admin
3. قم بتغيير كلمة المرور فوراً من إعدادات البرنامج

### استكشاف الأخطاء
- "لا يمكن العثور على قاعدة البيانات":
  → تأكد من وجود ملف diamond_sales.db في نفس المجلد
  
- "خطأ في الصلاحيات":
  → شغل الأداة كمسؤول (Run as Administrator)
  
- "فشل في إعادة التعيين":
  → تأكد من إغلاق البرنامج الرئيسي أولاً

تاريخ الإصدار: مايو 2025
"""
    
    with open(os.path.join(package_dir, "README.txt"), "w", encoding="utf-8") as f:
        f.write(readme_content)
    
    print_status("تم إنشاء حزمة أداة إعادة تعيين كلمة المرور", "SUCCESS")
    return package_dir

def create_zip_packages(packages):
    """إنشاء ملفات مضغوطة لكل حزمة"""
    print("\n" + "="*60)
    print("📦 إنشاء الملفات المضغوطة")
    print("="*60)
    
    zip_files = []
    
    for package_path in packages:
        if os.path.exists(package_path):
            package_name = os.path.basename(package_path)
            zip_filename = f"standalone_packages/{package_name}.zip"
            
            with zipfile.ZipFile(zip_filename, 'w', zipfile.ZIP_DEFLATED) as zipf:
                for root, dirs, files in os.walk(package_path):
                    for file in files:
                        file_path = os.path.join(root, file)
                        # حساب المسار النسبي داخل الملف المضغوط
                        arcname = os.path.relpath(file_path, package_path)
                        zipf.write(file_path, arcname)
            
            # حساب حجم الملف المضغوط
            zip_size = os.path.getsize(zip_filename)
            print_status(f"تم إنشاء: {package_name}.zip ({zip_size/1024/1024:.1f} MB)", "SUCCESS")
            zip_files.append(zip_filename)
    
    return zip_files

def create_master_readme():
    """إنشاء ملف تعليمات رئيسي لجميع الحزم"""
    print("\n" + "="*60)
    print("📚 إنشاء ملف التعليمات الرئيسي")
    print("="*60)
    
    readme_content = """# برنامج مبيعات الألماس - الإصدار 1.30
## الحزم المستقلة

### نظرة عامة
تم تقسيم برنامج مبيعات الألماس إلى ثلاث حزم منفصلة يمكن استخدامها بشكل مستقل:

## الحزم المتاحة

### 1. البرنامج الرئيسي 💎
**الملف:** `DiamondSales_v1.30_Standalone.zip`
**الحجم:** ~60 MB تقريباً
**الوصف:** البرنامج الأساسي لإدارة مبيعات الألماس

**المميزات:**
- إدارة شاملة للمبيعات والمخزون
- نظام العملاء والموردين
- التقارير المالية والإحصائيات
- نظام الصندوق والخزينة
- واجهة عربية كاملة

**متطلبات التشغيل:**
- Windows 10+
- 4 GB RAM
- 200 MB مساحة

### 2. مولد أكواد التفعيل 🔑
**الملف:** `ActivationGenerator_v1.30_Standalone.zip`
**الحجم:** ~40 MB تقريباً
**الوصف:** أداة إنشاء تراخيص وأكواد التفعيل

**المميزات:**
- إنشاء أكواد تفعيل فريدة
- إدارة التراخيص والصلاحيات
- ربط التراخيص بمعلومات العملاء
- تحديد فترة صلاحية

**متطلبات التشغيل:**
- Windows 10+
- 2 GB RAM
- 50 MB مساحة

### 3. أداة إعادة تعيين كلمة المرور 🔓
**الملف:** `ResetPassword_v1.30_Standalone.zip`
**الحجم:** ~45 MB تقريباً
**الوصف:** أداة طوارئ لإعادة تعيين كلمة مرور المدير

**المميزات:**
- إعادة تعيين كلمة مرور المدير
- إصلاح مشاكل تسجيل الدخول
- استرداد الوصول للنظام

**متطلبات التشغيل:**
- Windows 10+
- 2 GB RAM
- 100 MB مساحة

## تعليمات التثبيت والاستخدام

### للبرنامج الرئيسي
1. استخرج محتويات `DiamondSales_v1.30_Standalone.zip`
2. انقر نقراً مزدوجاً على `DiamondSales.exe`
3. استخدم كلمة المرور الافتراضية: admin/admin

### لمولد أكواد التفعيل
1. استخرج محتويات `ActivationGenerator_v1.30_Standalone.zip`
2. انقر نقراً مزدوجاً على `ActivationGenerator.exe`
3. أدخل معلومات العميل وأنشئ الكود

### لأداة إعادة تعيين كلمة المرور
1. استخرج محتويات `ResetPassword_v1.30_Standalone.zip`
2. انسخ `reset_password.exe` إلى مجلد البرنامج الرئيسي
3. أغلق البرنامج الرئيسي وشغل الأداة

## ملاحظات مهمة ⚠️

### الأمان
- احتفظ بنسخ احتياطية من قواعد البيانات
- لا تشارك أكواد التفعيل مع أطراف غير مصرح لها
- استخدم كلمات مرور قوية

### الاستخدام
- شغل البرامج كمسؤول إذا لزم الأمر
- لا تحذف الملفات المرافقة للبرامج
- تأكد من إغلاق البرامج بشكل صحيح

### الدعم الفني
- راجع ملفات README.txt في كل حزمة للتفاصيل
- تحقق من ملفات السجلات في حالة الأخطاء
- تأكد من استيفاء متطلبات النظام

## معلومات الإصدار
- **رقم الإصدار:** 1.30
- **تاريخ الإصدار:** مايو 2025
- **نوع الحزمة:** حزم مستقلة (Standalone)
- **دعم اللغة:** العربية والإنجليزية

---
**ملاحظة:** كل حزمة تعمل بشكل مستقل ولا تحتاج للحزم الأخرى إلا في حالات خاصة (مثل أداة إعادة تعيين كلمة المرور التي تحتاج للبرنامج الرئيسي).
"""
    
    with open("standalone_packages/README.txt", "w", encoding="utf-8") as f:
        f.write(readme_content)
    
    print_status("تم إنشاء ملف التعليمات الرئيسي", "SUCCESS")

def main():
    """الدالة الرئيسية"""
    print("🚀 إنشاء الحزم المستقلة لبرنامج مبيعات الألماس")
    print("="*70)
    
    # التأكد من وجود مجلد installer_package
    if not os.path.exists("installer_package"):
        print_status("لم يتم العثور على مجلد installer_package", "ERROR")
        print_status("يرجى تشغيل build_all.py أولاً", "WARNING")
        return False
    
    # إنشاء مجلد الحزم المستقلة
    create_directory_if_not_exists("standalone_packages")
    
    # إنشاء الحزم المنفصلة
    packages = []
    
    # إنشاء حزمة البرنامج الرئيسي
    diamond_sales_package = create_diamond_sales_package()
    packages.append(diamond_sales_package)
    
    # إنشاء حزمة مولد أكواد التفعيل
    activation_package = create_activation_generator_package()
    packages.append(activation_package)
    
    # إنشاء حزمة أداة إعادة تعيين كلمة المرور
    reset_package = create_reset_password_package()
    packages.append(reset_package)
    
    # إنشاء الملفات المضغوطة
    zip_files = create_zip_packages(packages)
    
    # إنشاء ملف التعليمات الرئيسي
    create_master_readme()
    
    # عرض النتائج النهائية
    print("\n" + "="*60)
    print("🎉 تم الانتهاء من إنشاء الحزم المستقلة")
    print("="*60)
    
    print("\n📦 الحزم المنشأة:")
    for zip_file in zip_files:
        if os.path.exists(zip_file):
            size = os.path.getsize(zip_file)
            print(f"  ✅ {os.path.basename(zip_file)} ({size/1024/1024:.1f} MB)")
    
    print("\n📁 مجلد الحزم: standalone_packages/")
    print("📚 ملف التعليمات: standalone_packages/README.txt")
    
    print("\n🎯 الحزم جاهزة للتوزيع!")
    return True

if __name__ == "__main__":
    main()
