import sys
import traceback
from PyQt6.QtWidgets import QApplication, QMessageBox
from PyQt6.QtGui import QFont
from login_screen import LoginWindow
import database
from translations import load_translations
from db_session import session_scope
from logger import log_error, log_info
import app_state
from activation import is_activated
from activation_screen import ActivationScreen
from ui_utils import center_window

# تعريف دالة للتعامل مع الاستثناءات غير المعالجة
def handle_exception(exc_type, exc_value, exc_traceback):
    """
    التعامل مع الاستثناءات غير المعالجة وتسجيلها
    """
    if issubclass(exc_type, KeyboardInterrupt):
        # السماح بإيقاف البرنامج باستخدام Ctrl+C
        sys.__excepthook__(exc_type, exc_value, exc_traceback)
        return

    # تسجيل الاستثناء
    error_msg = "".join(traceback.format_exception(exc_type, exc_value, exc_traceback))
    log_error(f"استثناء غير معالج: {error_msg}")

    # عرض رسالة خطأ للمستخدم
    if QApplication.instance():
        QMessageBox.critical(None, "خطأ غير متوقع",
                            "حدث خطأ غير متوقع في البرنامج. يرجى مراجعة سجل الأخطاء للحصول على مزيد من المعلومات.")

# تعيين معالج الاستثناءات
sys.excepthook = handle_exception

if __name__ == "__main__":
    try:
        # تهيئة قاعدة البيانات
        log_info("بدء تشغيل التطبيق")
        log_info("تهيئة قاعدة البيانات")
        database.init_db()

        # تهيئة حالة التطبيق
        log_info("تهيئة حالة التطبيق")
        app_state.initialize_app()

        # إنشاء التطبيق
        app = QApplication(sys.argv)

        # إنشاء شاشة البداية
        from ui_utils import SplashScreen
        splash = SplashScreen("assets/diamond_icon.png")
        splash.show()
        app.processEvents()

        # تحميل الإعدادات من قاعدة البيانات
        splash.set_message("جاري تحميل الإعدادات...")
        from database import Setting

        # الحصول على اللغة الافتراضية وإعدادات الخط من قاعدة البيانات
        default_language = "ar"  # اللغة العربية كلغة افتراضية

        try:
            with session_scope() as session:
                setting = session.query(Setting).first()
                if setting:
                    default_language = setting.language

                    # محاولة تطبيق إعدادات الخط
                    try:
                        # التحقق من وجود إعدادات الخط
                        if hasattr(setting, "font_family") and hasattr(setting, "font_size"):
                            font_family = setting.font_family if setting.font_family else "Arial"
                            font_size = setting.font_size if setting.font_size else 10
                            app_font = QFont(font_family, font_size)
                            app.setFont(app_font)
                            log_info(f"تم تطبيق الخط: {font_family}، الحجم: {font_size}")
                        else:
                            log_info("إعدادات الخط غير متوفرة في قاعدة البيانات")
                    except Exception as font_error:
                        log_error("خطأ في تطبيق إعدادات الخط", font_error)
                        # استخدام خط افتراضي في حالة حدوث خطأ
                        app_font = QFont("Arial", 10)
                        app.setFont(app_font)
                else:
                    log_info("لم يتم العثور على إعدادات، استخدام الإعدادات الافتراضية")
        except Exception as e:
            log_error("خطأ في تحميل الإعدادات", e)

        # تحميل الترجمات
        splash.set_message("جاري تحميل الترجمات...")
        log_info(f"تحميل الترجمات للغة: {default_language}")
        translations = load_translations(default_language)

        # التحقق من تفعيل البرنامج
        splash.set_message("جاري التحقق من تفعيل البرنامج...")

        if is_activated():
            # إنشاء نافذة تسجيل الدخول
            splash.set_message("جاري تهيئة واجهة المستخدم...")
            log_info("البرنامج مفعل، تهيئة نافذة تسجيل الدخول")
            login_window = LoginWindow()

            # إخفاء شاشة البداية وعرض نافذة تسجيل الدخول
            splash.finish(login_window)
            log_info("عرض نافذة تسجيل الدخول")
            center_window(login_window)
            login_window.show()
        else:
            # إنشاء نافذة التفعيل
            splash.set_message("البرنامج غير مفعل...")
            log_info("البرنامج غير مفعل، تهيئة نافذة التفعيل")
            activation_screen = ActivationScreen()

            # إضافة دالة للانتقال إلى شاشة تسجيل الدخول بعد التفعيل
            def show_login_after_activation():
                activation_screen.hide()
                login_window = LoginWindow()
                center_window(login_window)
                login_window.show()

            activation_screen.parent = None  # إزالة الإشارة الدائرية
            activation_screen.show_login_screen = show_login_after_activation

            # إخفاء شاشة البداية وعرض نافذة التفعيل
            splash.finish(activation_screen)
            log_info("عرض نافذة التفعيل")
            center_window(activation_screen)
            activation_screen.show()

        # تشغيل التطبيق
        sys.exit(app.exec())
    except Exception as e:
        log_error("خطأ أثناء بدء تشغيل التطبيق", e)
        if QApplication.instance():
            QMessageBox.critical(None, "خطأ في بدء التشغيل",
                                "حدث خطأ أثناء بدء تشغيل التطبيق. يرجى مراجعة سجل الأخطاء للحصول على مزيد من المعلومات.")
        sys.exit(1)