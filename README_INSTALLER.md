# دليل إنشاء الملفات التنفيذية وبرنامج التثبيت

هذا الدليل يشرح كيفية إنشاء الملفات التنفيذية (.exe) وبرنامج التثبيت لنظام إدارة مبيعات الألماس.

## المتطلبات الأساسية

1. **Python 3.8+** مثبت على جهازك
2. **PyInstaller** لإنشاء الملفات التنفيذية
3. **Inno Setup Compiler** لإنشاء برنامج التثبيت

## تثبيت المتطلبات

### تثبيت PyInstaller

```
pip install pyinstaller
```

### تثبيت Inno Setup Compiler

1. قم بتنزيل Inno Setup Compiler من الموقع الرسمي: [https://jrsoftware.org/isdl.php](https://jrsoftware.org/isdl.php)
2. قم بتثبيت البرنامج باتباع التعليمات

## خطوات إنشاء الملفات التنفيذية وبرنامج التثبيت

### الطريقة السريعة (تلقائية)

قم بتشغيل السكريبت `build_all.py` الذي سيقوم بتنفيذ جميع الخطوات تلقائياً:

```
python build_all.py
```

### الطريقة اليدوية (خطوة بخطوة)

#### 1. إنشاء الأيقونات

قم بتشغيل السكريبت `create_simple_icons.py` لإنشاء الأيقونات اللازمة:

```
python create_simple_icons.py
```

#### 2. إنشاء الملفات التنفيذية

قم بتشغيل PyInstaller لإنشاء الملفات التنفيذية باستخدام ملفات الـ spec:

```
pyinstaller diamond_sales.spec
pyinstaller activation_generator.spec
```

#### 3. إنشاء مجلد التوزيع

قم بنسخ الملفات التنفيذية إلى مجلد التوزيع:

```
python build_exe_files.py
```

#### 4. إنشاء برنامج التثبيت

قم بتشغيل Inno Setup Compiler لإنشاء برنامج التثبيت:

```
"C:\Program Files (x86)\Inno Setup 6\ISCC.exe" diamond_installer.iss
```

## مخرجات العملية

بعد الانتهاء من العملية، ستجد:

1. **الملفات التنفيذية** في مجلد `dist_package`
2. **برنامج التثبيت** في مجلد `installer` باسم `DiamondSalesSetup.exe`

## حل المشكلات الشائعة

### مشكلة: عدم العثور على Inno Setup Compiler

تأكد من تثبيت Inno Setup Compiler في المسار الافتراضي. إذا قمت بتثبيته في مسار مختلف، قم بتعديل المسار في ملف `build_all.py`.

### مشكلة: أخطاء في PyInstaller

إذا واجهت أخطاء في PyInstaller، جرب الخطوات التالية:

1. تحديث PyInstaller إلى أحدث إصدار:
   ```
   pip install --upgrade pyinstaller
   ```

2. حذف مجلدات `build` و `dist` وإعادة المحاولة:
   ```
   rmdir /s /q build
   rmdir /s /q dist
   ```

### مشكلة: أخطاء في الملفات التنفيذية

إذا كانت الملفات التنفيذية لا تعمل بشكل صحيح، تأكد من:

1. تضمين جميع المكتبات المطلوبة في ملفات الـ spec
2. نسخ جميع الملفات اللازمة (مثل قاعدة البيانات والأصول) إلى مجلد التوزيع

## ملاحظات هامة

- تأكد من اختبار الملفات التنفيذية قبل إنشاء برنامج التثبيت
- قم بتحديث إصدار البرنامج في ملف `diamond_installer.iss` عند إصدار نسخة جديدة
- تأكد من أن جميع المسارات في ملفات الـ spec وملف Inno Setup صحيحة
