"""
تهيئة قاعدة البيانات لصندوق النقدية وسندات الصرف
"""

from sqlalchemy import create_engine, func, or_, case, String, Float
from database import Base, صندوق_النقدية, حركة_نقدية
from datetime import datetime
import os
import sys

def initialize_cash_box_database():
    """
    تهيئة جداول صندوق النقدية وحركات النقدية
    """
    # الحصول على مسار ملف قاعدة البيانات
    current_dir = os.path.dirname(os.path.abspath(__file__))
    db_path = os.path.join(current_dir, 'diamond_sales.db')
    
    print(f"التحقق من قاعدة البيانات في: {db_path}")
    print(f"هل الملف موجود؟ {os.path.exists(db_path)}")
    
    # إنشاء محرك قاعدة البيانات
    engine_url = f'sqlite:///{db_path}'
    engine = create_engine(engine_url)
    
    # التحقق من وجود الجداول
    from sqlalchemy import inspect
    inspector = inspect(engine)
    
    # طباعة جميع الجداول الموجودة
    tables = inspector.get_table_names()
    print(f"الجداول الموجودة في قاعدة البيانات: {tables}")
    
    # هل جدول صندوق النقدية موجود؟
    has_cash_box = 'cash_box' in tables
    # هل جدول حركات النقدية موجود؟
    has_cash_transactions = 'cash_transactions' in tables
    
    print(f"جدول صندوق النقدية موجود: {has_cash_box}")
    print(f"جدول حركات النقدية موجود: {has_cash_transactions}")
    
    # إنشاء الجداول إذا لم تكن موجودة
    if not has_cash_box or not has_cash_transactions:
        print("إنشاء الجداول المفقودة...")
        
        # طباعة الجداول المتاحة في Base.metadata.tables
        print(f"الجداول المتاحة في Base.metadata: {list(Base.metadata.tables.keys())}")
        
        try:
            # إنشاء الجداول من الكلاسات
            tables_to_create = []
            if not has_cash_box:
                tables_to_create.append('cash_box')
            if not has_cash_transactions:
                tables_to_create.append('cash_transactions')
                
            print(f"محاولة إنشاء الجداول: {tables_to_create}")
            Base.metadata.create_all(engine, tables=tables_to_create)
            print("تم إنشاء الجداول بنجاح")
        except Exception as e:
            print(f"خطأ أثناء إنشاء الجداول: {e}")
    else:
        print("الجداول موجودة بالفعل")
    
    # التحقق من وجود صندوق نقدية
    print("التحقق من وجود سجل صندوق النقدية...")
    
    from sqlalchemy.orm import sessionmaker
    Session = sessionmaker(bind=engine)
    session = Session()
    
    try:
        cash_box = session.query(صندوق_النقدية).first()
        if not cash_box:
            print("إنشاء سجل لصندوق النقدية...")
            cash_box = صندوق_النقدية(balance=0.0, last_updated=datetime.now())
            session.add(cash_box)
            session.commit()
            print(f"تم إنشاء سجل صندوق النقدية بمعرّف {cash_box.id}")
        else:
            print(f"سجل صندوق النقدية موجود بمعرّف {cash_box.id} ورصيد {cash_box.balance}")
        
        # طباعة إحصائيات حركات النقدية
        transactions_count = session.query(func.count(حركة_نقدية.id)).scalar() or 0
        print(f"عدد حركات النقدية الموجودة: {transactions_count}")
    except Exception as e:
        print(f"خطأ أثناء التحقق من سجل صندوق النقدية: {e}")
    finally:
        # إغلاق الجلسة
        session.close()
    
    return True

if __name__ == "__main__":
    print("بدء عملية تهيئة قاعدة بيانات صندوق النقدية...")
    result = initialize_cash_box_database()
    print(f"انتهت عملية التهيئة. النتيجة: {result}")
    
    # إضافة هذا الكود للتأكد من أن النافذة تبقى مفتوحة عند التشغيل من Explorer
    if len(sys.argv) > 1 and sys.argv[1] == "--pause":
        input("اضغط Enter للإغلاق...")
