#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Diamond Sales V1.30 - Create Individual Installers
إنشاء برامج تثبيت منفصلة لكل برنامج
"""

import os
import shutil
from pathlib import Path

def print_status(message, status="INFO"):
    """طباعة رسالة حالة بتنسيق منظم"""
    symbols = {
        "INFO": "ℹ️",
        "SUCCESS": "✅", 
        "ERROR": "❌",
        "WARNING": "⚠️"
    }
    print(f"{symbols.get(status, 'ℹ️')} {message}")

def create_directory_if_not_exists(path):
    """إنشاء مجلد إذا لم يكن موجوداً"""
    Path(path).mkdir(parents=True, exist_ok=True)

def create_diamond_sales_installer():
    """إنشاء برنامج تثبيت البرنامج الرئيسي"""
    print("\n" + "="*60)
    print("💎 إنشاء برنامج تثبيت البرنامج الرئيسي")
    print("="*60)
    
    installer_content = """[Setup]
AppName=Diamond Sales
AppVersion=1.30
AppPublisher=Diamond Sales Company
AppPublisherURL=http://www.diamondsales.com
AppSupportURL=http://www.diamondsales.com/support
AppUpdatesURL=http://www.diamondsales.com/updates
DefaultDirName={autopf}\\Diamond Sales
DefaultGroupName=Diamond Sales
AllowNoIcons=yes
LicenseFile=
InfoBeforeFile=
InfoAfterFile=
OutputDir=installer_output
OutputBaseFilename=DiamondSalesMainProgram_v1.30_Setup
SetupIconFile=
Compression=lzma
SolidCompression=yes
WizardStyle=modern
PrivilegesRequired=admin
ArchitecturesInstallIn64BitMode=x64

[Languages]
Name: "english"; MessagesFile: "compiler:Default.isl"

[Tasks]
Name: "desktopicon"; Description: "{cm:CreateDesktopIcon}"; GroupDescription: "{cm:AdditionalIcons}"; Flags: unchecked
Name: "quicklaunchicon"; Description: "{cm:CreateQuickLaunchIcon}"; GroupDescription: "{cm:AdditionalIcons}"; Flags: unchecked; OnlyBelowVersion: 0,6.1

[Files]
Source: "standalone_packages\\DiamondSales_v1.30_Standalone\\*"; DestDir: "{app}"; Flags: ignoreversion recursesubdirs createallsubdirs

[Icons]
Name: "{group}\\Diamond Sales"; Filename: "{app}\\DiamondSales.exe"
Name: "{group}\\{cm:ProgramOnTheWeb,Diamond Sales}"; Filename: "http://www.diamondsales.com"
Name: "{group}\\{cm:UninstallProgram,Diamond Sales}"; Filename: "{uninstallexe}"
Name: "{autodesktop}\\Diamond Sales"; Filename: "{app}\\DiamondSales.exe"; Tasks: desktopicon
Name: "{userappdata}\\Microsoft\\Internet Explorer\\Quick Launch\\Diamond Sales"; Filename: "{app}\\DiamondSales.exe"; Tasks: quicklaunchicon

[Run]
Filename: "{app}\\DiamondSales.exe"; Description: "{cm:LaunchProgram,Diamond Sales}"; Flags: nowait postinstall skipifsilent

[UninstallDelete]
Type: filesandordirs; Name: "{app}"

[Messages]
BeveledLabel=Diamond Sales v1.30 - Main Program Installer

[Code]
procedure InitializeWizard;
begin
  WizardForm.WelcomeLabel1.Caption := 'Welcome to Diamond Sales Setup';
  WizardForm.WelcomeLabel2.Caption := 'This will install Diamond Sales version 1.30 on your computer.' + #13#10#13#10 +
    'Diamond Sales is a comprehensive diamond retail management system with Arabic language support.' + #13#10#13#10 +
    'It is recommended that you close all other applications before continuing.';
end;

function InitializeSetup(): Boolean;
begin
  Result := True;
  if MsgBox('This installer will install the main Diamond Sales program.' + #13#10 +
           'The program includes:' + #13#10 +
           '• Sales and inventory management' + #13#10 +
           '• Customer management system' + #13#10 +
           '• Financial reports' + #13#10 +
           '• Cash box management' + #13#10 +
           '• Full Arabic language support' + #13#10#13#10 +
           'Do you want to continue?', mbConfirmation, MB_YESNO) = IDNO then
    Result := False;
end;

procedure CurStepChanged(CurStep: TSetupStep);
begin
  if CurStep = ssPostInstall then
  begin
    MsgBox('Diamond Sales has been successfully installed!' + #13#10#13#10 +
           'Default login credentials:' + #13#10 +
           'Username: admin' + #13#10 +
           'Password: admin' + #13#10#13#10 +
           'Please change the password after first login for security.', mbInformation, MB_OK);
  end;
end;
"""
    
    with open("diamond_sales_main_installer.iss", "w", encoding="utf-8") as f:
        f.write(installer_content)
    
    print_status("تم إنشاء ملف installer: diamond_sales_main_installer.iss", "SUCCESS")
    return "diamond_sales_main_installer.iss"

def create_activation_generator_installer():
    """إنشاء برنامج تثبيت مولد أكواد التفعيل"""
    print("\n" + "="*60)
    print("🔑 إنشاء برنامج تثبيت مولد أكواد التفعيل")
    print("="*60)
    
    installer_content = """[Setup]
AppName=Diamond Sales - Activation Generator
AppVersion=1.30
AppPublisher=Diamond Sales Company
AppPublisherURL=http://www.diamondsales.com
AppSupportURL=http://www.diamondsales.com/support
AppUpdatesURL=http://www.diamondsales.com/updates
DefaultDirName={autopf}\\Diamond Sales\\Activation Generator
DefaultGroupName=Diamond Sales\\Tools
AllowNoIcons=yes
LicenseFile=
InfoBeforeFile=
InfoAfterFile=
OutputDir=installer_output
OutputBaseFilename=DiamondSalesActivationGenerator_v1.30_Setup
SetupIconFile=
Compression=lzma
SolidCompression=yes
WizardStyle=modern
PrivilegesRequired=admin
ArchitecturesInstallIn64BitMode=x64

[Languages]
Name: "english"; MessagesFile: "compiler:Default.isl"

[Tasks]
Name: "desktopicon"; Description: "{cm:CreateDesktopIcon}"; GroupDescription: "{cm:AdditionalIcons}"; Flags: unchecked
Name: "quicklaunchicon"; Description: "{cm:CreateQuickLaunchIcon}"; GroupDescription: "{cm:AdditionalIcons}"; Flags: unchecked; OnlyBelowVersion: 0,6.1

[Files]
Source: "standalone_packages\\ActivationGenerator_v1.30_Standalone\\*"; DestDir: "{app}"; Flags: ignoreversion recursesubdirs createallsubdirs

[Icons]
Name: "{group}\\Activation Generator"; Filename: "{app}\\ActivationGenerator.exe"
Name: "{group}\\{cm:UninstallProgram,Activation Generator}"; Filename: "{uninstallexe}"
Name: "{autodesktop}\\Diamond Sales - Activation Generator"; Filename: "{app}\\ActivationGenerator.exe"; Tasks: desktopicon
Name: "{userappdata}\\Microsoft\\Internet Explorer\\Quick Launch\\Activation Generator"; Filename: "{app}\\ActivationGenerator.exe"; Tasks: quicklaunchicon

[Run]
Filename: "{app}\\ActivationGenerator.exe"; Description: "{cm:LaunchProgram,Activation Generator}"; Flags: nowait postinstall skipifsilent

[UninstallDelete]
Type: filesandordirs; Name: "{app}"

[Messages]
BeveledLabel=Diamond Sales v1.30 - Activation Generator Installer

[Code]
procedure InitializeWizard;
begin
  WizardForm.WelcomeLabel1.Caption := 'Welcome to Activation Generator Setup';
  WizardForm.WelcomeLabel2.Caption := 'This will install Diamond Sales Activation Generator version 1.30 on your computer.' + #13#10#13#10 +
    'The Activation Generator is a tool for creating license codes for Diamond Sales software.' + #13#10#13#10 +
    'This tool is intended for authorized distributors and administrators only.' + #13#10#13#10 +
    'It is recommended that you close all other applications before continuing.';
end;

function InitializeSetup(): Boolean;
begin
  Result := True;
  if MsgBox('This installer will install the Diamond Sales Activation Generator.' + #13#10 +
           'This tool is for:' + #13#10 +
           '• Creating unique activation codes' + #13#10 +
           '• Managing software licenses' + #13#10 +
           '• Linking licenses to customer information' + #13#10 +
           '• Setting license expiration dates' + #13#10#13#10 +
           'WARNING: This tool should only be used by authorized personnel.' + #13#10#13#10 +
           'Do you want to continue?', mbConfirmation, MB_YESNO) = IDNO then
    Result := False;
end;

procedure CurStepChanged(CurStep: TSetupStep);
begin
  if CurStep = ssPostInstall then
  begin
    MsgBox('Activation Generator has been successfully installed!' + #13#10#13#10 +
           'IMPORTANT SECURITY NOTICE:' + #13#10 +
           '• This tool should only be used by authorized personnel' + #13#10 +
           '• Do not share activation codes with unauthorized parties' + #13#10 +
           '• Keep a backup record of all issued licenses' + #13#10 +
           '• Ensure the main Diamond Sales database is accessible', mbInformation, MB_OK);
  end;
end;
"""
    
    with open("activation_generator_installer.iss", "w", encoding="utf-8") as f:
        f.write(installer_content)
    
    print_status("تم إنشاء ملف installer: activation_generator_installer.iss", "SUCCESS")
    return "activation_generator_installer.iss"

def create_reset_password_installer():
    """إنشاء برنامج تثبيت أداة إعادة تعيين كلمة المرور"""
    print("\n" + "="*60)
    print("🔓 إنشاء برنامج تثبيت أداة إعادة تعيين كلمة المرور")
    print("="*60)
    
    installer_content = """[Setup]
AppName=Diamond Sales - Password Reset Tool
AppVersion=1.30
AppPublisher=Diamond Sales Company
AppPublisherURL=http://www.diamondsales.com
AppSupportURL=http://www.diamondsales.com/support
AppUpdatesURL=http://www.diamondsales.com/updates
DefaultDirName={autopf}\\Diamond Sales\\Password Reset Tool
DefaultGroupName=Diamond Sales\\Tools
AllowNoIcons=yes
LicenseFile=
InfoBeforeFile=
InfoAfterFile=
OutputDir=installer_output
OutputBaseFilename=DiamondSalesPasswordReset_v1.30_Setup
SetupIconFile=
Compression=lzma
SolidCompression=yes
WizardStyle=modern
PrivilegesRequired=admin
ArchitecturesInstallIn64BitMode=x64

[Languages]
Name: "english"; MessagesFile: "compiler:Default.isl"

[Tasks]
Name: "desktopicon"; Description: "{cm:CreateDesktopIcon}"; GroupDescription: "{cm:AdditionalIcons}"; Flags: unchecked
Name: "quicklaunchicon"; Description: "{cm:CreateQuickLaunchIcon}"; GroupDescription: "{cm:AdditionalIcons}"; Flags: unchecked; OnlyBelowVersion: 0,6.1

[Files]
Source: "standalone_packages\\ResetPassword_v1.30_Standalone\\*"; DestDir: "{app}"; Flags: ignoreversion recursesubdirs createallsubdirs

[Icons]
Name: "{group}\\Password Reset Tool"; Filename: "{app}\\reset_password.exe"
Name: "{group}\\{cm:UninstallProgram,Password Reset Tool}"; Filename: "{uninstallexe}"
Name: "{autodesktop}\\Diamond Sales - Password Reset"; Filename: "{app}\\reset_password.exe"; Tasks: desktopicon
Name: "{userappdata}\\Microsoft\\Internet Explorer\\Quick Launch\\Password Reset"; Filename: "{app}\\reset_password.exe"; Tasks: quicklaunchicon

[UninstallDelete]
Type: filesandordirs; Name: "{app}"

[Messages]
BeveledLabel=Diamond Sales v1.30 - Password Reset Tool Installer

[Code]
procedure InitializeWizard;
begin
  WizardForm.WelcomeLabel1.Caption := 'Welcome to Password Reset Tool Setup';
  WizardForm.WelcomeLabel2.Caption := 'This will install Diamond Sales Password Reset Tool version 1.30 on your computer.' + #13#10#13#10 +
    'The Password Reset Tool is an emergency utility for resetting administrator passwords.' + #13#10#13#10 +
    'This tool is intended for technical support personnel only.' + #13#10#13#10 +
    'It is recommended that you close all other applications before continuing.';
end;

function InitializeSetup(): Boolean;
begin
  Result := True;
  if MsgBox('This installer will install the Diamond Sales Password Reset Tool.' + #13#10 +
           'This emergency tool is used for:' + #13#10 +
           '• Resetting administrator password' + #13#10 +
           '• Recovering access to the system' + #13#10 +
           '• Fixing login-related issues' + #13#10 +
           '• Technical support scenarios' + #13#10#13#10 +
           'WARNING: This tool should only be used by technical support staff.' + #13#10#13#10 +
           'Do you want to continue?', mbConfirmation, MB_YESNO) = IDNO then
    Result := False;
end;

procedure CurStepChanged(CurStep: TSetupStep);
begin
  if CurStep = ssPostInstall then
  begin
    MsgBox('Password Reset Tool has been successfully installed!' + #13#10#13#10 +
           'USAGE INSTRUCTIONS:' + #13#10 +
           '1. Close the main Diamond Sales program' + #13#10 +
           '2. Copy this tool to the main program folder' + #13#10 +
           '3. Ensure diamond_sales.db file is present' + #13#10 +
           '4. Run the tool to reset password to "admin"' + #13#10 +
           '5. Change password immediately after login' + #13#10#13#10 +
           'Make sure to backup the database before using this tool!', mbInformation, MB_OK);
  end;
end;
"""
    
    with open("reset_password_installer.iss", "w", encoding="utf-8") as f:
        f.write(installer_content)
    
    print_status("تم إنشاء ملف installer: reset_password_installer.iss", "SUCCESS")
    return "reset_password_installer.iss"

def build_installers():
    """بناء جميع برامج التثبيت"""
    print("\n" + "="*60)
    print("🏗️ بناء برامج التثبيت")
    print("="*60)
    
    # التحقق من وجود Inno Setup
    inno_setup_paths = [
        r"C:\Program Files (x86)\Inno Setup 6\ISCC.exe",
        r"C:\Program Files\Inno Setup 6\ISCC.exe",
        r"C:\Program Files (x86)\Inno Setup 5\ISCC.exe",
        r"C:\Program Files\Inno Setup 5\ISCC.exe"
    ]
    
    iscc_path = None
    for path in inno_setup_paths:
        if os.path.exists(path):
            iscc_path = path
            break
    
    if not iscc_path:
        print_status("لم يتم العثور على Inno Setup. يرجى تثبيته أولاً", "ERROR")
        print_status("يمكن تحميله من: https://jrsoftware.org/isdl.php", "INFO")
        return False
    
    print_status(f"تم العثور على Inno Setup في: {iscc_path}", "SUCCESS")
    
    # قائمة ملفات .iss للبناء
    iss_files = [
        "diamond_sales_main_installer.iss",
        "activation_generator_installer.iss", 
        "reset_password_installer.iss"
    ]
    
    built_installers = []
    
    for iss_file in iss_files:
        if os.path.exists(iss_file):
            print_status(f"بناء: {iss_file}", "INFO")
            
            # تشغيل Inno Setup compiler
            import subprocess
            try:
                result = subprocess.run([iscc_path, iss_file], 
                                      capture_output=True, text=True, cwd=os.getcwd())
                
                if result.returncode == 0:
                    print_status(f"تم بناء {iss_file} بنجاح", "SUCCESS")
                    built_installers.append(iss_file)
                else:
                    print_status(f"فشل في بناء {iss_file}", "ERROR")
                    print_status(f"خطأ: {result.stderr}", "ERROR")
            
            except Exception as e:
                print_status(f"خطأ في تشغيل Inno Setup: {str(e)}", "ERROR")
        else:
            print_status(f"ملف غير موجود: {iss_file}", "ERROR")
    
    return built_installers

def main():
    """الدالة الرئيسية"""
    print("🚀 إنشاء برامج تثبيت منفصلة لبرامج مبيعات الألماس")
    print("="*70)
    
    # التأكد من وجود الحزم المستقلة
    if not os.path.exists("standalone_packages"):
        print_status("لم يتم العثور على مجلد standalone_packages", "ERROR")
        print_status("يرجى تشغيل create_standalone_packages.py أولاً", "WARNING")
        return False
    
    # إنشاء مجلد مخرجات برامج التثبيت
    create_directory_if_not_exists("installer_output")
    
    # إنشاء ملفات .iss
    installers_created = []
    
    # إنشاء برنامج تثبيت البرنامج الرئيسي
    main_installer = create_diamond_sales_installer()
    installers_created.append(main_installer)
    
    # إنشاء برنامج تثبيت مولد التفعيل
    activation_installer = create_activation_generator_installer()
    installers_created.append(activation_installer)
    
    # إنشاء برنامج تثبيت أداة إعادة تعيين كلمة المرور
    reset_installer = create_reset_password_installer()
    installers_created.append(reset_installer)
    
    print("\n" + "="*60)
    print("📄 ملفات .iss المُنشأة:")
    print("="*60)
    for installer in installers_created:
        print_status(f"✅ {installer}", "SUCCESS")
    
    # بناء برامج التثبيت
    built_installers = build_installers()
    
    # عرض النتائج النهائية
    print("\n" + "="*60)
    print("🎉 نتائج إنشاء برامج التثبيت")
    print("="*60)
    
    print(f"\n📊 الإحصائيات:")
    print(f"   - ملفات .iss مُنشأة: {len(installers_created)}")
    print(f"   - برامج تثبيت مبنية: {len(built_installers)}")
    
    if len(built_installers) == len(installers_created):
        print_status("تم بناء جميع برامج التثبيت بنجاح! 🎉", "SUCCESS")
        
        # عرض برامج التثبيت المُنشأة
        print("\n📦 برامج التثبيت الجاهزة:")
        installer_files = [
            "DiamondSalesMainProgram_v1.30_Setup.exe",
            "DiamondSalesActivationGenerator_v1.30_Setup.exe", 
            "DiamondSalesPasswordReset_v1.30_Setup.exe"
        ]
        
        total_size = 0
        for installer_file in installer_files:
            installer_path = os.path.join("installer_output", installer_file)
            if os.path.exists(installer_path):
                size = os.path.getsize(installer_path)
                total_size += size
                print(f"   ✅ {installer_file} ({size/1024/1024:.1f} MB)")
            else:
                print(f"   ❌ {installer_file} (غير موجود)")
        
        print(f"\n📈 الحجم الإجمالي: {total_size/1024/1024:.1f} MB")
        
        print("\n🎯 توصيات التوزيع:")
        print("   📦 DiamondSalesMainProgram_v1.30_Setup.exe - للعملاء العاديين")
        print("   🔑 DiamondSalesActivationGenerator_v1.30_Setup.exe - للموزعين فقط")
        print("   🔓 DiamondSalesPasswordReset_v1.30_Setup.exe - لفرق الدعم الفني")
        
    else:
        print_status("فشل في بناء بعض برامج التثبيت", "WARNING")
        print_status("تأكد من تثبيت Inno Setup ووجود الملفات المطلوبة", "INFO")
    
    return len(built_installers) > 0

if __name__ == "__main__":
    main()
