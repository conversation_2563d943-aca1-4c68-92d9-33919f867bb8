#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
سكريبت لبناء برامج تثبيت منفصلة للبرنامج الرئيسي ومولد أكواد التفعيل
"""

import os
import subprocess
import shutil
import time

def run_command(command):
    """
    تنفيذ أمر وعرض مخرجاته في الوقت الفعلي
    
    المعاملات:
        command (str): الأمر المراد تنفيذه
        
    العائد:
        bool: True إذا تم تنفيذ الأمر بنجاح، False في حالة حدوث خطأ
    """
    print(f"جاري تنفيذ: {command}")
    process = subprocess.Popen(
        command,
        shell=True,
        stdout=subprocess.PIPE,
        stderr=subprocess.STDOUT,
        universal_newlines=True
    )
    
    # عرض المخرجات في الوقت الفعلي
    for line in process.stdout:
        print(line, end='')
    
    # انتظار انتهاء العملية
    process.wait()
    
    # التحقق من نجاح تنفيذ الأمر
    if process.returncode != 0:
        print(f"حدث خطأ أثناء تنفيذ الأمر. رمز الخروج: {process.returncode}")
        return False
    
    return True

def create_icons():
    """
    إنشاء الأيقونات اللازمة للبرنامج
    
    العائد:
        bool: True إذا تم الإنشاء بنجاح، False في حالة حدوث خطأ
    """
    print("\n=== جاري إنشاء الأيقونات ===\n")
    
    # تنفيذ سكريبت إنشاء الأيقونات
    if not run_command("python create_simple_icons.py"):
        return False
    
    print("\nتم إنشاء الأيقونات بنجاح.")
    return True

def build_main_executable():
    """
    بناء الملف التنفيذي للبرنامج الرئيسي
    
    العائد:
        bool: True إذا تم البناء بنجاح، False في حالة حدوث خطأ
    """
    print("\n=== جاري بناء البرنامج الرئيسي ===\n")
    
    # حذف مجلدات الإخراج السابقة
    if os.path.exists("dist/DiamondSales"):
        print("حذف مجلد الإخراج السابق...")
        shutil.rmtree("dist/DiamondSales")
    
    # بناء الملف التنفيذي
    if not run_command("pyinstaller diamond_sales.spec"):
        return False
    
    print("\nتم بناء البرنامج الرئيسي بنجاح.")
    return True

def build_activation_generator():
    """
    بناء الملف التنفيذي لمولد أكواد التفعيل
    
    العائد:
        bool: True إذا تم البناء بنجاح، False في حالة حدوث خطأ
    """
    print("\n=== جاري بناء مولد أكواد التفعيل ===\n")
    
    # حذف مجلدات الإخراج السابقة
    if os.path.exists("dist/ActivationGenerator"):
        print("حذف مجلد الإخراج السابق...")
        shutil.rmtree("dist/ActivationGenerator")
    
    # بناء الملف التنفيذي
    if not run_command("pyinstaller activation_generator.spec"):
        return False
    
    print("\nتم بناء مولد أكواد التفعيل بنجاح.")
    return True

def create_distribution_folders():
    """
    إنشاء مجلدات للتوزيع تحتوي على الملفات التنفيذية المبنية
    
    العائد:
        bool: True إذا تم الإنشاء بنجاح، False في حالة حدوث خطأ
    """
    print("\n=== جاري إنشاء مجلدات التوزيع ===\n")
    
    # إنشاء مجلد التوزيع إذا لم يكن موجوداً
    if not os.path.exists("dist_package"):
        os.makedirs("dist_package")
    
    # نسخ الملفات التنفيذية إلى مجلد التوزيع
    try:
        # نسخ البرنامج الرئيسي
        if os.path.exists("dist_package/DiamondSales"):
            shutil.rmtree("dist_package/DiamondSales")
        shutil.copytree("dist/DiamondSales", "dist_package/DiamondSales")
        
        # نسخ مولد أكواد التفعيل
        if os.path.exists("dist_package/ActivationGenerator"):
            shutil.rmtree("dist_package/ActivationGenerator")
        shutil.copytree("dist/ActivationGenerator", "dist_package/ActivationGenerator")
        
        print("\nتم إنشاء مجلدات التوزيع بنجاح.")
        return True
    except Exception as e:
        print(f"حدث خطأ أثناء إنشاء مجلدات التوزيع: {str(e)}")
        return False

def create_main_program_installer():
    """
    إنشاء برنامج تثبيت للبرنامج الرئيسي
    
    العائد:
        bool: True إذا تم الإنشاء بنجاح، False في حالة حدوث خطأ
    """
    print("\n=== جاري إنشاء برنامج تثبيت البرنامج الرئيسي ===\n")
    
    # إنشاء مجلد للمثبت إذا لم يكن موجوداً
    if not os.path.exists("installer"):
        os.makedirs("installer")
    
    # البحث عن مسار Inno Setup Compiler
    inno_setup_path = None
    possible_paths = [
        r"C:\Program Files (x86)\Inno Setup 6\ISCC.exe",
        r"C:\Program Files\Inno Setup 6\ISCC.exe",
        r"C:\Program Files (x86)\Inno Setup 5\ISCC.exe",
        r"C:\Program Files\Inno Setup 5\ISCC.exe"
    ]
    
    for path in possible_paths:
        if os.path.exists(path):
            inno_setup_path = path
            break
    
    if inno_setup_path is None:
        print("لم يتم العثور على Inno Setup Compiler. يرجى تثبيته أولاً.")
        print("يمكنك تنزيله من: https://jrsoftware.org/isdl.php")
        return False
    
    # تنفيذ Inno Setup Compiler
    if not run_command(f'"{inno_setup_path}" main_program_setup.iss'):
        return False
    
    print("\nتم إنشاء برنامج تثبيت البرنامج الرئيسي بنجاح.")
    return True

def create_activation_generator_installer():
    """
    إنشاء برنامج تثبيت لمولد أكواد التفعيل
    
    العائد:
        bool: True إذا تم الإنشاء بنجاح، False في حالة حدوث خطأ
    """
    print("\n=== جاري إنشاء برنامج تثبيت مولد أكواد التفعيل ===\n")
    
    # إنشاء مجلد للمثبت إذا لم يكن موجوداً
    if not os.path.exists("installer"):
        os.makedirs("installer")
    
    # البحث عن مسار Inno Setup Compiler
    inno_setup_path = None
    possible_paths = [
        r"C:\Program Files (x86)\Inno Setup 6\ISCC.exe",
        r"C:\Program Files\Inno Setup 6\ISCC.exe",
        r"C:\Program Files (x86)\Inno Setup 5\ISCC.exe",
        r"C:\Program Files\Inno Setup 5\ISCC.exe"
    ]
    
    for path in possible_paths:
        if os.path.exists(path):
            inno_setup_path = path
            break
    
    if inno_setup_path is None:
        print("لم يتم العثور على Inno Setup Compiler. يرجى تثبيته أولاً.")
        print("يمكنك تنزيله من: https://jrsoftware.org/isdl.php")
        return False
    
    # تنفيذ Inno Setup Compiler
    if not run_command(f'"{inno_setup_path}" activation_generator_setup.iss'):
        return False
    
    print("\nتم إنشاء برنامج تثبيت مولد أكواد التفعيل بنجاح.")
    return True

def main():
    """
    الدالة الرئيسية
    """
    start_time = time.time()
    print("=== بدء عملية بناء برامج التثبيت المنفصلة ===\n")
    
    # إنشاء الأيقونات
    if not create_icons():
        print("حدث خطأ أثناء إنشاء الأيقونات.")
        return
    
    # بناء البرنامج الرئيسي
    if not build_main_executable():
        print("حدث خطأ أثناء بناء البرنامج الرئيسي.")
        return
    
    # بناء مولد أكواد التفعيل
    if not build_activation_generator():
        print("حدث خطأ أثناء بناء مولد أكواد التفعيل.")
        return
    
    # إنشاء مجلدات التوزيع
    if not create_distribution_folders():
        print("حدث خطأ أثناء إنشاء مجلدات التوزيع.")
        return
    
    # إنشاء برنامج تثبيت البرنامج الرئيسي
    if not create_main_program_installer():
        print("حدث خطأ أثناء إنشاء برنامج تثبيت البرنامج الرئيسي.")
        return
    
    # إنشاء برنامج تثبيت مولد أكواد التفعيل
    if not create_activation_generator_installer():
        print("حدث خطأ أثناء إنشاء برنامج تثبيت مولد أكواد التفعيل.")
        return
    
    end_time = time.time()
    elapsed_time = end_time - start_time
    
    print("\n=== تم الانتهاء من عملية بناء برامج التثبيت المنفصلة بنجاح ===")
    print(f"الوقت المستغرق: {elapsed_time:.2f} ثانية")
    print("برامج التثبيت موجودة في مجلد 'installer':")
    print("1. DiamondSalesSetup.exe - برنامج تثبيت البرنامج الرئيسي")
    print("2. ActivationGeneratorSetup.exe - برنامج تثبيت مولد أكواد التفعيل")

if __name__ == "__main__":
    main()
