#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
سكريبت لنسخ الملفات إلى مجلد منفصل للمثبت
"""

import os
import shutil
from datetime import datetime

def create_installer_package():
    """إنشاء حزمة منفصلة للمثبت"""
    print("📦 إنشاء حزمة المثبت...")
    
    # إنشاء مجلد الحزمة
    package_dir = "installer_package"
    if os.path.exists(package_dir):
        shutil.rmtree(package_dir)
    os.makedirs(package_dir)
    
    # نسخ مجلد dist بالكامل
    if os.path.exists("dist/DiamondSales"):
        shutil.copytree("dist/DiamondSales", os.path.join(package_dir, "DiamondSales"))
        print("  ✅ تم نسخ مجلد DiamondSales")
        
        # إعادة تسمية الملف التنفيذي إذا كان يحتوي على مسافة
        old_exe_path = os.path.join(package_dir, "DiamondSales", "Diamond Sales.exe")
        new_exe_path = os.path.join(package_dir, "DiamondSales", "DiamondSales.exe")
        if os.path.exists(old_exe_path):
            os.rename(old_exe_path, new_exe_path)
            print("  ✅ تم إعادة تسمية الملف التنفيذي")
    else:
        print("  ❌ مجلد dist/DiamondSales غير موجود")
        return False
    
    # نسخ مجلد assets
    if os.path.exists("assets"):
        shutil.copytree("assets", os.path.join(package_dir, "assets"))
        print("  ✅ تم نسخ مجلد assets")
    
    # إنشاء سكريبت مثبت جديد
    installer_script = f'''[Setup]
AppName=Diamond Sales
AppVersion=1.30
AppPublisher=Diamond Sales Company
DefaultDirName={{autopf}}\\Diamond Sales
DefaultGroupName=Diamond Sales
OutputDir=.
OutputBaseFilename=DiamondSalesSetup_v1.30
SetupIconFile=assets\\diamond_icon.ico
Compression=lzma
SolidCompression=yes
WizardStyle=modern
ArchitecturesAllowed=x64
ArchitecturesInstallIn64BitMode=x64

[Languages]
Name: "arabic"; MessagesFile: "compiler:Languages\\Arabic.isl"
Name: "english"; MessagesFile: "compiler:Default.isl"

[Tasks]
Name: "desktopicon"; Description: "{{cm:CreateDesktopIcon}}"; GroupDescription: "{{cm:AdditionalIcons}}"; Flags: unchecked

[Files]
Source: "DiamondSales\\*"; DestDir: "{{app}}"; Flags: ignoreversion recursesubdirs createallsubdirs

[Icons]
Name: "{{group}}\\Diamond Sales"; Filename: "{{app}}\\DiamondSales.exe"
Name: "{{group}}\\{{cm:UninstallProgram,Diamond Sales}}"; Filename: "{{uninstallexe}}"
Name: "{{autodesktop}}\\Diamond Sales"; Filename: "{{app}}\\DiamondSales.exe"; Tasks: desktopicon

[Run]
Filename: "{{app}}\\DiamondSales.exe"; Description: "{{cm:LaunchProgram,Diamond Sales}}"; Flags: nowait postinstall skipifsilent
'''
    
    # حفظ السكريبت الجديد
    script_path = os.path.join(package_dir, "DiamondSales_Installer.iss")
    with open(script_path, "w", encoding="utf-8") as f:
        f.write(installer_script)
    
    print("  ✅ تم إنشاء سكريبت المثبت الجديد")
    
    # إنشاء ملف تعليمات
    instructions = f"""
===============================================
تعليمات إنشاء المثبت - Diamond Sales v1.30
===============================================

📅 تاريخ الإنشاء: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

📋 خطوات إنشاء المثبت:

1️⃣ تثبيت Inno Setup:
   - حمل من: https://jrsoftware.org/isinfo.php
   - ثبت البرنامج على جهازك

2️⃣ إنشاء المثبت:
   - افتح Inno Setup Compiler
   - افتح ملف: DiamondSales_Installer.iss
   - اضغط F9 أو Build > Compile
   - انتظر حتى انتهاء العملية

3️⃣ النتيجة:
   - ستجد ملف: DiamondSalesSetup_v1.30.exe
   - في نفس المجلد
   - حجم الملف: ~50-100 MB

📁 محتويات الحزمة:
- DiamondSales/ (مجلد البرنامج الكامل)
- assets/ (الأيقونات والصور)
- DiamondSales_Installer.iss (سكريبت المثبت)

⚠️ ملاحظات مهمة:
- تأكد من تشغيل Inno Setup من هذا المجلد
- لا تغير أسماء المجلدات أو الملفات
- تأكد من وجود جميع الملفات قبل البناء

🎯 بعد إنشاء المثبت:
- اختبر المثبت على جهاز آخر
- تأكد من عمل البرنامج بشكل صحيح
- وزع المثبت على المستخدمين

===============================================
"""
    
    instructions_path = os.path.join(package_dir, "تعليمات_المثبت.txt")
    with open(instructions_path, "w", encoding="utf-8") as f:
        f.write(instructions)
    
    print("  ✅ تم إنشاء ملف التعليمات")
    
    print(f"\n🎉 تم إنشاء حزمة المثبت في مجلد: {package_dir}")
    print("📋 اتبع التعليمات في ملف: تعليمات_المثبت.txt")
    
    return True

def main():
    """الدالة الرئيسية"""
    print("=" * 60)
    print("📦 إنشاء حزمة مثبت Diamond Sales")
    print("=" * 60)
    
    if create_installer_package():
        print("\n✅ تم إنشاء الحزمة بنجاح!")
        print("📁 المجلد: installer_package/")
        print("📋 اقرأ ملف: تعليمات_المثبت.txt")
    else:
        print("\n❌ فشل في إنشاء الحزمة")
    
    print("\n" + "=" * 60)

if __name__ == "__main__":
    main()
