"""
فئة النافذة الأساسية للتطبيق
توفر هذه الفئة وظائف مشتركة لجميع نوافذ التطبيق
"""

from PyQt6.QtWidgets import QWidget, QMainWindow, QDialog
from ui_utils import center_window

class BaseWindow:
    """
    فئة أساسية لجميع نوافذ التطبيق
    توفر وظائف مشتركة مثل توسيط النافذة
    """
    
    def __init__(self):
        """
        تهيئة النافذة الأساسية
        """
        # سيتم استدعاء هذه الدالة من قبل الفئات الوارثة
        pass
    
    def show(self):
        """
        عرض النافذة مع توسيطها في الشاشة
        """
        # توسيط النافذة
        center_window(self)
        
        # استدعاء دالة العرض الأصلية
        self._original_show()

class BaseQWidget(QWidget, BaseWindow):
    """
    فئة أساسية لنوافذ QWidget
    """
    
    def __init__(self, *args, **kwargs):
        """
        تهيئة النافذة
        """
        QWidget.__init__(self, *args, **kwargs)
        BaseWindow.__init__(self)
        
        # حفظ دالة العرض الأصلية
        self._original_show = super().show

class BaseQMainWindow(QMainWindow, BaseWindow):
    """
    فئة أساسية لنوافذ QMainWindow
    """
    
    def __init__(self, *args, **kwargs):
        """
        تهيئة النافذة
        """
        QMainWindow.__init__(self, *args, **kwargs)
        BaseWindow.__init__(self)
        
        # حفظ دالة العرض الأصلية
        self._original_show = super().show

class BaseQDialog(QDialog, BaseWindow):
    """
    فئة أساسية لنوافذ QDialog
    """
    
    def __init__(self, *args, **kwargs):
        """
        تهيئة النافذة
        """
        QDialog.__init__(self, *args, **kwargs)
        BaseWindow.__init__(self)
        
        # حفظ دالة العرض الأصلية
        self._original_show = super().show
