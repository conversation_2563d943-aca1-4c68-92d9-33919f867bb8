.. This data file has been placed in the public domain.
.. Derived from the Unicode character mappings available from
   <http://www.w3.org/2003/entities/xml/>.
   Processed by unicode2rstsubs.py, part of Docutils:
   <https://docutils.sourceforge.io>.

.. |acute| unicode:: U+000B4 .. ACUTE ACCENT
.. |breve| unicode:: U+002D8 .. BREVE
.. |caron| unicode:: U+002C7 .. CARON
.. |cedil| unicode:: U+000B8 .. CEDILLA
.. |circ|  unicode:: U+002C6 .. MODIFIER LETTER CIRCUMFLEX ACCENT
.. |dblac| unicode:: U+002DD .. DOUBLE ACUTE ACCENT
.. |die|   unicode:: U+000A8 .. DIAERESIS
.. |dot|   unicode:: U+002D9 .. DOT ABOVE
.. |grave| unicode:: U+00060 .. GRAVE ACCENT
.. |macr|  unicode:: U+000AF .. MACRON
.. |ogon|  unicode:: U+002DB .. OGONEK
.. |ring|  unicode:: U+002DA .. RING ABOVE
.. |tilde| unicode:: U+002DC .. SMALL TILDE
.. |uml|   unicode:: U+000A8 .. DIAERESIS
