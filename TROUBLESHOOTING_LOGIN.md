# أدوات إعادة تعيين محاولات تسجيل الدخول

## مقدمة
هذه الأدوات تساعد في حل مشكلة "تم تجاوز الحد الأقصى لمحاولات تسجيل الدخول" التي قد تظهر بعد تثبيت نظام مبيعات الماس.

**ملاحظة هامة:** تم تحديث الأدوات لحل مشكلة "Failed to execute script due to unhandled exception: lost sys.stdin" التي كانت تظهر أحياناً عند تشغيلها.

## الملفات التنفيذية

### 1. الأداة السريعة (quick_reset_admin_login.exe)
هذه الأداة تقوم بإعادة تعيين عدد محاولات تسجيل الدخول الفاشلة للمستخدم الافتراضي (admin) فقط.
- **الاستخدام**: انقر نقرًا مزدوجًا على الملف لتشغيله، وسيتم إعادة تعيين عداد المحاولات الفاشلة تلقائيًا.
- **مناسبة لـ**: المستخدمين الذين يواجهون المشكلة مباشرة بعد التثبيت ويريدون حلها بسرعة.

### 2. الأداة المتقدمة (reset_login_attempts.exe)
هذه الأداة توفر واجهة لعرض جميع المستخدمين وإعادة تعيين محاولات تسجيل الدخول الفاشلة لمستخدم محدد أو لجميع المستخدمين.
- **الاستخدام**: انقر نقرًا مزدوجًا على الملف، وستظهر قائمة بالمستخدمين وعدد محاولات تسجيل الدخول الفاشلة لكل منهم. ثم اختر من الخيارات المتاحة.
- **مناسبة لـ**: المشرفين الذين يريدون التحكم في محاولات تسجيل الدخول الفاشلة لمستخدمين محددين.

## تعليمات الاستخدام

1. **لإعادة تعيين محاولات المستخدم الافتراضي (admin) بسرعة**:
   - قم بتشغيل `quick_reset_admin_login.exe`
   - ستظهر رسالة تأكيد بنجاح العملية

2. **لإدارة محاولات تسجيل الدخول لجميع المستخدمين**:
   - قم بتشغيل `reset_login_attempts.exe`
   - ستظهر قائمة بجميع المستخدمين وعدد محاولات تسجيل الدخول الفاشلة لكل منهم
   - اختر إحدى العمليات التالية:
     - إعادة تعيين محاولات تسجيل الدخول الفاشلة لمستخدم محدد
     - إعادة تعيين محاولات تسجيل الدخول الفاشلة لجميع المستخدمين
     - الخروج

## ملاحظات مهمة

- تأكد من وجود ملف قاعدة البيانات `diamond_sales.db` في نفس المجلد مع هذه الأدوات
- في حالة وجود أي خطأ، يتم تسجيله في ملف `error_log.txt`
- هذه الأدوات لا تغير كلمات المرور، بل تقوم فقط بإعادة تعيين عداد محاولات تسجيل الدخول الفاشلة

### 3. أداة استعادة كلمة المرور (reset_password.exe)
هذه الأداة تمكن المستخدمين من استعادة كلمة المرور الخاصة بهم عندما ينسونها.
- **الاستخدام**: انقر نقرًا مزدوجًا على الملف، وستظهر واجهة رسومية تتيح اختيار اسم المستخدم وإدخال البريد الإلكتروني للتحقق، ثم إعادة تعيين كلمة المرور.
- **مناسبة لـ**: جميع المستخدمين الذين نسوا كلمات مرورهم.
- **الميزات**:
  - اختيار المستخدم من قائمة منسدلة
  - التحقق عبر البريد الإلكتروني
  - توليد وإرسال رمز التحقق
  - إمكانية توليد كلمات مرور قوية تلقائيًا
  - التحقق من معايير قوة كلمة المرور الجديدة

## استكشاف الأخطاء وإصلاحها

1. **في حالة ظهور رسالة "لا يمكن العثور على قاعدة البيانات"**:
   - تأكد من وجود ملف `diamond_sales.db` في نفس المجلد مع الأداة
   - يمكنك نسخ الأداة إلى المجلد الذي يحتوي على قاعدة البيانات

2. **في حالة ظهور رسالة "Failed to execute script due to unhandled exception: lost sys.stdin"**:
   - تم إصلاح هذا الخطأ في النسخة الجديدة من الأدوات
   - في حال استمر الخطأ، تأكد من استخدام أحدث نسخة من الأدوات الموجودة في مجلد `tools`

3. **في حالة عدم وصول رمز التحقق إلى البريد الإلكتروني**:
   - تأكد من أن البريد الإلكتروني المدخل هو نفسه المسجل في النظام
   - تحقق من مجلد البريد غير الهام (Spam/Junk)
   - تأكد من اتصالك بالإنترنت

4. **في حالة استمرار مشكلة تسجيل الدخول بعد استخدام الأداة**:
   - جرب استخدام أداة `reset_admin_password.py` لإعادة تعيين كلمة مرور المستخدم الافتراضي
   - تأكد من أن عداد محاولات تسجيل الدخول الفاشلة قد تم إعادة تعيينه باستخدام `reset_login_attempts.exe`
   - تأكد من استخدام اسم المستخدم وكلمة المرور الصحيحين
