from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTabWidget,
                            QPushButton, QLabel, QLineEdit, QComboBox,
                            QTableWidget, QTableWidgetItem, QFormLayout,
                            QHeaderView, QDialog, QDialogButtonBox, QMessageBox,
                            QSpacerItem, QSizePolicy)
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QFont
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from datetime import datetime
import traceback

from database import Customer, Sale, Receipt, صندوق_النقدية, حركة_نقدية
from ui_utils import style_button, style_dialog_buttons
from translations import get_translation as _

class CustomersScreen(QWidget):
    def __init__(self, user):
        super().__init__()
        self.user = user
        self.init_ui()
        self.load_customers_data()

    def init_ui(self):
        self.setWindowTitle(_("customers_title", "نظام إدارة مبيعات الألماس - إدارة العملاء"))
        self.setGeometry(100, 100, 900, 600)
        self.setLayoutDirection(Qt.LayoutDirection.RightToLeft)

        # Create main layout
        main_layout = QVBoxLayout(self)

        # Create tab widget
        self.tab_widget = QTabWidget()

        # Create tabs
        self.customers_tab = QWidget()
        self.new_customer_tab = QWidget()

        # Setup tabs
        self.setup_customers_tab()
        self.setup_new_customer_tab()

        # Add tabs to tab widget
        self.tab_widget.addTab(self.customers_tab, "قائمة العملاء")
        self.tab_widget.addTab(self.new_customer_tab, "إضافة عميل جديد")

        # Add tab widget to main layout
        main_layout.addWidget(self.tab_widget)

    def setup_customers_tab(self):
        # Create layout for customers tab
        layout = QVBoxLayout(self.customers_tab)        # Create search controls
        search_layout = QHBoxLayout()
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("ابحث باسم العميل أو رقم الهوية")
        
        search_button = QPushButton("بحث")
        search_button.clicked.connect(self.search_customers)
        style_button(search_button, "info", min_width=120, min_height=35)

        search_layout.addWidget(QLabel("بحث:"))
        search_layout.addWidget(self.search_input)
        search_layout.addWidget(search_button)

        # Create customers table
        self.customers_table = QTableWidget()
        self.customers_table.setColumnCount(6)
        self.customers_table.setHorizontalHeaderLabels([
            "رقم العميل", "اسم العميل", "رقم الهوية", "رقم الهاتف", "العنوان", "الرصيد الافتتاحي"
        ])
        header = self.customers_table.horizontalHeader()
        for i in range(6):
            header.setSectionResizeMode(i, QHeaderView.ResizeMode.Stretch)        # Create action buttons
        action_layout = QHBoxLayout()
        action_layout.setSpacing(20)  # زيادة المسافة بين الأزرار
        action_layout.setContentsMargins(5, 5, 5, 15)  # هوامش لمجموعة الأزرار

        view_button = QPushButton("عرض التفاصيل")
        view_button.clicked.connect(self.view_customer_details)
        style_button(view_button, "info")

        edit_button = QPushButton("تعديل")
        edit_button.clicked.connect(self.edit_customer)
        style_button(edit_button, "edit")

        delete_button = QPushButton("حذف")
        delete_button.clicked.connect(self.delete_customer)
        style_button(delete_button, "delete")

        view_sales_button = QPushButton("عرض المبيعات")
        view_sales_button.clicked.connect(self.view_customer_sales)
        style_button(view_sales_button, "success")

        action_layout.addWidget(view_button)
        action_layout.addWidget(edit_button)
        action_layout.addWidget(delete_button)
        action_layout.addWidget(view_sales_button)

        # Add widgets to layout
        layout.addLayout(search_layout)
        layout.addWidget(self.customers_table)
        layout.addLayout(action_layout)

    def setup_new_customer_tab(self):
        # Create layout for new customer tab
        layout = QVBoxLayout(self.new_customer_tab)

        # Create form layout for customer details
        form_layout = QFormLayout()

        # Name input
        self.name_input = QLineEdit()

        # ID Number input
        self.id_number_input = QLineEdit()

        # Phone input
        self.phone_input = QLineEdit()        # Address input
        self.address_input = QLineEdit()

        # Opening Balance input
        self.opening_balance_input = QLineEdit()
        self.opening_balance_input.setPlaceholderText("0.00")
        self.opening_balance_input.textChanged.connect(self.calculate_opening_balance_sar)
        
        # Opening Balance in SAR (read-only)
        self.opening_balance_sar_input = QLineEdit()
        self.opening_balance_sar_input.setPlaceholderText("0.00")
        self.opening_balance_sar_input.setReadOnly(True)
        self.opening_balance_sar_input.setStyleSheet("background-color: #f0f0f0;")

        # Add fields to form layout
        form_layout.addRow("اسم العميل:", self.name_input)
        form_layout.addRow("رقم الهوية:", self.id_number_input)
        form_layout.addRow("رقم الهاتف:", self.phone_input)
        form_layout.addRow("العنوان:", self.address_input)
        form_layout.addRow("الرصيد الافتتاحي (دولار):", self.opening_balance_input)
        form_layout.addRow("الرصيد الافتتاحي (ريال):", self.opening_balance_sar_input)

        # Add spacer
        layout.addSpacerItem(QSpacerItem(20, 40, QSizePolicy.Policy.Minimum, QSizePolicy.Policy.Expanding))        # Create button to save customer
        save_button = QPushButton("حفظ بيانات العميل")
        save_button.clicked.connect(self.save_customer)
        style_button(save_button, "add", min_width=200, min_height=45)

        # Add widgets to layout
        layout.addLayout(form_layout)
        layout.addSpacerItem(QSpacerItem(20, 20, QSizePolicy.Policy.Minimum, QSizePolicy.Policy.Expanding))
        layout.addWidget(save_button)

    def calculate_opening_balance_sar(self):
        """حساب المقابل بالريال للرصيد الافتتاحي"""
        try:
            usd_amount = float(self.opening_balance_input.text() or '0')
            sar_amount = usd_amount * 3.75  # سعر الصرف الثابت
            self.opening_balance_sar_input.setText(f"{sar_amount:.2f}")
        except ValueError:
            self.opening_balance_sar_input.setText("0.00")

    def load_customers_data(self):
        try:
            # Connect to database
            engine = create_engine('sqlite:///diamond_sales.db')
            Session = sessionmaker(bind=engine)
            session = Session()

            try:
                # Get all customers
                customers = session.query(Customer).all()
                
                # Update table
                self.customers_table.setRowCount(len(customers))
                
                for i, customer in enumerate(customers):
                    self.customers_table.setItem(i, 0, QTableWidgetItem(str(customer.id)))
                    self.customers_table.setItem(i, 1, QTableWidgetItem(customer.name))
                    self.customers_table.setItem(i, 2, QTableWidgetItem(customer.id_number))
                    self.customers_table.setItem(i, 3, QTableWidgetItem(customer.phone))
                    self.customers_table.setItem(i, 4, QTableWidgetItem(customer.address))
                    self.customers_table.setItem(i, 5, QTableWidgetItem(f"{customer.opening_balance:.2f}"))
                    
            except Exception as e:
                QMessageBox.critical(self, "خطأ",
                    f"حدث خطأ أثناء تحميل بيانات العملاء: {str(e)}\n\nالتفاصيل الكاملة:\n{traceback.format_exc()}")
            finally:
                session.close()
        except Exception as e:
            QMessageBox.critical(self, "خطأ",
                f"خطأ في الاتصال بقاعدة البيانات: {str(e)}\n\nالتفاصيل الكاملة:\n{traceback.format_exc()}")

    def search_customers(self):
        # Get search text
        search_text = self.search_input.text().strip()

        if not search_text:
            # If search is empty, reload all data
            self.load_customers_data()
            return

        # Connect to database
        engine = create_engine('sqlite:///diamond_sales.db')
        Session = sessionmaker(bind=engine)
        session = Session()

        try:
            # Search for customers
            customers = session.query(Customer).filter(
                (Customer.name.like(f'%{search_text}%')) |
                (Customer.id_number.like(f'%{search_text}%'))
            ).all()

            # Update table
            self.customers_table.setRowCount(len(customers))

            for i, customer in enumerate(customers):
                self.customers_table.setItem(i, 0, QTableWidgetItem(str(customer.id)))
                self.customers_table.setItem(i, 1, QTableWidgetItem(customer.name))
                self.customers_table.setItem(i, 2, QTableWidgetItem(customer.id_number))
                self.customers_table.setItem(i, 3, QTableWidgetItem(customer.phone))
                self.customers_table.setItem(i, 4, QTableWidgetItem(customer.address))

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء البحث: {str(e)}")
        finally:
            session.close()

    def save_customer(self):
        # Get form data
        name = self.name_input.text().strip()
        id_number = self.id_number_input.text().strip()
        phone = self.phone_input.text().strip()
        address = self.address_input.text().strip()
        
        # Get and validate opening balance
        try:
            opening_balance = float(self.opening_balance_input.text().strip() or '0')
        except ValueError:
            QMessageBox.warning(self, "خطأ", "الرجاء إدخال رقم صحيح للرصيد الافتتاحي")
            return

        # Validate form data
        if not name:
            QMessageBox.warning(self, "خطأ", "الرجاء إدخال اسم العميل")
            return

        # Connect to database
        engine = create_engine('sqlite:///diamond_sales.db')
        Session = sessionmaker(bind=engine)
        session = Session()

        try:
            # Create new customer
            customer = Customer(
                name=name,
                id_number=id_number,
                phone=phone,
                address=address,
                opening_balance=opening_balance
            )
            session.add(customer)
            
            # تم إزالة إضافة الرصيد الافتتاحي إلى صندوق النقدية
            # الرصيد الافتتاحي يُحفظ فقط كمعلومة للعميل ولا يؤثر على صندوق النقدية

            session.commit()
            QMessageBox.information(self, "نجاح", "تم حفظ بيانات العميل بنجاح")

            # Clear form
            self.name_input.clear()
            self.id_number_input.clear()
            self.phone_input.clear()
            self.address_input.clear()
            self.opening_balance_input.clear()
            self.opening_balance_sar_input.clear()

            # Refresh customers table
            self.load_customers_data()

            # Switch to customers tab
            self.tab_widget.setCurrentIndex(0)

        except Exception as e:
            session.rollback()
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء حفظ بيانات العميل: {str(e)}")
        finally:
            session.close()

    def view_customer_details(self):
        # Get selected row
        selected_row = self.customers_table.currentRow()

        if selected_row < 0:
            QMessageBox.warning(self, "تحذير", "الرجاء اختيار عميل لعرض بياناته")
            return

        # Get customer ID
        customer_id = int(self.customers_table.item(selected_row, 0).text())

        # Connect to database
        engine = create_engine('sqlite:///diamond_sales.db')
        Session = sessionmaker(bind=engine)
        session = Session()

        try:
            # Get customer
            customer = session.query(Customer).filter_by(id=customer_id).first()

            if not customer:
                QMessageBox.warning(self, "تحذير", "لم يتم العثور على العميل")
                return

            # Show details in a dialog
            dialog = QDialog(self)
            dialog.setWindowTitle(f"بيانات العميل: {customer.name}")
            dialog.setMinimumWidth(400)

            layout = QVBoxLayout(dialog)

            # Create labels with customer information
            info_text = f"""
            <h3>بيانات العميل</h3>
            <p><b>الاسم:</b> {customer.name}</p>
            <p><b>رقم الهوية:</b> {customer.id_number or 'غير محدد'}</p>
            <p><b>رقم الهاتف:</b> {customer.phone or 'غير محدد'}</p>
            <p><b>العنوان:</b> {customer.address or 'غير محدد'}</p>
            """

            info_label = QLabel(info_text)
            info_label.setTextFormat(Qt.TextFormat.RichText)
            layout.addWidget(info_label)

            # Sales summary
            sales = session.query(Sale).filter_by(customer_id=customer_id).all()

            # Get receipts without reference
            receipts_without_ref = session.query(Receipt).filter_by(
                customer_id=customer_id,
                sale_id=None,
                receipt_type="CashIn"
            ).all()

            # Calculate total receipts without reference
            total_receipts_usd = sum(receipt.amount_usd for receipt in receipts_without_ref)
            total_receipts_sar = sum(receipt.amount_sar for receipt in receipts_without_ref)

            if sales:
                # Calculate total sales
                total_carats = sum(sale.carat_weight for sale in sales)
                total_amount_usd = sum(sale.total_price_usd for sale in sales)
                total_amount_sar = sum(sale.total_price_sar for sale in sales)
                total_paid = sum(sale.amount_paid for sale in sales)
                total_due = sum(sale.amount_due for sale in sales)

                # Add sales summary
                sales_text = f"""
                <h3>ملخص المبيعات</h3>
                <p><b>عدد المبيعات:</b> {len(sales)}</p>
                <p><b>إجمالي القيراط:</b> {total_carats:.2f}</p>
                <p><b>إجمالي المبيعات بالدولار:</b> {total_amount_usd:.2f} $</p>
                <p><b>إجمالي المبيعات بالريال:</b> {total_amount_sar:.2f} ريال</p>
                <p><b>إجمالي المبالغ المدفوعة:</b> {total_paid:.2f} $</p>
                <p><b>إجمالي المبالغ المستحقة:</b> {total_due:.2f} $</p>
                """

                sales_label = QLabel(sales_text)
                sales_label.setTextFormat(Qt.TextFormat.RichText)
                layout.addWidget(sales_label)

            # Add receipts without reference summary
            if receipts_without_ref:
                receipts_text = f"""
                <h3>ملخص السدادات بدون مرجع</h3>
                <p><b>عدد السدادات:</b> {len(receipts_without_ref)}</p>
                <p><b>إجمالي السدادات بالدولار:</b> {total_receipts_usd:.2f} $</p>
                <p><b>إجمالي السدادات بالريال:</b> {total_receipts_sar:.2f} ريال</p>
                """

                receipts_label = QLabel(receipts_text)
                receipts_label.setTextFormat(Qt.TextFormat.RichText)
                layout.addWidget(receipts_label)

            # Add a close button
            buttons = QDialogButtonBox(QDialogButtonBox.StandardButton.Close)
            buttons.rejected.connect(dialog.reject)
            layout.addWidget(buttons)

            # Show the dialog
            dialog.exec()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء عرض بيانات العميل: {str(e)}")
        finally:
            session.close()

    def edit_customer(self):
        # Get selected row
        selected_row = self.customers_table.currentRow()

        if selected_row < 0:
            QMessageBox.warning(self, "تحذير", "الرجاء اختيار عميل لتعديل بياناته")
            return

        # Get customer ID
        customer_id = int(self.customers_table.item(selected_row, 0).text())

        # Connect to database
        engine = create_engine('sqlite:///diamond_sales.db')
        Session = sessionmaker(bind=engine)
        session = Session()

        try:
            # Get customer
            customer = session.query(Customer).filter_by(id=customer_id).first()

            if not customer:
                QMessageBox.warning(self, "تحذير", "لم يتم العثور على العميل")
                return

            # Create dialog for editing
            dialog = QDialog(self)
            dialog.setWindowTitle(f"تعديل بيانات العميل: {customer.name}")
            dialog.setMinimumWidth(400)

            layout = QVBoxLayout(dialog)
            form_layout = QFormLayout()

            # Create input fields
            name_input = QLineEdit(customer.name)
            id_number_input = QLineEdit(customer.id_number or "")
            phone_input = QLineEdit(customer.phone or "")
            address_input = QLineEdit(customer.address or "")
            opening_balance_input = QLineEdit(str(customer.opening_balance or 0.0))

            # Add fields to form layout
            form_layout.addRow("اسم العميل:", name_input)
            form_layout.addRow("رقم الهوية:", id_number_input)
            form_layout.addRow("رقم الهاتف:", phone_input)
            form_layout.addRow("العنوان:", address_input)
            form_layout.addRow("الرصيد الافتتاحي:", opening_balance_input)

            layout.addLayout(form_layout)

            # Add buttons
            button_box = QDialogButtonBox(QDialogButtonBox.StandardButton.Save | QDialogButtonBox.StandardButton.Cancel)
            button_box.button(QDialogButtonBox.StandardButton.Save).setText("حفظ")
            button_box.button(QDialogButtonBox.StandardButton.Cancel).setText("إلغاء")
            button_box.accepted.connect(dialog.accept)
            button_box.rejected.connect(dialog.reject)
            style_dialog_buttons(button_box)
            layout.addWidget(button_box)

            # Execute dialog
            if dialog.exec() == QDialog.DialogCode.Accepted:
                # Validate opening balance
                try:
                    new_opening_balance = float(opening_balance_input.text().strip() or '0')
                except ValueError:
                    QMessageBox.warning(self, "خطأ", "الرجاء إدخال رقم صحيح للرصيد الافتتاحي")
                    return
                
                # Update customer information
                customer.name = name_input.text().strip()
                customer.id_number = id_number_input.text().strip()
                customer.phone = phone_input.text().strip()
                customer.address = address_input.text().strip()
                customer.opening_balance = new_opening_balance

                # Save changes
                session.commit()

                # Show success message
                QMessageBox.information(self, "نجاح", "تم تعديل بيانات العميل بنجاح")

                # Reload customers data
                self.load_customers_data()

        except Exception as e:
            session.rollback()
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تعديل بيانات العميل: {str(e)}")
        finally:
            session.close()

    def delete_customer(self):
        # Get selected row
        selected_row = self.customers_table.currentRow()

        if selected_row < 0:
            QMessageBox.warning(self, "تحذير", "الرجاء اختيار عميل لحذفه")
            return

        # Get customer ID
        customer_id = int(self.customers_table.item(selected_row, 0).text())
        customer_name = self.customers_table.item(selected_row, 1).text()

        # Connect to database
        engine = create_engine('sqlite:///diamond_sales.db')
        Session = sessionmaker(bind=engine)
        session = Session()

        try:
            # Check if customer has sales
            sales_count = session.query(Sale).filter_by(customer_id=customer_id).count()

            if sales_count > 0:
                QMessageBox.warning(self, "تحذير",
                                 f"لا يمكن حذف العميل '{customer_name}' لأنه مرتبط بـ {sales_count} عملية بيع.")
                return

            # Confirm deletion
            reply = QMessageBox.question(self, "تأكيد",
                                   f"هل أنت متأكد من رغبتك في حذف العميل '{customer_name}'؟",
                                   QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No)

            if reply == QMessageBox.StandardButton.Yes:
                # Delete customer
                session.query(Customer).filter_by(id=customer_id).delete()

                # Commit changes
                session.commit()

                # Show success message
                QMessageBox.information(self, "نجاح", "تم حذف العميل بنجاح")

                # Reload customers data
                self.load_customers_data()

        except Exception as e:
            session.rollback()
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء حذف العميل: {str(e)}")
        finally:
            session.close()

    def view_customer_sales(self):
        # Get selected row
        selected_row = self.customers_table.currentRow()

        if selected_row < 0:
            QMessageBox.warning(self, "تحذير", "الرجاء اختيار عميل لعرض مبيعاته")
            return

        # Get customer ID
        customer_id = int(self.customers_table.item(selected_row, 0).text())
        customer_name = self.customers_table.item(selected_row, 1).text()

        # Create dialog for sales
        dialog = QDialog(self)
        dialog.setWindowTitle(f"مبيعات العميل: {customer_name}")
        dialog.setMinimumWidth(800)
        dialog.setMinimumHeight(500)

        layout = QVBoxLayout(dialog)

        # Add title
        title = QLabel(f"مبيعات العميل: <b>{customer_name}</b>")
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title)

        # Create sales table
        sales_table = QTableWidget()
        sales_table.setColumnCount(8)
        sales_table.setHorizontalHeaderLabels([
            "رقم المبيعات", "نوع الألماس", "الوزن بالقيراط",
            "السعر/قيراط ($)", "الإجمالي ($)", "الإجمالي (ريال)",
            "تاريخ البيع", "المبلغ المتبقي"
        ])
        header = sales_table.horizontalHeader()
        for i in range(8):
            header.setSectionResizeMode(i, QHeaderView.ResizeMode.Stretch)

        layout.addWidget(sales_table)

        # Add button to view receipts without reference
        receipts_button = QPushButton("عرض السدادات بدون مرجع")
        receipts_button.clicked.connect(lambda: self.view_customer_receipts(customer_id, customer_name))
        layout.addWidget(receipts_button)

        # Add a close button
        buttons = QDialogButtonBox(QDialogButtonBox.StandardButton.Close)
        buttons.rejected.connect(dialog.reject)
        layout.addWidget(buttons)

        # Connect to database
        engine = create_engine('sqlite:///diamond_sales.db')
        Session = sessionmaker(bind=engine)
        session = Session()

        try:
            # Get sales for customer
            sales = session.query(Sale).filter_by(customer_id=customer_id).all()

            # Update table
            sales_table.setRowCount(len(sales))

            for i, sale in enumerate(sales):
                sales_table.setItem(i, 0, QTableWidgetItem(str(sale.id)))
                sales_table.setItem(i, 1, QTableWidgetItem(sale.diamond_type))
                sales_table.setItem(i, 2, QTableWidgetItem(f"{sale.carat_weight:.2f}"))
                sales_table.setItem(i, 3, QTableWidgetItem(f"{sale.price_per_carat_usd:.2f}"))
                sales_table.setItem(i, 4, QTableWidgetItem(f"{sale.total_price_usd:.2f}"))
                sales_table.setItem(i, 5, QTableWidgetItem(f"{sale.total_price_sar:.2f}"))

                # Format date
                date_str = sale.sale_date.strftime("%d/%m/%Y") if sale.sale_date else ""
                sales_table.setItem(i, 6, QTableWidgetItem(date_str))

                sales_table.setItem(i, 7, QTableWidgetItem(f"{sale.amount_due:.2f}"))

            # If no sales, show message
            if not sales:
                sales_table.setRowCount(0)
                QMessageBox.information(dialog, "معلومات", "لا توجد مبيعات لهذا العميل")

            # Show the dialog
            dialog.exec()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء عرض مبيعات العميل: {str(e)}")
        finally:
            session.close()

    def view_customer_receipts(self, customer_id, customer_name):
        """عرض سندات القبض بدون مرجع للعميل"""
        # Create dialog for receipts
        dialog = QDialog(self)
        dialog.setWindowTitle(f"سدادات العميل بدون مرجع: {customer_name}")
        dialog.setMinimumWidth(800)
        dialog.setMinimumHeight(500)

        layout = QVBoxLayout(dialog)

        # Add title
        title = QLabel(f"سدادات العميل بدون مرجع: <b>{customer_name}</b>")
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title)

        # Create receipts table
        receipts_table = QTableWidget()
        receipts_table.setColumnCount(5)
        receipts_table.setHorizontalHeaderLabels([
            "رقم السند", "المبلغ بالدولار", "المبلغ بالريال",
            "تاريخ السداد", "ملاحظات"
        ])
        header = receipts_table.horizontalHeader()
        for i in range(5):
            header.setSectionResizeMode(i, QHeaderView.ResizeMode.Stretch)

        layout.addWidget(receipts_table)

        # Add a close button
        buttons = QDialogButtonBox(QDialogButtonBox.StandardButton.Close)
        buttons.rejected.connect(dialog.reject)
        layout.addWidget(buttons)

        # Connect to database
        engine = create_engine('sqlite:///diamond_sales.db')
        Session = sessionmaker(bind=engine)
        session = Session()

        try:
            # Get receipts without reference for customer
            receipts = session.query(Receipt).filter_by(
                customer_id=customer_id,
                sale_id=None,
                receipt_type="CashIn"
            ).order_by(Receipt.issue_date).all()

            # Update table
            receipts_table.setRowCount(len(receipts))

            # متغير للرصيد التراكمي
            cumulative_balance_usd = 0
            cumulative_balance_sar = 0

            for i, receipt in enumerate(receipts):
                # تحديث الرصيد التراكمي
                cumulative_balance_usd += receipt.amount_usd
                cumulative_balance_sar += receipt.amount_sar

                receipts_table.setItem(i, 0, QTableWidgetItem(str(receipt.id)))
                receipts_table.setItem(i, 1, QTableWidgetItem(f"{receipt.amount_usd:.2f}"))
                receipts_table.setItem(i, 2, QTableWidgetItem(f"{receipt.amount_sar:.2f}"))

                # Format date
                date_str = receipt.issue_date.strftime("%d/%m/%Y") if receipt.issue_date else ""
                receipts_table.setItem(i, 3, QTableWidgetItem(date_str))

                # Add cumulative balance as notes
                receipts_table.setItem(i, 4, QTableWidgetItem(f"الرصيد التراكمي: {cumulative_balance_usd:.2f} $ / {cumulative_balance_sar:.2f} ريال"))

            # If no receipts, show message
            if not receipts:
                receipts_table.setRowCount(0)
                QMessageBox.information(dialog, "معلومات", "لا توجد سدادات بدون مرجع لهذا العميل")

            # Show the dialog
            dialog.exec()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء عرض سدادات العميل: {str(e)}")
        finally:
            session.close()