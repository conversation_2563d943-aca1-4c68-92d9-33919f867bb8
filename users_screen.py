from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTableWidget, QTableWidgetItem,
                            QPushButton, QLabel, QLineEdit, QComboBox, QFormLayout, QHeaderView,
                            QMessageBox, QDialog, QCheckBox, QSpacerItem, QSizePolicy)
from PyQt6.QtCore import Qt, pyqtSlot
from PyQt6.QtGui import QFont, QIcon, QColor
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
import bcrypt
from database import User, Base
import re
from datetime import datetime
import app_state
from ui_utils import style_button, style_dialog_buttons
from translations import get_translation as _

class UserDialog(QDialog):
    def __init__(self, parent=None, user=None):
        super().__init__(parent)
        self.user = user
        self.setWindowTitle("إضافة مستخدم جديد" if not user else "تعديل بيانات المستخدم")
        self.setGeometry(300, 300, 400, 300)
        self.setLayoutDirection(Qt.LayoutDirection.RightToLeft)
        self.init_ui()

    def init_ui(self):
        layout = QVBoxLayout(self)

        form_layout = QFormLayout()

        # اسم المستخدم
        self.username_input = QLineEdit()
        if self.user:
            self.username_input.setText(self.user.username)
            self.username_input.setReadOnly(True)  # لا يمكن تعديل اسم المستخدم
        form_layout.addRow("اسم المستخدم:", self.username_input)

        # الاسم الكامل
        self.full_name_input = QLineEdit()
        if self.user and self.user.full_name:
            self.full_name_input.setText(self.user.full_name)
        form_layout.addRow("الاسم الكامل:", self.full_name_input)

        # البريد الإلكتروني
        self.email_input = QLineEdit()
        if self.user and self.user.email:
            self.email_input.setText(self.user.email)
        form_layout.addRow("البريد الإلكتروني:", self.email_input)

        # رقم الهاتف
        self.phone_input = QLineEdit()
        if self.user and self.user.phone:
            self.phone_input.setText(self.user.phone)
        form_layout.addRow("رقم الهاتف:", self.phone_input)

        # كلمة المرور مع زر الإظهار/الإخفاء
        password_container = QWidget()
        password_layout = QHBoxLayout(password_container)
        password_layout.setContentsMargins(0, 0, 0, 0)

        self.password_input = QLineEdit()
        self.password_input.setEchoMode(QLineEdit.EchoMode.Password)

        self.toggle_password_button = QPushButton()
        self.toggle_password_button.setIcon(QIcon("assets/eye_closed.png"))
        self.toggle_password_button.setFixedSize(30, 30)
        self.toggle_password_button.setStyleSheet("""
            QPushButton {
                border: none;
                background-color: transparent;
            }
            QPushButton:hover {
                background-color: #e0e0e0;
            }
        """)
        self.toggle_password_button.clicked.connect(lambda: self.toggle_password_visibility(self.password_input, self.toggle_password_button))

        password_layout.addWidget(self.password_input)
        password_layout.addWidget(self.toggle_password_button)

        form_layout.addRow("كلمة المرور:", password_container)

        # تأكيد كلمة المرور مع زر الإظهار/الإخفاء
        confirm_password_container = QWidget()
        confirm_password_layout = QHBoxLayout(confirm_password_container)
        confirm_password_layout.setContentsMargins(0, 0, 0, 0)

        self.confirm_password_input = QLineEdit()
        self.confirm_password_input.setEchoMode(QLineEdit.EchoMode.Password)

        self.toggle_confirm_password_button = QPushButton()
        self.toggle_confirm_password_button.setIcon(QIcon("assets/eye_closed.png"))
        self.toggle_confirm_password_button.setFixedSize(30, 30)
        self.toggle_confirm_password_button.setStyleSheet("""
            QPushButton {
                border: none;
                background-color: transparent;
            }
            QPushButton:hover {
                background-color: #e0e0e0;
            }
        """)
        self.toggle_confirm_password_button.clicked.connect(lambda: self.toggle_password_visibility(self.confirm_password_input, self.toggle_confirm_password_button))

        confirm_password_layout.addWidget(self.confirm_password_input)
        confirm_password_layout.addWidget(self.toggle_confirm_password_button)

        form_layout.addRow("تأكيد كلمة المرور:", confirm_password_container)

        # إضافة مؤشر تطابق كلمة المرور
        self.password_match_label = QLabel("")
        form_layout.addRow("", self.password_match_label)

        # ربط حدث تغيير كلمة المرور بدالة التحقق من التطابق
        self.password_input.textChanged.connect(self.check_password_match)
        self.confirm_password_input.textChanged.connect(self.check_password_match)

        # نوع المستخدم (الدور)
        self.role_combo = QComboBox()
        self.role_combo.addItems(["admin", "user", "accountant", "sales", "manager"])
        if self.user:
            self.role_combo.setCurrentText(self.user.role)
        form_layout.addRow("نوع المستخدم:", self.role_combo)

        # حالة المستخدم (نشط/غير نشط)
        self.active_checkbox = QCheckBox("مستخدم نشط")
        if self.user:
            self.active_checkbox.setChecked(self.user.is_active)
        else:
            self.active_checkbox.setChecked(True)
        form_layout.addRow("الحالة:", self.active_checkbox)

        layout.addLayout(form_layout)        # أزرار العمليات
        button_layout = QHBoxLayout()
        self.save_button = QPushButton("حفظ")
        self.save_button.clicked.connect(self.save)
        style_button(self.save_button, "add", min_width=120, min_height=40)

        self.cancel_button = QPushButton("إلغاء")
        self.cancel_button.clicked.connect(self.reject)
        style_button(self.cancel_button, "default", min_width=120, min_height=40)

        button_layout.addWidget(self.save_button)
        button_layout.addWidget(self.cancel_button)

        layout.addLayout(button_layout)

    def toggle_password_visibility(self, password_input, toggle_button):
        """تبديل حالة إظهار/إخفاء كلمة المرور"""
        if password_input.echoMode() == QLineEdit.EchoMode.Password:
            # إظهار كلمة المرور
            password_input.setEchoMode(QLineEdit.EchoMode.Normal)
            toggle_button.setIcon(QIcon("assets/eye_open.png"))
        else:
            # إخفاء كلمة المرور
            password_input.setEchoMode(QLineEdit.EchoMode.Password)
            toggle_button.setIcon(QIcon("assets/eye_closed.png"))

    def check_password_match(self):
        """التحقق من تطابق كلمة المرور وتأكيدها"""
        password = self.password_input.text()
        confirm_password = self.confirm_password_input.text()

        if not password or not confirm_password:
            self.password_match_label.setText("")
            return

        if password == confirm_password:
            self.password_match_label.setText("كلمة المرور متطابقة ✓")
            self.password_match_label.setStyleSheet("color: green;")
        else:
            self.password_match_label.setText("كلمة المرور غير متطابقة ✗")
            self.password_match_label.setStyleSheet("color: red;")

    def save(self):
        username = self.username_input.text().strip()
        full_name = self.full_name_input.text().strip()
        email = self.email_input.text().strip()
        phone = self.phone_input.text().strip()
        password = self.password_input.text()
        confirm_password = self.confirm_password_input.text()
        role = self.role_combo.currentText()
        is_active = self.active_checkbox.isChecked()

        # التحقق من البيانات
        if not username:
            QMessageBox.warning(self, _("warning", "تنبيه"), _("username_required", "يجب إدخال اسم المستخدم"))
            return

        # التحقق من عدم وجود محارف خاصة في اسم المستخدم
        if not re.match(r'^[a-zA-Z0-9_]+$', username):
            QMessageBox.warning(self, _("warning", "تنبيه"), _("username_format_error", "اسم المستخدم يجب أن يحتوي على أحرف إنجليزية وأرقام فقط"))
            return

        # التحقق من صحة البريد الإلكتروني إذا تم إدخاله
        if email and not re.match(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$', email):
            QMessageBox.warning(self, _("warning", "تنبيه"), _("invalid_email", "البريد الإلكتروني غير صحيح"))
            return

        # إذا كان إضافة مستخدم جديد أو تم تغيير كلمة المرور
        if not self.user or password:
            if not password:
                QMessageBox.warning(self, _("warning", "تنبيه"), _("password_required", "يجب إدخال كلمة مرور"))
                return

            if password != confirm_password:
                QMessageBox.warning(self, _("warning", "تنبيه"), _("password_mismatch", "كلمة المرور وتأكيدها غير متطابقين"))
                return

        self.username = username
        self.full_name = full_name
        self.email = email
        self.phone = phone
        self.password = password
        self.role = role
        self.is_active = is_active

        self.accept()

class UsersScreen(QWidget):
    def __init__(self):
        super().__init__()

        # الحصول على المستخدم الحالي من حالة التطبيق
        self.user = app_state.get_current_user()
        if not self.user:
            QMessageBox.critical(self, _("error", "خطأ"), _("current_user_not_found", "لم يتم العثور على المستخدم الحالي"))
            self.close()
            return

        # إنشاء جلسة قاعدة البيانات
        self.session = app_state.get_fresh_session()

        self.init_ui()
        self.load_users()

    def init_ui(self):
        self.setWindowTitle(_("users_title", "نظام إدارة مبيعات الألماس - إدارة المستخدمين"))
        self.setGeometry(100, 100, 800, 500)
        self.setLayoutDirection(Qt.LayoutDirection.RightToLeft)

        # Create main layout
        self.main_layout = QVBoxLayout(self)

        # Add header
        header_label = QLabel("إدارة المستخدمين")
        header_label.setFont(QFont("Arial", 14, QFont.Weight.Bold))
        self.main_layout.addWidget(header_label)

        # البحث عن مستخدم
        search_layout = QHBoxLayout()
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("بحث عن مستخدم...")
        self.search_input.textChanged.connect(self.filter_users)

        search_layout.addWidget(QLabel("بحث:"))
        search_layout.addWidget(self.search_input)
        search_layout.addStretch()        # أزرار العمليات
        self.add_button = QPushButton("إضافة مستخدم")
        self.add_button.clicked.connect(self.add_user)
        style_button(self.add_button, "add", min_width=150, min_height=40)
        search_layout.addWidget(self.add_button)

        self.main_layout.addLayout(search_layout)

        # جدول المستخدمين
        self.users_table = QTableWidget()
        self.users_table.setColumnCount(5)
        self.users_table.setHorizontalHeaderLabels(["الرقم", "اسم المستخدم", "نوع المستخدم", "الحالة", "الإجراءات"])
        self.users_table.horizontalHeader().setSectionResizeMode(1, QHeaderView.ResizeMode.Stretch)
        self.users_table.horizontalHeader().setSectionResizeMode(2, QHeaderView.ResizeMode.Stretch)

        self.main_layout.addWidget(self.users_table)

    def load_users(self):
        users = self.session.query(User).all()
        self.display_users(users)

    def filter_users(self):
        search_text = self.search_input.text().strip().lower()
        if not search_text:
            self.load_users()
            return

        users = self.session.query(User).filter(User.username.like(f"%{search_text}%")).all()
        self.display_users(users)

    def display_users(self, users):
        self.users_table.setRowCount(0)

        for i, user in enumerate(users):
            self.users_table.insertRow(i)

            id_item = QTableWidgetItem(str(user.id))
            id_item.setData(Qt.ItemDataRole.UserRole, user.id)  # تخزين معرف المستخدم للاستخدام لاحقًا
            id_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)

            username_item = QTableWidgetItem(user.username)
            username_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)

            role_item = QTableWidgetItem(user.role)
            role_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)

            status_item = QTableWidgetItem("نشط" if user.is_active else "غير نشط")
            status_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            if not user.is_active:
                status_item.setForeground(QColor(255, 0, 0))  # لون أحمر للمستخدمين غير النشطين

            self.users_table.setItem(i, 0, id_item)
            self.users_table.setItem(i, 1, username_item)
            self.users_table.setItem(i, 2, role_item)
            self.users_table.setItem(i, 3, status_item)            # إضافة أزرار الإجراءات
            actions_layout = QHBoxLayout()
            actions_layout.setContentsMargins(2, 2, 2, 2)
            actions_layout.setSpacing(5)
            
            edit_button = QPushButton("تعديل")
            edit_button.clicked.connect(lambda checked, row=i: self.edit_user(row))
            style_button(edit_button, "edit", min_width=70, min_height=35)

            toggle_button = QPushButton("تعطيل" if user.is_active else "تنشيط")
            toggle_button.clicked.connect(lambda checked, row=i: self.toggle_user_status(row))
            style_button(toggle_button, "warning" if user.is_active else "success", min_width=70, min_height=35)

            delete_button = QPushButton("حذف")
            delete_button.clicked.connect(lambda checked, row=i: self.delete_user(row))
            style_button(delete_button, "delete", min_width=70, min_height=35)

            # لا يمكن تعديل/حذف المستخدم الحالي أو حذف المستخدم admin
            if user.username == self.user.username:
                delete_button.setEnabled(False)
                toggle_button.setEnabled(False)
            elif user.username == "admin":
                delete_button.setEnabled(False)

            actions_layout.addWidget(edit_button)
            actions_layout.addWidget(toggle_button)
            actions_layout.addWidget(delete_button)

            actions_widget = QWidget()
            actions_widget.setLayout(actions_layout)

            self.users_table.setCellWidget(i, 4, actions_widget)

        # ضبط عرض الأعمدة
        self.users_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.ResizeMode.ResizeToContents)
        self.users_table.horizontalHeader().setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents)
        self.users_table.horizontalHeader().setSectionResizeMode(4, QHeaderView.ResizeMode.ResizeToContents)

    def add_user(self):
        dialog = UserDialog(self)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            # التحقق من وجود مستخدم بنفس الاسم
            existing_user = self.session.query(User).filter_by(username=dialog.username).first()
            if (existing_user):
                QMessageBox.warning(self, _("warning", "تنبيه"), _("username_exists", "اسم المستخدم موجود بالفعل، الرجاء اختيار اسم مستخدم آخر."))
                return

            # تشفير كلمة المرور
            hashed_password = bcrypt.hashpw(dialog.password.encode('utf-8'), bcrypt.gensalt())

            # إنشاء مستخدم جديد
            new_user = User(
                username=dialog.username,
                password_hash=hashed_password.decode('utf-8'),
                role=dialog.role,
                is_active=dialog.is_active,
                full_name=dialog.full_name,
                email=dialog.email,
                phone=dialog.phone,
                created_at=datetime.now(),
                updated_at=datetime.now()
            )

            try:
                self.session.add(new_user)
                self.session.commit()
                QMessageBox.information(self, _("success", "نجاح"), _("user_added_success", "تم إضافة المستخدم بنجاح."))
                self.load_users()
            except Exception as e:
                self.session.rollback()
                error_msg = _("user_add_error") + ": " + str(e)
                QMessageBox.critical(self, _("error"), error_msg)

    def edit_user(self, row):
        user_id = self.users_table.item(row, 0).data(Qt.ItemDataRole.UserRole)
        user = self.session.query(User).filter_by(id=user_id).first()

        if not user:
            QMessageBox.warning(self, _("warning", "تنبيه"), _("user_not_found", "لم يتم العثور على المستخدم."))
            return

        dialog = UserDialog(self, user)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            try:
                # تحديث بيانات المستخدم
                user.role = dialog.role
                user.is_active = dialog.is_active
                user.full_name = dialog.full_name
                user.email = dialog.email
                user.phone = dialog.phone
                user.updated_at = datetime.now()

                # إذا تم إدخال كلمة مرور جديدة
                if dialog.password:
                    hashed_password = bcrypt.hashpw(dialog.password.encode('utf-8'), bcrypt.gensalt())
                    user.password_hash = hashed_password.decode('utf-8')

                self.session.commit()
                QMessageBox.information(self, _("success", "نجاح"), _("user_updated_success", "تم تحديث بيانات المستخدم بنجاح."))
                self.load_users()
            except Exception as e:
                self.session.rollback()
                error_msg = _("user_update_error") + ": " + str(e)
                QMessageBox.critical(self, _("error"), error_msg)

    def toggle_user_status(self, row):
        user_id = self.users_table.item(row, 0).data(Qt.ItemDataRole.UserRole)
        user = self.session.query(User).filter_by(id=user_id).first()

        if not user:
            QMessageBox.warning(self, _("warning", "تنبيه"), _("user_not_found", "لم يتم العثور على المستخدم."))
            return

        # لا يمكن تغيير حالة المستخدم الحالي
        if user.username == self.user.username:
            QMessageBox.warning(self, _("warning", "تنبيه"), _("cannot_change_current_user_status", "لا يمكن تغيير حالة المستخدم الحالي."))
            return

        try:
            # تغيير حالة المستخدم
            user.is_active = not user.is_active
            self.session.commit()

            action = _("activate", "تنشيط") if user.is_active else _("deactivate", "تعطيل")
            success_msg = _("user_status_changed") + " " + action + " " + _("user_successfully")
            QMessageBox.information(self, _("success"), success_msg) 
            self.load_users()
        except Exception as e:
            self.session.rollback()
            error_msg = _("user_status_change_error") + ": " + str(e)
            QMessageBox.critical(self, _("error"), error_msg)

    def delete_user(self, row):
        user_id = self.users_table.item(row, 0).data(Qt.ItemDataRole.UserRole)
        user = self.session.query(User).filter_by(id=user_id).first()

        if not user:
            QMessageBox.warning(self, _("warning", "تنبيه"), _("user_not_found", "لم يتم العثور على المستخدم."))
            return

        # لا يمكن حذف المستخدم admin أو المستخدم الحالي
        if user.username == "admin":
            QMessageBox.warning(self, _("warning", "تنبيه"), _("cannot_delete_admin", "لا يمكن حذف المستخدم الرئيسي (admin)."))
            return

        if user.username == self.user.username:
            QMessageBox.warning(self, _("warning", "تنبيه"), _("cannot_delete_current_user", "لا يمكن حذف المستخدم الحالي."))
            return

        # تأكيد الحذف
        confirm_msg = _("confirm_delete_user") + " " + user.username + "؟"
        confirm = QMessageBox.question(self, _("confirm_delete"), confirm_msg,
                                      QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No)

        if confirm == QMessageBox.StandardButton.Yes:
            try:
                self.session.delete(user)
                self.session.commit()
                QMessageBox.information(self, _("success", "نجاح"), _("user_deleted_success", "تم حذف المستخدم بنجاح."))
                self.load_users()
            except Exception as e:
                self.session.rollback()
                error_msg = _("user_delete_error") + ": " + str(e)
                QMessageBox.critical(self, _("error"), error_msg)

    def closeEvent(self, event):
        self.session.close()
        super().closeEvent(event)