#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Script para convertir iconos SVG a formato ICO
"""

import os
import cairosvg
from PIL import Image
import io

def svg_to_ico(svg_path, ico_path, sizes=[16, 32, 48, 64, 128, 256]):
    """
    Convierte un archivo SVG a formato ICO con múltiples tamaños
    
    Args:
        svg_path (str): Ruta al archivo SVG
        ico_path (str): Ruta donde guardar el archivo ICO
        sizes (list): Lista de tamaños para el icono
    """
    print(f"Convirtiendo {svg_path} a {ico_path}...")
    
    # Leer el archivo SVG
    with open(svg_path, 'rb') as svg_file:
        svg_data = svg_file.read()
    
    # Convertir SVG a PNG en diferentes tamaños
    images = []
    for size in sizes:
        png_data = cairosvg.svg2png(bytestring=svg_data, output_width=size, output_height=size)
        img = Image.open(io.BytesIO(png_data))
        images.append(img)
    
    # Guardar como ICO
    images[0].save(
        ico_path,
        format='ICO',
        sizes=[(img.width, img.height) for img in images],
        append_images=images[1:]
    )
    
    print(f"Icono guardado en {ico_path}")

def main():
    # Crear carpeta para los iconos ICO si no existe
    if not os.path.exists('assets/icons'):
        os.makedirs('assets/icons')
    
    # Convertir iconos necesarios
    svg_to_ico('assets/diamond_icon.svg', 'assets/diamond_icon.ico')
    
    # Crear un icono para el generador de activación a partir del icono de configuración
    svg_to_ico('assets/settings_icon.svg', 'assets/key_icon.ico')
    
    print("Conversión de iconos completada.")

if __name__ == "__main__":
    main()
