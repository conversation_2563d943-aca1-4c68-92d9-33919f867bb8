"""
نظام تسجيل الأخطاء الموحد للتطبيق
"""

import logging
import os
from datetime import datetime
from logging.handlers import RotatingFileHandler

# إنشاء مجلد للسجلات إذا لم يكن موجودًا
if not os.path.exists('logs'):
    os.makedirs('logs')

# تكوين نظام تسجيل الأخطاء
logger = logging.getLogger('diamond_sales')
logger.setLevel(logging.DEBUG)

# تكوين معالج الملفات مع التدوير
log_file = os.path.join('logs', 'app.log')
file_handler = RotatingFileHandler(log_file, maxBytes=1024*1024*5, backupCount=5, encoding='utf-8')
file_handler.setLevel(logging.DEBUG)

# تكوين تنسيق السجل
formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s', datefmt='%Y-%m-%d %H:%M:%S')
file_handler.setFormatter(formatter)

# إضافة المعالجات إلى المسجل - فقط تسجيل في الملفات بدون عرض على الشاشة
logger.addHandler(file_handler)

# تعليق معالج وحدة التحكم لإخفاء نافذة السجلات عند تشغيل البرنامج
# console_handler = logging.StreamHandler()
# console_handler.setLevel(logging.INFO)
# console_handler.setFormatter(formatter)
# logger.addHandler(console_handler)

# سجل تسجيل الدخول
login_logger = logging.getLogger('diamond_sales.login')
login_logger.setLevel(logging.INFO)
login_log_file = os.path.join('logs', 'login.log')
login_file_handler = RotatingFileHandler(login_log_file, maxBytes=1024*1024*2, backupCount=3, encoding='utf-8')
login_file_handler.setLevel(logging.INFO)
login_file_handler.setFormatter(formatter)
login_logger.addHandler(login_file_handler)

def log_error(message, exception=None):
    """
    تسجيل خطأ في السجل
    
    Args:
        message (str): رسالة الخطأ
        exception (Exception, optional): الاستثناء الذي حدث
    """
    if exception:
        logger.error(f"{message}: {str(exception)}", exc_info=True)
    else:
        logger.error(message)

def log_warning(message):
    """
    تسجيل تحذير في السجل
    
    Args:
        message (str): رسالة التحذير
    """
    logger.warning(message)

def log_info(message):
    """
    تسجيل معلومات في السجل
    
    Args:
        message (str): رسالة المعلومات
    """
    logger.info(message)

def log_debug(message):
    """
    تسجيل معلومات تصحيح في السجل
    
    Args:
        message (str): رسالة التصحيح
    """
    logger.debug(message)

def log_login_attempt(username, success, ip_address=None, details=None):
    """
    تسجيل محاولة تسجيل دخول
    
    Args:
        username (str): اسم المستخدم
        success (bool): نجاح محاولة تسجيل الدخول
        ip_address (str, optional): عنوان IP للمستخدم
        details (str, optional): تفاصيل إضافية
    """
    status = "نجاح" if success else "فشل"
    ip_info = f" من {ip_address}" if ip_address else ""
    detail_info = f" - {details}" if details else ""
    
    login_logger.info(f"محاولة تسجيل دخول ({status}) - المستخدم: {username}{ip_info}{detail_info}")
    
    # تسجيل في السجل العام أيضًا
    if not success:
        logger.warning(f"فشل تسجيل دخول - المستخدم: {username}{ip_info}{detail_info}")
