Diamond Sales Program in Python
The diamond sales program is written in Python, aiming to manage diamond buying and selling operations in two currencies (USD and SAR), issue cash receipt and payment vouchers, provide a customer screen displaying sales details by diamond types (Round/Baguette) and units in carats, and include comprehensive accounting management with a chart of accounts, journal entries, detailed reports for customers, suppliers, sales, and purchases, as well as user management and basic program settings. The following requirements outline the program specifications:

1. User Interface
Design: Create a user interface using Tkinter, PyQt, or Kivy, supporting both Arabic and English languages with right-to-left (RTL) layout for Arabic using libraries like tkinter or PyQt6.
Main Screen: Options include Add Sale/Purchase, Issue Receipt/Payment Voucher, Manage Customers, Manage Suppliers, Reports, Settings, User Management.
Customer Screen: Displays a list of customers with sales details:
Customer Name, Diamond Type, Carat Weight, Price in USD and SAR, Sale Date, Amount Due/Paid.
Supplier Screen: Displays supplier details and purchase information (similar to customer screen).
Search: Allow searching for a customer/supplier by name or ID number.
Settings Screen: Customize company information (name, address, logo, tax number) and exchange rate.
2. Sales and Purchases Management
Input: Record sale/purchase data including Diamond Type (Round/Baguette), Carat Weight, Price per Carat in USD.
Currency Conversion: Automatically convert prices to SAR using an exchange rate (default: 3.75 SAR/USD, adjustable in settings).
Calculation: Compute total amount: (Carat Weight × Price per Carat) in USD, then convert to SAR.
Storage: Save sales and purchase records in the database with operation date and time.
3. Receipt and Payment Vouchers
Details: Issue cash receipt/payment vouchers including Voucher Number, Customer/Supplier Name, Transaction Date, Amount in USD and SAR, Diamond Type, Carat Weight.
PDF Generation: Generate vouchers as PDF files using reportlab or PyPDF2 with printing capability.
Database Link: Link vouchers to journal entries and store them in the database.
4. Database
Database: Use SQLite for lightweight applications or PostgreSQL for scalability.
ORM: Use SQLAlchemy for database connectivity and CRUD operations.
Table Design:
CompanyInfo: (CompanyID, Name, Address, TaxNumber, Logo [as binary or file path]).
ChartOfAccounts: (AccountID, AccountName, AccountType [Asset/Liability/Revenue/Expense], ParentAccountID).
Customers: (CustomerID, Name, IDNumber, PhoneNumber, Address).
Suppliers: (SupplierID, Name, IDNumber, PhoneNumber, Address).
Sales: (SaleID, CustomerID, DiamondType, CaratWeight, PricePerCaratUSD, TotalPriceUSD, TotalPriceSAR, SaleDate, ExchangeRate, AmountDue, AmountPaid).
Purchases: (PurchaseID, SupplierID, DiamondType, CaratWeight, PricePerCaratUSD, TotalPriceUSD, TotalPriceSAR, PurchaseDate, ExchangeRate, AmountDue, AmountPaid).
Receipts: (ReceiptID, SaleID/PurchaseID, Type [CashIn/CashOut], AmountUSD, AmountSAR, IssueDate).
JournalEntries: (EntryID, AccountID, Debit, Credit, Date, Description, SaleID/PurchaseID/ReceiptID).
Users: (UserID, Username, PasswordHash, Role [Admin/Employee]).
Settings: (SettingID, ExchangeRate, DefaultCurrency, Language).
5. Chart of Accounts and Journal Entries
Chart of Accounts: Include accounts for Cash, Customers, Suppliers, Sales, Purchases, Expenses.
Journal Entries: Automatically record entries for each operation:
Sale: Debit (Customers/Cash), Credit (Sales).
Purchase: Debit (Purchases), Credit (Suppliers/Cash).
Receipt: Debit (Cash), Credit (Customers).
Payment: Debit (Suppliers), Credit (Cash).
Management: Display journal entry log with edit/delete options for authorized users.
6. Reports
Customer Report: For a specific customer (by name or ID):
Quantities (Carat weight by diamond type).
Amount due and paid.
Cumulative balances (Remaining balance).
Supplier Report: Similar details for a specific supplier.
Sales and Purchases Report:
Quantities (Total carats by diamond type).
Amounts (In USD and SAR).
Remaining balances (Due from customers/suppliers).
Periodic Reports: Daily/monthly reports for total sales and purchases.
Export: Export reports in PDF (reportlab) or Excel (openpyxl or pandas).
7. User Management and Settings
User Accounts: Create accounts with roles (Admin, Employee) and permissions (e.g., editing journal entries, generating reports).
Password Encryption: Use bcrypt or hashlib for password hashing.
Settings Screen: Customize:
Company information (Name, Address, Logo, Tax Number).
Default exchange rate.
Language (Arabic/English).
Report settings (Format, Company Logo).
8. Technologies and Libraries Required
Language: Python 3.9 or later.
User Interface: Tkinter, PyQt6, or Kivy.
Database: SQLAlchemy with SQLite or PostgreSQL.
Additional Libraries:
reportlab or PyPDF2 for PDF generation.
openpyxl or pandas for Excel exports.
bcrypt or hashlib for password encryption.
python-dateutil for date processing.
Dependencies: Manage with pip and requirements.txt.
9. Additional Considerations
Security: Encrypt sensitive data (e.g., ID numbers) using cryptography or store hashed values.
Performance: Optimize database queries with indexes for large datasets.
Compatibility: Ensure cross-platform support (Windows, macOS, Linux) using Python's portability.
User Experience: Design an intuitive interface with language-switching support using gettext for localization.
10. Example Workflow
Sale Entry: User inputs a sale: Diamond type (Baguette), weight (2.5 carats), price (1000 USD/carat).
Calculation: System computes: Amount in USD (2.5 × 1000 = 2500 USD), in SAR (2500 × 3.75 = 9375 SAR).
Storage: Sale is recorded in the Sales table, a journal entry is created (Debit: Customers, Credit: Sales), and a receipt voucher is generated as a PDF.
Display: Transaction appears on the customer screen, updating due/paid amounts.
Reporting: Customer report shows quantities (2.5 carats), amount due (9375 SAR), amount paid (0 SAR), and cumulative balance (9375 SAR).
Summary
The program provides a comprehensive accounting solution for managing diamond sales and purchases in Python, supporting dual currencies (USD/SAR), issuing vouchers, managing customers/suppliers, recording journal entries, and generating accurate reports. It leverages Python's ecosystem with libraries like SQLAlchemy, Tkinter/PyQt, and reportlab, ensuring cross-