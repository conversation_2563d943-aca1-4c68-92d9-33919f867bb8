@echo off
chcp 65001

set PYTHON_PATH=python.exe
set SCRIPT_PATH=c:\Users\<USER>\Desktop\DiamondSalesV1.20\DiamondSalesV1.20\build_exe_now.py

echo === Building Diamond Sales Executable ===

%PYTHON_PATH% --version
if %ERRORLEVEL% neq 0 (
    echo Python not found in system
    pause
    exit /b 1
)

if not exist %SCRIPT_PATH% (
    echo Build script not found
    pause
    exit /b 1
)

%PYTHON_PATH% %SCRIPT_PATH%

if %ERRORLEVEL% neq 0 (
    echo Build failed
    pause
    exit /b 1
)

echo Build completed successfully!
echo Executable file is located in: dist\DiamondSales\DiamondSales.exe

pause