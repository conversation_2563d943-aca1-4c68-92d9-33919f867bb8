# Build Diamond Sales Executable
Write-Host "Building Diamond Sales executable..." -ForegroundColor Green
Write-Host ""

# Clean previous builds
if (Test-Path "dist") {
    Remove-Item -Recurse -Force "dist"
    Write-Host "Cleaned dist folder" -ForegroundColor Yellow
}

if (Test-Path "build") {
    Remove-Item -Recurse -Force "build"
    Write-Host "Cleaned build folder" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "Running PyInstaller..." -ForegroundColor Cyan

# Run PyInstaller
& ".venv\Scripts\pyinstaller.exe" "diamond_sales.spec"

if ($LASTEXITCODE -eq 0) {
    Write-Host ""
    Write-Host "Build completed successfully!" -ForegroundColor Green
    Write-Host "Executable location: dist\DiamondSales\" -ForegroundColor Yellow
    Write-Host ""
} else {
    Write-Host ""
    Write-Host "Error: Build failed!" -ForegroundColor Red
    Write-Host ""
    exit 1
}

Write-Host "Press any key to continue..."
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")