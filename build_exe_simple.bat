@echo off
chcp 65001

echo === Building Diamond Sales Executable ===

cd /d "%~dp0"

echo === Cleaning previous build ===
if exist "dist\DiamondSales" (
    echo Removing previous build...
    rmdir /s /q "dist\DiamondSales"
)

echo === Running PyInstaller ===
".venv\Scripts\pyinstaller.exe" diamond_sales.spec

if %ERRORLEVEL% neq 0 (
    echo Error occurred during build process.
    pause
    exit /b 1
)

echo === Build completed successfully ===
echo Executable is located in dist\DiamondSales\DiamondSales.exe

pause