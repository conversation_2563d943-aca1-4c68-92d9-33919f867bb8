import re

def fix_file():
    try:
        with open('reports_screen.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Remove markdown code blocks and backticks
        content = re.sub(r'```+python', '', content)
        content = re.sub(r'```+', '', content)
        content = re.sub(r'````+python', '', content)
        content = re.sub(r'````+', '', content)
        content = re.sub(r'`+', '', content)
        
        with open('reports_screen_fixed2.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("File fixed successfully!")
    except Exception as e:
        print(f"Error: {e}")

if __name__ == '__main__':
    fix_file()
