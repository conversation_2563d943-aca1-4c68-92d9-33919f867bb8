"""
وحدة إدارة الصلاحيات
تستخدم هذه الوحدة للتحقق من صلاحيات المستخدمين
"""

from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from database import User, Permission, RolePermission
from logger import log_error, log_info
from functools import wraps
from PyQt6.QtWidgets import QMessageBox

def check_permission(user, permission_code):
    """
    التحقق من صلاحية المستخدم
    
    Args:
        user (User): كائن المستخدم
        permission_code (str): رمز الصلاحية
        
    Returns:
        bool: True إذا كان المستخدم يملك الصلاحية، False خلاف ذلك
    """
    if not user:
        return False
        
    # المسؤول يملك جميع الصلاحيات
    if user.role == "admin":
        return True
        
    try:
        # إنشاء اتصال بقاعدة البيانات
        engine = create_engine('sqlite:///diamond_sales.db')
        Session = sessionmaker(bind=engine)
        session = Session()
        
        # البحث عن الصلاحية
        permission = session.query(Permission).filter_by(code=permission_code).first()
        if not permission:
            log_error(f"الصلاحية غير موجودة: {permission_code}")
            session.close()
            return False
            
        # التحقق من وجود الصلاحية للدور
        role_permission = session.query(RolePermission).filter_by(
            role=user.role,
            permission_id=permission.id
        ).first()
        
        session.close()
        
        return role_permission is not None
    except Exception as e:
        log_error(f"خطأ في التحقق من الصلاحية: {str(e)}")
        return False

def has_permission(permission_code):
    """
    مزخرف (decorator) للتحقق من صلاحية المستخدم
    
    Args:
        permission_code (str): رمز الصلاحية
        
    Returns:
        function: الدالة المزخرفة
    """
    def decorator(func):
        @wraps(func)
        def wrapper(self, *args, **kwargs):
            # الحصول على المستخدم الحالي
            import app_state
            user = app_state.get_current_user()
            
            if not user:
                QMessageBox.critical(None, "خطأ", "لم يتم العثور على المستخدم الحالي")
                return
                
            # التحقق من الصلاحية
            if not check_permission(user, permission_code):
                QMessageBox.warning(None, "تحذير", "ليس لديك صلاحية للقيام بهذه العملية")
                return
                
            # تنفيذ الدالة الأصلية
            return func(self, *args, **kwargs)
        return wrapper
    return decorator

def get_user_permissions(user):
    """
    الحصول على قائمة صلاحيات المستخدم
    
    Args:
        user (User): كائن المستخدم
        
    Returns:
        list: قائمة بكائنات الصلاحيات
    """
    if not user:
        return []
        
    try:
        # إنشاء اتصال بقاعدة البيانات
        engine = create_engine('sqlite:///diamond_sales.db')
        Session = sessionmaker(bind=engine)
        session = Session()
        
        # المسؤول يملك جميع الصلاحيات
        if user.role == "admin":
            permissions = session.query(Permission).all()
            session.close()
            return permissions
            
        # البحث عن صلاحيات الدور
        role_permissions = session.query(RolePermission).filter_by(role=user.role).all()
        
        # الحصول على كائنات الصلاحيات
        permission_ids = [rp.permission_id for rp in role_permissions]
        permissions = session.query(Permission).filter(Permission.id.in_(permission_ids)).all()
        
        session.close()
        
        return permissions
    except Exception as e:
        log_error(f"خطأ في الحصول على صلاحيات المستخدم: {str(e)}")
        return []

def get_all_permissions():
    """
    الحصول على جميع الصلاحيات المتاحة
    
    Returns:
        list: قائمة بكائنات الصلاحيات
    """
    try:
        # إنشاء اتصال بقاعدة البيانات
        engine = create_engine('sqlite:///diamond_sales.db')
        Session = sessionmaker(bind=engine)
        session = Session()
        
        # الحصول على جميع الصلاحيات
        permissions = session.query(Permission).all()
        
        session.close()
        
        return permissions
    except Exception as e:
        log_error(f"خطأ في الحصول على جميع الصلاحيات: {str(e)}")
        return []
