"""
أدوات مساعدة لواجهة المستخدم
"""

import time
import threading
from PyQt6.QtWidgets import (QProgressDialog, QMessageBox, QApplication,
                            QSplashScreen, QDialog, QVBoxLayout, QLabel,
                            QProgressBar, QPushButton, QSizePolicy)
from PyQt6.QtCore import Qt, QTimer, pyqtSignal, QRect
from PyQt6.QtGui import QPixmap, QFont
from logger import log_error, log_info
from translations import get_translation as _

class BackgroundTask(threading.Thread):
    """
    فئة لتنفيذ المهام في الخلفية مع عرض مؤشر تقدم
    """
    def __init__(self, task_func, *args, **kwargs):
        """
        تهيئة المهمة

        Args:
            task_func (function): الدالة التي ستنفذ في الخلفية
            *args: المعاملات التي ستمرر للدالة
            **kwargs: المعاملات المسماة التي ستمرر للدالة
        """
        super().__init__()
        self.task_func = task_func
        self.args = args
        self.kwargs = kwargs
        self.result = None
        self.error = None
        self.daemon = True  # جعل الخيط ينتهي عند انتهاء البرنامج الرئيسي

    def run(self):
        """
        تنفيذ المهمة
        """
        try:
            self.result = self.task_func(*self.args, **kwargs)
        except Exception as e:
            self.error = e
            log_error(f"خطأ في تنفيذ المهمة في الخلفية: {str(e)}")

def run_with_progress(parent, task_func, *args, title="جاري التنفيذ", message="يرجى الانتظار...", **kwargs):
    """
    تنفيذ مهمة مع عرض مؤشر تقدم

    Args:
        parent (QWidget): العنصر الأب
        task_func (function): الدالة التي ستنفذ
        *args: المعاملات التي ستمرر للدالة
        title (str): عنوان مربع الحوار
        message (str): رسالة مربع الحوار
        **kwargs: المعاملات المسماة التي ستمرر للدالة

    Returns:
        النتيجة التي تعيدها الدالة

    Raises:
        Exception: إذا حدث خطأ أثناء تنفيذ المهمة
    """
    # إنشاء مربع حوار التقدم
    progress = QProgressDialog(message, "إلغاء", 0, 0, parent)
    progress.setWindowTitle(title)
    progress.setWindowModality(Qt.WindowModality.WindowModal)
    progress.setMinimumDuration(500)  # عرض مربع الحوار بعد 500 مللي ثانية
    progress.setValue(0)
    progress.setAutoClose(True)
    progress.setCancelButton(None)  # إزالة زر الإلغاء

    # إنشاء وتشغيل المهمة في الخلفية
    task = BackgroundTask(task_func, *args, **kwargs)
    task.start()

    # انتظار انتهاء المهمة مع تحديث واجهة المستخدم
    while task.is_alive():
        QApplication.processEvents()
        time.sleep(0.1)

    # إغلاق مربع الحوار
    progress.close()

    # التحقق من وجود خطأ
    if task.error:
        raise task.error

    return task.result

class ProgressDialog(QDialog):
    """
    مربع حوار تقدم مخصص مع إمكانية تحديث النسبة المئوية
    """
    def __init__(self, parent=None, title="جاري التنفيذ", message="يرجى الانتظار..."):
        """
        تهيئة مربع الحوار

        Args:
            parent (QWidget): العنصر الأب
            title (str): عنوان مربع الحوار
            message (str): رسالة مربع الحوار
        """
        super().__init__(parent)
        self.setWindowTitle(title)
        self.setWindowModality(Qt.WindowModality.WindowModal)
        self.setMinimumWidth(300)

        # إنشاء عناصر واجهة المستخدم
        layout = QVBoxLayout(self)

        self.message_label = QLabel(message)
        layout.addWidget(self.message_label)

        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        layout.addWidget(self.progress_bar)

        self.setLayout(layout)

    def set_progress(self, value):
        """
        تعيين قيمة التقدم

        Args:
            value (int): قيمة التقدم (0-100)
        """
        self.progress_bar.setValue(value)
        QApplication.processEvents()

    def set_message(self, message):
        """
        تعيين رسالة التقدم

        Args:
            message (str): الرسالة الجديدة
        """
        self.message_label.setText(message)
        QApplication.processEvents()

class SplashScreen(QSplashScreen):
    """
    شاشة البداية
    """
    def __init__(self, image_path=None):
        """
        تهيئة شاشة البداية

        Args:
            image_path (str): مسار صورة شاشة البداية
        """
        if image_path:
            pixmap = QPixmap(image_path)
        else:
            # إنشاء صورة افتراضية
            pixmap = QPixmap(400, 300)
            pixmap.fill(Qt.GlobalColor.white)

        super().__init__(pixmap)

        # إضافة نص في الأسفل
        self.showMessage("جاري تحميل التطبيق...", Qt.AlignmentFlag.AlignBottom | Qt.AlignmentFlag.AlignCenter, Qt.GlobalColor.black)

    def set_message(self, message):
        """
        تعيين رسالة شاشة البداية

        Args:
            message (str): الرسالة الجديدة
        """
        self.showMessage(message, Qt.AlignmentFlag.AlignBottom | Qt.AlignmentFlag.AlignCenter, Qt.GlobalColor.black)
        QApplication.processEvents()


def center_window(window):
    """
    توسيط النافذة في منتصف الشاشة

    Args:
        window (QWidget): النافذة المراد توسيطها
    """
    # الحصول على مستطيل الشاشة
    screen_geometry = window.screen().availableGeometry()

    # حساب النقطة المركزية
    center_point = screen_geometry.center()

    # حساب النقطة العلوية اليسرى للنافذة
    window_frame = window.frameGeometry()
    window_frame.moveCenter(center_point)

    # تحريك النافذة
    window.move(window_frame.topLeft())

    # تأكد من أن الواجهة تظهر بشكل مناسب على الشاشة وليست كبيرة جدًا
    width = min(window.width(), int(screen_geometry.width() * 0.9))
    height = min(window.height(), int(screen_geometry.height() * 0.9))

    if width < window.width() or height < window.height():
        window.resize(int(width), int(height))
        # إعادة التوسيط بعد تعديل الحجم
        window_frame = window.frameGeometry()
        window_frame.moveCenter(center_point)
        window.move(window_frame.topLeft())


def style_button(button, button_type="default", min_width=150, min_height=45):
    """
    تطبيق تنسيق موحد على الأزرار في التطبيق
    
    Args:
        button (QPushButton): زر لتطبيق التنسيق عليه
        button_type (str): نوع الزر (default, add, edit, delete, success, danger)
        min_width (int): الحد الأدنى للعرض
        min_height (int): الحد الأدنى للارتفاع
    """
    # أنماط الأزرار بناءً على النوع
    styles = {
        "default": "#3498db", # أزرق
        "add": "#27ae60",     # أخضر
        "edit": "#3498db",    # أزرق
        "delete": "#e74c3c",  # أحمر
        "success": "#2ecc71", # أخضر فاتح
        "danger": "#c0392b",  # أحمر داكن
        "warning": "#f39c12", # برتقالي
        "info": "#3498db"     # أزرق
    }
    
    # تعيين اللون الافتراضي إذا كان النوع غير موجود
    color = styles.get(button_type, "#3498db")
    
    # قالب التنسيق للزر
    btn_style = f"""
        QPushButton {{
            background-color: {color};
            color: white;
            border: none;
            border-radius: 5px;
            font-size: 12pt;
            font-weight: bold;
            padding: 8px;
        }}
        QPushButton:hover {{
            background-color: {lighten_color(color)};
        }}
        QPushButton:pressed {{
            background-color: {darken_color(color)};
        }}
        QPushButton:disabled {{
            background-color: #cccccc;
            color: #888888;
        }}
    """
    
    # تطبيق التنسيق
    button.setMinimumWidth(min_width)
    button.setMinimumHeight(min_height)
    button.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed)
    button.setStyleSheet(btn_style)
    
    # تعيين الخط
    font = QFont()
    font.setPointSize(12)
    font.setBold(True)
    button.setFont(font)
    
    return button


def lighten_color(hex_color):
    """
    تفتيح لون Hex بنسبة معينة للاستخدام عند تحويم الماوس
    """
    # تحويل لون هيكس إلى RGB
    hex_color = hex_color.lstrip('#')
    r = int(hex_color[0:2], 16)
    g = int(hex_color[2:4], 16)
    b = int(hex_color[4:6], 16)
    
    # تفتيح بنسبة 10%
    factor = 0.1
    r = min(int(r + (255 - r) * factor), 255)
    g = min(int(g + (255 - g) * factor), 255)
    b = min(int(b + (255 - b) * factor), 255)
    
    # تحويل RGB إلى هيكس
    return "#{:02x}{:02x}{:02x}".format(r, g, b)


def darken_color(hex_color):
    """
    تغميق لون Hex بنسبة معينة للاستخدام عند الضغط
    """
    # تحويل لون هيكس إلى RGB
    hex_color = hex_color.lstrip('#')
    r = int(hex_color[0:2], 16)
    g = int(hex_color[2:4], 16)
    b = int(hex_color[4:6], 16)
    
    # تغميق بنسبة 10%
    factor = 0.1
    r = max(int(r * (1 - factor)), 0)
    g = max(int(g * (1 - factor)), 0)
    b = max(int(b * (1 - factor)), 0)
    
    # تحويل RGB إلى هيكس
    return "#{:02x}{:02x}{:02x}".format(r, g, b)


def style_dialog_buttons(dialog_buttons, ok_color="#27ae60", cancel_color="#e74c3c"):
    """
    تنسيق أزرار مربعات الحوار
    
    Args:
        dialog_buttons (QDialogButtonBox): مجموعة أزرار مربع الحوار
        ok_color (str): لون زر الموافقة/الحفظ
        cancel_color (str): لون زر الإلغاء
    """
    # تنسيق زر الموافقة
    ok_button = dialog_buttons.button(dialog_buttons.StandardButton.Ok)
    if ok_button:
        ok_button.setText("موافق")
        ok_button.setMinimumWidth(120)
        ok_button.setMinimumHeight(40)
        ok_button.setStyleSheet(f"""
            QPushButton {{
                background-color: {ok_color};
                color: white;
                border: none;
                border-radius: 4px;
                font-size: 12pt;
                font-weight: bold;
                padding: 5px 15px;
            }}
            QPushButton:hover {{
                background-color: {lighten_color(ok_color)};
            }}
            QPushButton:pressed {{
                background-color: {darken_color(ok_color)};
            }}
        """)
    
    # تنسيق زر الحفظ
    save_button = dialog_buttons.button(dialog_buttons.StandardButton.Save)
    if save_button:
        save_button.setText("حفظ")
        save_button.setMinimumWidth(120)
        save_button.setMinimumHeight(40)
        save_button.setStyleSheet(f"""
            QPushButton {{
                background-color: {ok_color};
                color: white;
                border: none;
                border-radius: 4px;
                font-size: 12pt;
                font-weight: bold;
                padding: 5px 15px;
            }}
            QPushButton:hover {{
                background-color: {lighten_color(ok_color)};
            }}
            QPushButton:pressed {{
                background-color: {darken_color(ok_color)};
            }}
        """)
    
    # تنسيق زر الإلغاء
    cancel_button = dialog_buttons.button(dialog_buttons.StandardButton.Cancel)
    if cancel_button:
        cancel_button.setText("إلغاء")
        cancel_button.setMinimumWidth(120)
        cancel_button.setMinimumHeight(40)
        cancel_button.setStyleSheet(f"""
            QPushButton {{
                background-color: {cancel_color};
                color: white;
                border: none;
                border-radius: 4px;
                font-size: 12pt;
                font-weight: bold;
                padding: 5px 15px;
            }}
            QPushButton:hover {{
                background-color: {lighten_color(cancel_color)};
            }}
            QPushButton:pressed {{
                background-color: {darken_color(cancel_color)};
            }}
        """)
    
    return dialog_buttons

def confirm_dialog(parent, title, message):
    """
    عرض مربع حوار للتأكيد مع نعم/لا

    Args:
        parent (QWidget): العنصر الأب
        title (str): عنوان مربع الحوار
        message (str): الرسالة المعروضة

    Returns:
        bool: True إذا تم الضغط على نعم، False إذا تم الضغط على لا
    """
    reply = QMessageBox.question(parent, title, message,
                               QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                               QMessageBox.StandardButton.No)
    return reply == QMessageBox.StandardButton.Yes
