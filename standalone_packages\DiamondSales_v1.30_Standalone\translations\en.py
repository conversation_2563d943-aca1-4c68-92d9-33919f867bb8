#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Translation file for English language
"""

translations = {
    # Window titles
    "app_title": "Diamond Sales Management System",
    "login_title": "Diamond Sales Management System - Login",
    "dashboard_title": "Diamond Sales Management System - Dashboard",
    "sales_title": "Diamond Sales Management System - Sales",
    "purchases_title": "Diamond Sales Management System - Purchases",
    "customers_title": "Diamond Sales Management System - Customers",
    "suppliers_title": "Diamond Sales Management System - Suppliers",
    "vouchers_title": "Diamond Sales Management System - Vouchers",
    "reports_title": "Diamond Sales Management System - Reports",
    "settings_title": "Diamond Sales Management System - Settings",
    "users_title": "Diamond Sales Management System - Users Management",
    
    # Main menu
    "welcome": "Welcome",
    "sales": "Sales",
    "purchases": "Purchases",
    "customers": "Customers",
    "suppliers": "Suppliers",
    "vouchers": "Vouchers",
    "reports": "Reports",
    "settings": "Settings",
    "users": "Users",
    "logout": "Logout",
    "copyright": "© 2025 Diamond Sales Management System - All Rights Reserved",
    
    # Login screen
    "username": "Username",
    "password": "Password",
    "login": "Login",
    "login_error": "Login Error",
    "invalid_credentials": "Invalid username or password",
    
    # Common tabs
    "list_tab": "List",
    "add_tab": "Add New",
    "edit_tab": "Edit",
    "details_tab": "Details",
    
    # Common buttons
    "save": "Save",
    "cancel": "Cancel",
    "add": "Add",
    "edit": "Edit",
    "delete": "Delete",
    "search": "Search",
    "print": "Print",
    "close": "Close",
    "browse": "Browse...",
    
    # Common messages
    "success": "Success",
    "error": "Error",
    "warning": "Warning",
    "info": "Information",
    "confirm": "Confirm",
    "confirm_delete": "Are you sure you want to delete?",
    "operation_success": "Operation completed successfully",
    "operation_error": "An error occurred during the operation",
    
    # Sales
    "sales_list": "Sales List",
    "add_sale": "Add New Sale",
    "sale_details": "Sale Details",
    "sale_id": "Sale ID",
    "customer": "Customer",
    "diamond_type": "Diamond Type",
    "carat_weight": "Carat Weight",
    "price_per_carat": "Price per Carat",
    "total_price_usd": "Total (USD)",
    "total_price_sar": "Total (SAR)",
    "sale_date": "Sale Date",
    "amount_paid": "Amount Paid",
    "amount_due": "Amount Due",
    "notes": "Notes",
    "create_receipt": "Create Receipt",
    "print_invoice": "Print Invoice",
    
    # Purchases
    "purchases_list": "Purchases List",
    "add_purchase": "Add New Purchase",
    "purchase_details": "Purchase Details",
    "purchase_id": "Purchase ID",
    "supplier": "Supplier",
    "purchase_date": "Purchase Date",
    "create_payment": "Create Payment",
    
    # Customers
    "customers_list": "Customers List",
    "add_customer": "Add New Customer",
    "customer_details": "Customer Details",
    "customer_id": "Customer ID",
    "customer_name": "Customer Name",
    "id_number": "ID Number",
    "phone": "Phone",
    "address": "Address",
    
    # Suppliers
    "suppliers_list": "Suppliers List",
    "add_supplier": "Add New Supplier",
    "supplier_details": "Supplier Details",
    "supplier_id": "Supplier ID",
    "supplier_name": "Supplier Name",
    
    # Settings
    "company_info": "Company Information",
    "company_name": "Company Name",
    "company_address": "Company Address",
    "tax_number": "Tax/Registration Number",
    "logo": "Logo",
    "system_settings": "System Settings",
    "exchange_rate": "Exchange Rate",
    "default_currency": "Default Currency",
    "language": "Language",
    "arabic": "العربية",
    "english": "English",
    "backup": "Backup",
    "backup_path": "Backup Path",
    "auto_backup": "Auto Backup",
    "create_backup": "Create Backup",
    "restore_backup": "Restore Backup",
    
    # Language
    "language_changed": "Language Changed",
    "restart_required": "Please restart the application to apply changes",
    "change_language": "Change Language",
}
