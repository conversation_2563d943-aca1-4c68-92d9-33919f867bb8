# ميزات تصدير Excel المحسنة

## نظرة عامة
تم إضافة ميزات تصدير Excel محسنة لتقارير العملاء والموردين مع نفس التنسيق والخصائص الموجودة في ملفات الطباعة HTML.

## الميزات الجديدة

### 1. أزرار تصدير Excel
- **تقرير العملاء**: زر "تصدير Excel" بجانب زر "تصدير HTML"
- **تقرير الموردين**: زر "تصدير Excel" بجانب زر "تصدير HTML"

### 2. تنسيق Excel المحسن

#### العناوين والرؤوس
- عنوان الشركة بتنسيق احترافي
- اسم التقرير ونوعه
- الفترة الزمنية للتقرير
- رؤوس أعمدة ملونة ومنسقة

#### البيانات
- جدول البيانات التفصيلي مع تنسيق متناسق
- صف الإجماليات بتنسيق مميز
- ألوان متناوبة للصفوف لسهولة القراءة

#### ملخص الإجماليات المحسن
- **قسم منفصل**: ملخص الإجماليات في قسم منفصل أسفل الجدول
- **تصميم شبكة**: تخطيط 2×2 مثل ملف الطباعة
- **ألوان مميزة**: 
  - تقرير العملاء: أزرق (#2E86AB)
  - تقرير الموردين: أحمر (#E74C3C)
- **معلومات شاملة**:
  - إجمالي الوزن بالقيراط
  - إجمالي المستحق (دولار وريال)
  - إجمالي المسدد (دولار وريال)
  - إجمالي الرصيد (دولار وريال)

#### التذييل
- اسم المستخدم الذي أنشأ التقرير
- تاريخ ووقت الطباعة

### 3. التنسيقات المستخدمة

#### تقرير العملاء
```python
# عنوان ملخص الإجماليات
bg_color: '#2E86AB' (أزرق)
font_color: 'white'

# خلايا الملخص
bg_color: '#F0F8FF' (أزرق فاتح)
font_color: '#2E86AB'
```

#### تقرير الموردين
```python
# عنوان ملخص الإجماليات
bg_color: '#E74C3C' (أحمر)
font_color: 'white'

# خلايا الملخص
bg_color: '#FFF5F5' (أحمر فاتح)
font_color: '#E74C3C'
```

## كيفية الاستخدام

### تصدير تقرير العميل
1. اذهب إلى تبويب "تقارير العملاء"
2. اختر العميل المطلوب
3. حدد الفترة الزمنية
4. اضغط على "عرض التقرير"
5. اضغط على "تصدير Excel"
6. اختر مكان الحفظ
7. سيتم فتح الملف تلقائياً

### تصدير تقرير المورد
1. اذهب إلى تبويب "تقارير الموردين"
2. اختر المورد المطلوب
3. حدد الفترة الزمنية
4. اضغط على "عرض التقرير"
5. اضغط على "تصدير Excel"
6. اختر مكان الحفظ
7. سيتم فتح الملف تلقائياً

## الملفات المتأثرة

### الملفات المحدثة
- `reports_screen.py`: إضافة أزرار وتحسين دوال التصدير
- `test_excel_export_demo.py`: ملف اختبار لإنشاء نماذج Excel

### الملفات الجديدة
- `تقرير_العميل_نموذج.xlsx`: نموذج تقرير العميل
- `تقرير_المورد_نموذج.xlsx`: نموذج تقرير المورد
- `Excel_Export_Features.md`: هذا الملف

## المتطلبات التقنية

### المكتبات المطلوبة
- `xlsxwriter`: لإنشاء ملفات Excel
- `PyQt6`: للواجهة الرسومية
- `sqlalchemy`: لقاعدة البيانات

### التثبيت
```bash
pip install xlsxwriter
```

## المزايا

### للمستخدمين
- **سهولة الاستخدام**: زر واحد للتصدير
- **تنسيق احترافي**: مظهر منسق وجذاب
- **معلومات شاملة**: جميع البيانات والإجماليات
- **فتح تلقائي**: يفتح الملف بعد التصدير

### للمطورين
- **كود منظم**: دوال منفصلة لكل نوع تقرير
- **قابلية التوسع**: سهولة إضافة تقارير جديدة
- **معالجة الأخطاء**: حماية من الأخطاء وتسجيلها
- **توافق**: يعمل مع جميع إصدارات Excel

## الاختبار

### ملفات الاختبار
- `test_excel_export_demo.py`: ينشئ ملفات Excel نموذجية
- `تقرير_العميل_نموذج.xlsx`: نموذج لتقرير العميل
- `تقرير_المورد_نموذج.xlsx`: نموذج لتقرير المورد

### تشغيل الاختبار
```bash
python test_excel_export_demo.py
```

## الدعم والصيانة

### تسجيل الأخطاء
- جميع الأخطاء تُسجل في `error_log.txt`
- رسائل خطأ واضحة للمستخدم
- معلومات تفصيلية للمطورين

### التحديثات المستقبلية
- إمكانية إضافة المزيد من التنسيقات
- دعم تصدير تقارير أخرى
- تحسينات على الأداء

---

**تم التطوير بواسطة**: فريق تطوير نظام إدارة المجوهرات
**التاريخ**: 2025-06-29
**الإصدار**: 1.50
