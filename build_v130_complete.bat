@echo off
chcp 65001

echo === بدء عملية البناء للإصدار 1.30 ===

cd /d "%~dp0"

echo === جاري بناء البرنامج الرئيسي ===

if exist "dist\DiamondSales" (
    echo حذف مجلد الإخراج السابق...
    rmdir /s /q "dist\DiamondSales"
)

echo جاري تنفيذ PyInstaller...

set PYTHON_EXE="%~dp0.venv\Scripts\python.exe"
set PYINSTALLER_EXE="%~dp0.venv\Scripts\pyinstaller.exe"

echo Using Python: %PYTHON_EXE%
echo Using PyInstaller: %PYINSTALLER_EXE%

%PYINSTALLER_EXE% diamond_sales.spec

if %ERRORLEVEL% neq 0 (
    echo حدث خطأ أثناء بناء البرنامج الرئيسي.
    pause
    exit /b 1
)

echo === جاري إنشاء مجلد التوزيع ===

if not exist "installer_package" (
    mkdir "installer_package"
)

echo جاري نسخ الملفات إلى مجلد التوزيع...
xcopy /E /I /Y "dist\DiamondSales" "installer_package\DiamondSales"

echo === جاري إنشاء برنامج التثبيت ===

set INNO_SETUP_FOUND=0

if exist "C:\Program Files (x86)\Inno Setup 6\ISCC.exe" (
    set INNO_SETUP_PATH="C:\Program Files (x86)\Inno Setup 6\ISCC.exe"
    set INNO_SETUP_FOUND=1
) else if exist "C:\Program Files\Inno Setup 6\ISCC.exe" (
    set INNO_SETUP_PATH="C:\Program Files\Inno Setup 6\ISCC.exe"
    set INNO_SETUP_FOUND=1
) else if exist "C:\Program Files (x86)\Inno Setup 5\ISCC.exe" (
    set INNO_SETUP_PATH="C:\Program Files (x86)\Inno Setup 5\ISCC.exe"
    set INNO_SETUP_FOUND=1
) else if exist "C:\Program Files\Inno Setup 5\ISCC.exe" (
    set INNO_SETUP_PATH="C:\Program Files\Inno Setup 5\ISCC.exe"
    set INNO_SETUP_FOUND=1
)

if %INNO_SETUP_FOUND% equ 0 (
    echo لم يتم العثور على Inno Setup Compiler. يرجى تثبيته أولاً.
    echo يمكنك تنزيله من: https://jrsoftware.org/isdl.php
    pause
    exit /b 1
)

echo جاري تنفيذ Inno Setup Compiler...
cd /d "%~dp0"

if exist "..\New folder\diamond_installer.iss" (
    echo استخدام ملف الإعداد من المجلد الجديد...
    %INNO_SETUP_PATH% "..\New folder\diamond_installer.iss"
) else if exist "..\New folder\main_program_setup.iss" (
    echo استخدام ملف الإعداد الرئيسي من المجلد الجديد...
    %INNO_SETUP_PATH% "..\New folder\main_program_setup.iss"
) else (
    echo لم يتم العثور على ملفات الإعداد. يرجى التحقق من وجود ملفات .iss في المجلد.
    pause
    exit /b 1
)

if %ERRORLEVEL% neq 0 (
    echo حدث خطأ أثناء إنشاء برنامج التثبيت.
    pause
    exit /b 1
)

echo === تم الانتهاء من عملية البناء بنجاح ===
echo برنامج التثبيت موجود في مجلد 'installer_output'.

pause