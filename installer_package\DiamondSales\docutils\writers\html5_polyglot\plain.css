/* CSS31_ style sheet for the output of Docutils HTML writers.             */
/* Rules for easy reading and pre-defined style variants.                  */
/*                                                                         */
/* :Author: <PERSON><PERSON><PERSON>, based on html4css1.css by <PERSON>          */
/* :Id: $Id: plain.css 9615 2024-04-06 13:28:15Z milde $                                                               */
/* :Copyright: © 2015 Günter Milde.                                        */
/* :License: Released under the terms of the `2-Clause BSD license`_,      */
/*    in short:                                                            */
/*                                                                         */
/*    Copying and distribution of this file, with or without modification, */
/*    are permitted in any medium without royalty provided the copyright   */
/*    notice and this notice are preserved.                                */
/*                                                                         */
/*    This file is offered as-is, without any warranty.                    */
/*                                                                         */
/* .. _2-Clause BSD license: http://www.spdx.org/licenses/BSD-2-Clause     */
/* .. _CSS3: https://www.w3.org/Style/CSS/                                 */


/* Document Structure */
/* ****************** */

/* "page layout" */
body {
  margin: 0;
  background-color: #dbdbdb;
  --field-indent: 9em; /* default indent of fields in field lists */
}
main, footer, header {
  line-height:1.6;
  /* avoid long lines --> better reading */
  /* optimum is 45…75 characters/line <http://webtypography.net/2.1.2> */
  /* OTOH: lines should not be too short because of missing hyphenation, */
  max-width: 50rem;
  padding: 1px 2%; /* 1px on top avoids grey bar above title (mozilla) */
  margin: auto;
}
main {
  counter-reset: table figure;
  background-color: white;
}
footer, header {
  font-size: smaller;
  padding: 0.5em 2%;
  border: none;
}

/* Table of Contents */
ul.auto-toc > li > p {
  padding-left: 1em;
  text-indent: -1em;
}
nav.contents ul {
  padding-left: 1em;
}
main > nav.contents ul ul ul ul:not(.auto-toc) {
  list-style-type: '\2B29\ ';
}
main > nav.contents ul ul ul ul ul:not(.auto-toc) {
  list-style-type: '\2B1D\ ';
}

/* Transitions */
hr.docutils {
  width: 80%;
  margin-top: 1em;
  margin-bottom: 1em;
  clear: both;
}

/* Paragraphs */

/* vertical space (parskip) */
p, ol, ul, dl, li,
.footnote, .citation,
div > math,
table {
  margin-top: 0.5em;
  margin-bottom: 0.5em;
}

h1, h2, h3, h4, h5, h6,
dd, details > p:last-child {
  margin-bottom: 0.5em;
}

/* Lists */
/* ===== */

/* Definition Lists */
/* Indent lists nested in definition lists */
dd > ul:only-child, dd > ol:only-child { padding-left: 1em; }

/* Description Lists */
/* styled like in most dictionaries, encyclopedias etc. */
dl.description {
  display: flow-root;
}
dl.description > dt {
  font-weight: bold;
  clear: left;
  float: left;
  margin: 0;
  padding: 0;
  padding-right: 0.3em;
}
dl.description > dd:after {
  display: table;
  content: "";
  clear: left; /* clearfix for empty descriptions */
}

/* Field Lists */

dl.field-list > dd,
dl.docinfo > dd {
  margin-left: var(--field-indent); /* adapted in media queries or HTML */
}

/* example for custom field-name width */
dl.field-list.narrow > dd {
  --field-indent: 5em;
}
/* run-in: start field-body on same line after long field names */
dl.field-list.run-in > dd p {
  display: block;
}

/* Bibliographic Fields */

/* generally, bibliographic fields use dl.docinfo */
/* but dedication and abstract are placed into divs */
div.abstract p.topic-title {
  text-align: center;
}
div.dedication {
  margin: 2em 5em;
  text-align: center;
  font-style: italic;
}
div.dedication p.topic-title {
  font-style: normal;
}

/* disclosures */
details { padding-left: 1em; }
summary { margin-left: -1em; }

/* Text Blocks */
/* =========== */

/* Literal Blocks */
pre.literal-block, pre.doctest-block,
pre.math, pre.code {
  font-family: monospace;
}

/* Block Quotes and Topics */
bockquote { margin: 1em 2em; }
blockquote p.attribution,
.topic p.attribution {
  text-align: right;
  margin-left: 20%;
}

/* Tables */
/* ====== */

/* th { vertical-align: bottom; } */

table tr { text-align: left; }

/* "booktabs" style (no vertical lines) */
table.booktabs {
  border: 0;
  border-top: 2px solid;
  border-bottom: 2px solid;
  border-collapse: collapse;
}
table.booktabs * {
  border: 0;
}
table.booktabs th {
  border-bottom: thin solid;
}

/* numbered tables (counter defined in div.document) */
table.numbered > caption:before {
  counter-increment: table;
  content: "Table " counter(table) ": ";
  font-weight: bold;
}

/* Explicit Markup Blocks */
/* ====================== */

/* Footnotes and Citations */
/* ----------------------- */

/* line on the left */
.footnote-list {
  border-left: solid thin;
  padding-left: 0.25em;
}

/* Directives */
/* ---------- */

/* Body Elements */
/* ~~~~~~~~~~~~~ */

/* Images and Figures */

/* let content flow to the side of aligned images and figures */
figure.align-left,
img.align-left,
svg.align-left,
video.align-left,
div.align-left,
object.align-left {
  clear: left;
  float: left;
  margin-right: 1em;
}
figure.align-right,
img.align-right,
svg.align-right,
video.align-right,
div.align-right,
object.align-right {
  clear: right;
  float: right;
  margin-left: 1em;
}
/* Stop floating sidebars, images and figures */
h1, h2, h3, h4, footer, header { clear: both; }

/* Numbered figures */
figure.numbered > figcaption > p:before {
  counter-increment: figure;
  content: "Figure " counter(figure) ": ";
  font-weight: bold;
}

/* Admonitions and System Messages */
.caution p.admonition-title,
.attention p.admonition-title,
.danger p.admonition-title,
.error p.admonition-title,
.warning p.admonition-title,
div.error {
  color: red;
}

/* Sidebar */
/* Move right. In a layout with fixed margins, */
/* it can be moved into the margin.            */
aside.sidebar {
  width: 30%;
  max-width: 26em;
  float: right;
  clear: right;
  margin-left: 1em;
  margin-right: -1%;
  background-color: #fffffa;
}


/* Code */
pre.code { padding: 0.7ex }
pre.code, code { background-color: #eeeeee }
/* basic highlighting: for a complete scheme, see */
/* https://docutils.sourceforge.io/sandbox/stylesheets/ */
pre.code .comment, code .comment { color: #5C6576 }
pre.code .keyword, code .keyword { color: #3B0D06; font-weight: bold }
pre.code .literal.string, code .literal.string { color: #0C5404 }
pre.code .name.builtin, code .name.builtin { color: #352B84 }
pre.code .deleted, code .deleted { background-color: #DEB0A1}
pre.code .inserted, code .inserted { background-color: #A3D289}


/* Epigraph           */
/* Highlights         */
/* Pull-Quote         */
/* Compound Paragraph */
/* Container          */

/* Inline Markup */
/* ============= */

sup, sub { line-height: 0.8; } /* do not add leading for lines with sup/sub */

/* Inline Literals                                          */
/* possible values: normal, nowrap, pre, pre-wrap, pre-line */
/*   span.docutils.literal { white-space: pre-wrap; }       */

/* Hyperlink References */
a { text-decoration: none; }

/* External Targets       */
/*   span.target.external */
/* Internal Targets       */
/*   span.target.internal */
/* Footnote References    */
/*   a[role="doc-noteref"] */
/* Citation References    */
/*   a.citation-reference */
