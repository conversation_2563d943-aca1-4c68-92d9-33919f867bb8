from database import صندوق_النقدية, حركة_نقدية
from db_session import session_scope

print("Checking Cash Box Status...")

with session_scope() as session:
    cash_box = session.query(صندوق_النقدية).first()
    print(f"Cash Box: {cash_box}")
    
    transactions = session.query(حركة_نقدية).order_by(حركة_نقدية.id.desc()).limit(5).all()
    print("\nRecent Transactions:")
    for t in transactions:
        print(f"{t.id}: {t.transaction_type} ${t.amount} - {t.reference}")
