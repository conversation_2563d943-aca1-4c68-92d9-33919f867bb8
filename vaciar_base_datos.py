#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Script para vaciar la base de datos manteniendo la estructura y el usuario administrador
"""

import sqlite3
import os
from datetime import datetime

def main():
    # Verificar que la base de datos existe
    if not os.path.exists('diamond_sales.db'):
        print("Error: No se encontró la base de datos 'diamond_sales.db'")
        return False

    # Crear una copia de seguridad antes de vaciar la base de datos
    backup_filename = f"diamond_sales_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.db"
    try:
        import shutil
        shutil.copy2('diamond_sales.db', backup_filename)
        print(f"Se ha creado una copia de seguridad en: {backup_filename}")
    except Exception as e:
        print(f"Error al crear copia de seguridad: {str(e)}")
        return False

    # Conectar a la base de datos
    conn = sqlite3.connect('diamond_sales.db')
    cursor = conn.cursor()

    try:
        # Obtener la lista de tablas
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'")
        tables = [table[0] for table in cursor.fetchall()]

        # Desactivar las restricciones de clave foránea temporalmente
        cursor.execute("PRAGMA foreign_keys = OFF")

        # Iniciar una transacción
        cursor.execute("BEGIN TRANSACTION")

        # Tablas que no se deben vaciar completamente
        preserve_tables = ['users', 'settings', 'activation', 'permissions', 'role_permissions']

        # Vaciar cada tabla excepto las preservadas
        for table in tables:
            if table in preserve_tables:
                if table == 'users':
                    # Mantener solo el usuario admin
                    cursor.execute("DELETE FROM users WHERE username != 'admin'")
                    print(f"Se ha preservado el usuario admin en la tabla {table}")
                elif table == 'settings':
                    # Mantener la configuración pero reiniciar algunos valores
                    cursor.execute("UPDATE settings SET backup_path = NULL, last_backup_date = NULL")
                    print(f"Se ha preservado la configuración en la tabla {table}")
                elif table in ['permissions', 'role_permissions', 'activation']:
                    # Mantener estos datos intactos
                    print(f"Se ha preservado la tabla {table}")
            else:
                # Vaciar completamente la tabla
                cursor.execute(f"DELETE FROM {table}")
                print(f"Se han eliminado todos los registros de la tabla {table}")

        # Reiniciar los contadores de autoincremento
        for table in tables:
            if table not in preserve_tables or (table == 'users' and table != 'settings'):
                try:
                    cursor.execute(f"DELETE FROM sqlite_sequence WHERE name='{table}'")
                except:
                    pass

        # Confirmar los cambios
        cursor.execute("COMMIT")

        # Reactivar las restricciones de clave foránea
        cursor.execute("PRAGMA foreign_keys = ON")

        # Optimizar la base de datos
        cursor.execute("VACUUM")

        print("\nLa base de datos ha sido vaciada exitosamente manteniendo la estructura y el usuario admin.")
        print(f"Se ha creado una copia de seguridad en: {backup_filename}")

    except Exception as e:
        # En caso de error, revertir los cambios
        cursor.execute("ROLLBACK")
        print(f"Error al vaciar la base de datos: {str(e)}")
        return False
    finally:
        # Cerrar la conexión
        conn.close()

    return True

if __name__ == "__main__":
    # Ejecutar sin solicitar confirmación
    print("¡ADVERTENCIA! Este script eliminará TODOS los datos de la base de datos excepto el usuario admin.")
    print("Se creará una copia de seguridad antes de proceder.")

    if main():
        print("\nOperación completada con éxito.")
    else:
        print("\nLa operación falló. No se han eliminado los datos.")
