#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكريبت لحذف جميع البيانات من جدول الأرصدة الافتتاحية
"""

from database import OpeningBalance
from db_session import session_scope
from logger import log_info, log_error

def clear_opening_balances():
    """حذف جميع الأرصدة الافتتاحية من قاعدة البيانات"""
    try:
        with session_scope() as session:
            # حذف جميع السجلات
            deleted_count = session.query(OpeningBalance).count()
            session.query(OpeningBalance).delete()
            session.commit()
            
            print(f"تم حذف {deleted_count} رصيد افتتاحي بنجاح")
            log_info(f"تم حذف {deleted_count} رصيد افتتاحي من قاعدة البيانات")
            
    except Exception as e:
        print(f"خطأ في حذف الأرصدة الافتتاحية: {str(e)}")
        log_error(f"خطأ في حذف الأرصدة الافتتاحية: {str(e)}", e)

if __name__ == "__main__":
    clear_opening_balances()