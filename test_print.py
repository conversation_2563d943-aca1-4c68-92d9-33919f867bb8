#!/usr/bin/env python
# -*- coding: utf-8 -*-

import sys
import os
from PyQt6.QtWidgets import QApplication
from cash_box_screen import شاشة_صندوق_النقدية
from db_session import session_scope

class TestUser:
    def __init__(self):
        self.id = 1
        self.username = "admin"
        self.role = "admin"

def test_print_function():
    app = QApplication(sys.argv)
    
    # إنشاء مستخدم وهمي
    user = TestUser()
    
    # إنشاء شاشة صندوق النقدية
    cash_screen = شاشة_صندوق_النقدية(user)
    
    # تحميل البيانات أولاً
    cash_screen.load_data()
    
    print(f"عدد الصفوف في الجدول: {cash_screen.transactions_table.rowCount()}")
    
    # اختبار دالة إنشاء HTML
    try:
        html_content = cash_screen.generate_cash_report_html()
        print(f"تم إنشاء محتوى HTML بنجاح. الطول: {len(html_content)} حرف")
        
        # حفظ المحتوى في ملف للاختبار
        test_file = "test_cash_report.html"
        with open(test_file, 'w', encoding='utf-8') as file:
            file.write(html_content)
        print(f"تم حفظ الملف: {test_file}")
        
        # اختبار دالة الطباعة
        cash_screen.print_report()
        print("تم تشغيل دالة الطباعة بنجاح")
        
    except Exception as e:
        print(f"خطأ في الاختبار: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_print_function()
