import sqlite3
import os
import datetime
import shutil

# Crear una copia de seguridad de la base de datos actual
def backup_database():
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_dir = "backups"
    
    # Crear directorio de copias de seguridad si no existe
    if not os.path.exists(backup_dir):
        os.makedirs(backup_dir)
    
    backup_filename = os.path.join(backup_dir, f"diamond_sales_backup_{timestamp}.db")
    
    # Verificar si el archivo original existe
    if not os.path.exists('diamond_sales.db'):
        print("خطأ: لم يتم العثور على قاعدة البيانات الأصلية.")
        return False
    
    # Copiar el archivo
    try:
        shutil.copy2('diamond_sales.db', backup_filename)
        print(f"تم إنشاء نسخة احتياطية: {backup_filename}")
        return True
    except Exception as e:
        print(f"خطأ في إنشاء نسخة احتياطية: {str(e)}")
        return False

# Vaciar las tablas de la base de datos
def reset_database():
    # Tablas que se mantendrán con datos
    keep_tables = ['users', 'company_info']
    
    # Conectar a la base de datos
    conn = sqlite3.connect('diamond_sales.db')
    cursor = conn.cursor()
    
    # Obtener la lista de tablas
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
    tables = cursor.fetchall()
    
    # Desactivar las restricciones de clave foránea temporalmente
    cursor.execute("PRAGMA foreign_keys = OFF")
    
    # Iniciar una transacción
    conn.execute("BEGIN TRANSACTION")
    
    try:
        # Vaciar cada tabla excepto las que están en keep_tables
        for table in tables:
            table_name = table[0]
            
            if table_name in keep_tables or table_name.startswith('sqlite_'):
                print(f"الاحتفاظ بجدول: {table_name}")
                continue
            
            # Vaciar completamente la tabla
            print(f"تفريغ جدول: {table_name}")
            cursor.execute(f"DELETE FROM {table_name}")
            
            # Reiniciar el contador de autoincremento si existe
            try:
                cursor.execute(f"DELETE FROM sqlite_sequence WHERE name='{table_name}'")
            except:
                pass
        
        # Actualizar la tasa de cambio a un valor predeterminado
        try:
            cursor.execute("UPDATE settings SET value = '3.75' WHERE key = 'exchange_rate'")
            print("تم إعادة تعيين سعر الصرف إلى القيمة الافتراضية (3.75)")
        except:
            print("تعذر إعادة تعيين سعر الصرف")
        
        # Confirmar los cambios
        conn.commit()
        print("تم إعادة تعيين قاعدة البيانات بنجاح.")
    except Exception as e:
        # Revertir los cambios en caso de error
        conn.rollback()
        print(f"خطأ في إعادة تعيين قاعدة البيانات: {str(e)}")
    finally:
        # Reactivar las restricciones de clave foránea
        cursor.execute("PRAGMA foreign_keys = ON")
        # Cerrar la conexión
        conn.close()

# Limpiar archivos temporales y logs
def clean_temp_files():
    try:
        # Limpiar logs de errores pero mantener el archivo
        if os.path.exists('error_log.txt'):
            with open('error_log.txt', 'w', encoding='utf-8') as f:
                f.write(f"# تم إعادة تعيين السجل في {datetime.datetime.now()}\n")
            print("تم تنظيف ملف سجل الأخطاء.")
        
    except Exception as e:
        print(f"خطأ في تنظيف الملفات المؤقتة: {str(e)}")

# Función principal
def main():
    print("=== أداة إعادة تعيين نظام إدارة مبيعات الألماس ===")
    print("تحذير: ستؤدي هذه العملية إلى حذف جميع البيانات باستثناء المستخدمين ومعلومات الشركة.")
    print("سيتم إنشاء نسخة احتياطية قبل المتابعة.")
    
    confirmation = input("هل أنت متأكد من أنك تريد المتابعة؟ (ن/لا): ")
    
    if confirmation.lower() not in ['ن', 'نعم', 'y', 'yes']:
        print("تم إلغاء العملية.")
        return
    
    # Crear copia de seguridad
    if backup_database():
        # Reiniciar la base de datos
        reset_database()
        # Limpiar archivos temporales
        clean_temp_files()
        print("\nتم إعادة تعيين النظام بنجاح.")
        print("يمكنك الآن بدء البرنامج ببيانات جديدة.")
    else:
        print("تعذر إنشاء نسخة احتياطية. تم إلغاء العملية.")

if __name__ == "__main__":
    main()
