# Integration Plan: Cash Box with Sales and Purchases

## Current Status
- Cash Box system has been implemented
- Cash Box screen created
- Dashboard button added
- Permissions added

## Next Steps for Integration with Sales and Purchases

### Sales Integration
1. Modify the `save_sale` method in `sales_screen.py` to update the cash box when a sale is made
2. For cash sales, add a deposit transaction to the cash box
3. Add a reference to the sale ID in the cash transaction

### Purchase Integration
1. Modify the `save_purchase` method in `purchases_screen.py` to update the cash box when a purchase is made
2. For cash purchases, add a withdrawal transaction from the cash box
3. Add a reference to the purchase ID in the cash transaction

### Code Examples for Integration

#### Sales Integration
```python
# Inside save_sale method after saving the sale
if payment_method == "cash":
    # Create cash box transaction
    with session_scope() as session:        cash_box = session.query(صندوق_النقدية).first()
        if not cash_box:
            cash_box = صندوق_النقدية(balance=0.0, last_updated=datetime.now())
            session.add(cash_box)
            
        # Update cash box balance
        new_balance = cash_box.balance + sale_amount
        cash_box.balance = new_balance
        cash_box.last_updated = datetime.now()
        
        # Add transaction record
        transaction = حركة_نقدية(
            cash_box_id=cash_box.id,
            transaction_type="deposit",
            amount=sale_amount,
            balance_after=new_balance,
            transaction_date=datetime.now(),
            reference=f"Sale #{sale.id}",
            description=f"Cash sale to {customer_name}",
            created_by=current_user.id,
            created_at=datetime.now()
        )
        session.add(transaction)
        session.commit()
```

#### Purchases Integration
```python
# Inside save_purchase method after saving the purchase
if payment_method == "cash":
    # Create cash box transaction
    with session_scope() as session:        cash_box = session.query(صندوق_النقدية).first()
        if not cash_box:
            cash_box = صندوق_النقدية(balance=0.0, last_updated=datetime.now())
            session.add(cash_box)
            
        # Check if enough balance
        if cash_box.balance < purchase_amount:
            QMessageBox.warning(self, "تنبيه", "الرصيد في الصندوق غير كافٍ لإتمام عملية الشراء")
            return False
            
        # Update cash box balance
        new_balance = cash_box.balance - purchase_amount
        cash_box.balance = new_balance
        cash_box.last_updated = datetime.now()
        
        # Add transaction record
        transaction = حركة_نقدية(
            cash_box_id=cash_box.id,
            transaction_type="withdraw",
            amount=purchase_amount,
            balance_after=new_balance,
            transaction_date=datetime.now(),
            reference=f"Purchase #{purchase.id}",
            description=f"Cash purchase from {supplier_name}",
            created_by=current_user.id,
            created_at=datetime.now()
        )
        session.add(transaction)
        session.commit()
```

## Testing Plan
1. Test adding deposits and withdrawals directly from the Cash Box screen
2. Test permissions for different user roles
3. Test the reports generation and filtering
4. Test integration with sales and purchases when implemented

## Future Enhancements
1. Add daily closing reports for the cash box
2. Add reconciliation functionality
3. Create detailed reports with charts
4. Add audit trail for all cash transactions
