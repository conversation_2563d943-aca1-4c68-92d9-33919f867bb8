#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
سكريبت لبناء النسخة التنفيذية من برنامج Diamond Sales
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def main():
    print("=" * 60)
    print("🔨 بناء النسخة التنفيذية لبرنامج Diamond Sales")
    print("=" * 60)
    
    # التأكد من أننا في المجلد الصحيح
    script_dir = Path(__file__).parent
    os.chdir(script_dir)
    
    print(f"📁 المجلد الحالي: {os.getcwd()}")
    
    # التحقق من وجود البيئة الافتراضية
    venv_path = Path(".venv")
    if not venv_path.exists():
        print("❌ البيئة الافتراضية غير موجودة")
        print("💡 يرجى إنشاء البيئة الافتراضية أولاً")
        return False
    
    # التحقق من وجود PyInstaller
    pyinstaller_path = venv_path / "Scripts" / "pyinstaller.exe"
    if not pyinstaller_path.exists():
        print("❌ PyInstaller غير مثبت")
        print("💡 يرجى تثبيت PyInstaller في البيئة الافتراضية")
        return False
    
    # التحقق من وجود ملف spec
    spec_file = "diamond_sales.spec"
    if not Path(spec_file).exists():
        print(f"❌ ملف {spec_file} غير موجود")
        return False
    
    print("✅ جميع المتطلبات متوفرة")
    
    # حذف المجلد السابق إن وجد
    dist_path = Path("dist") / "DiamondSales"
    if dist_path.exists():
        print("🗑️ حذف النسخة السابقة...")
        shutil.rmtree(dist_path)
    
    # تشغيل PyInstaller
    print("🔨 بدء عملية البناء...")
    try:
        cmd = [str(pyinstaller_path), spec_file]
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        
        print("✅ تم بناء النسخة التنفيذية بنجاح!")
        print(f"📁 الملف التنفيذي موجود في: {dist_path}")
        
        # التحقق من وجود الملف التنفيذي
        exe_file = dist_path / "DiamondSales.exe"
        if exe_file.exists():
            print(f"✅ الملف التنفيذي: {exe_file}")
            print(f"📊 حجم الملف: {exe_file.stat().st_size / (1024*1024):.1f} MB")
        else:
            print("⚠️ لم يتم العثور على الملف التنفيذي")
        
        return True
        
    except subprocess.CalledProcessError as e:
        print("❌ حدث خطأ أثناء البناء:")
        print(f"الخطأ: {e}")
        if e.stdout:
            print(f"المخرجات: {e.stdout}")
        if e.stderr:
            print(f"الأخطاء: {e.stderr}")
        return False
    
    except Exception as e:
        print(f"❌ خطأ غير متوقع: {e}")
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("\n🎉 تم الانتهاء بنجاح!")
    else:
        print("\n💥 فشل في البناء")
    
    input("\nاضغط Enter للمتابعة...")